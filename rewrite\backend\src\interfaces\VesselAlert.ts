import mongoose from "mongoose";

export interface IVesselAlert {
    _id: mongoose.Types.ObjectId;
    unit_id: string;
    vessel_id: mongoose.Types.ObjectId;
    emergency_button: boolean;
    location: {
        type: string;
        coordinates: number[];
    };
    gps_valid: boolean;
    severe_weather_alerts_type: string[];
    severe_weather_alerts_description: string[];
    geo_hazard_alerts_type: string[];
    geo_hazard_alerts_description: string[];
    visual_alerts_type: string[];
    visual_alerts_description: string[];
    max_alert_severity: number;
    emergency_type: number;
    emergency_description: string;
    reported_at: Date;
}

// const vesselAlertsSchema = new mongoose.Schema({
//     unit_id: {
//         required: true,
//         unique: true,
//     },
//     vessel_id: {
//         type: mongoose.Schema.Types.ObjectId,
//         required: true,
//     },
//     emergency_button: {
//         type: Boolean,
//         required: true,
//     },
//     location: {
//         type: {
//             type: String,
//             enum: ["Point"],
//             default: "Point",
//         },
//         coordinates: {
//             type: [Number],
//             required: true,
//         },
//     },
//     gps_valid: {
//         type: Boolean,
//         required: true,
//     },
//     severe_weather_alerts_type: {
//         type: [String],
//         required: true,
//     },
//     severe_weather_alerts_description: {
//         type: [String],
//         required: true,
//     },
//     geo_hazard_alerts_type: {
//         type: [String],
//         required: true,
//     },
//     geo_hazard_alerts_description: {
//         type: [String],
//         required: true,
//     },
//     visual_alerts_type: {
//         type: [String],
//         required: true,
//     },
//     visual_alerts_description: {
//         type: [String],
//         required: true,
//     },
//     max_alert_severity: {
//         type: Number,
//         required: true,
//     },
//     emergency_type: {
//         type: Number,
//         required: true,
//     },
//     emergency_description: {
//         type: String,
//         required: true,
//     },
//     creation_timestamp: {
//         type: Date,
//         required: true,
//         default: () => new Date().toISOString(),
//     },
// });
