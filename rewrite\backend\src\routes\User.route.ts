import express, { Request, Response } from "express";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import crypto from "crypto";
import User from "../models/User";
import { getUser } from "../queries/User";
import mongoose, { isValidObjectId } from "mongoose";
import { body, param, query } from "express-validator";
import { isIntStrict, validateError, generateInvitationLink, escapeRegExp } from "../utils/functions";
import Role from "../models/Role";
import { validateData } from "../middlewares/validator";
import hasPermission from "../middlewares/hasPermission";
import { permissions } from "../utils/permissions";
import isAuthenticated from "../middlewares/auth";
import { sendEmail } from "../modules/email";
import { sendOtp, verifyOtp } from "../modules/otpService";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import ApiKey from "../models/ApiKey";
import { INVITE_EMAIL_CONTENT, WELCOME_EMAIL_CONTENT, FORGET_PASSWORD_EMAIL_CONTENT } from "../utils/Email";
import Organization from "../models/Organization";
import InviteToken from "../models/InviteToken";
import Vessel from "../models/Vessel";
import { IJwtPayload } from "../interfaces/User";

const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/suggest",
    assignEndpointId.bind(this, endpointIds.SUGGEST_USERS),
    isAuthenticated,
    validateData.bind(this, [query("q").isString().trim().notEmpty()]),
    async (req: Request, res: Response) => {
        try {
            const q = String(req.query.q || "").trim();
            const regex = new RegExp(q.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"), "i");
            const baseMatch: any = {
                is_deleted: false,
                organization_id: req.user.organization_id,
                $or: [{ name: { $regex: regex } }, { email: { $regex: regex } }, { username: { $regex: regex } }],
            };
            const users = await User.find(baseMatch).limit(10).select({ name: 1, email: 1, username: 1 }).lean();
            res.json(users);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_USERS_LIST),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageUsers]),
    async (req: Request, res: Response) => {
        try {
            let page = parseInt(req.query.page as string) || 1;
            let limit = parseInt(req.query.limit as string) || 10;

            if (page < 1) page = 1;
            if (limit < 1) limit = 10;

            const query: Record<string, boolean | string | mongoose.Types.ObjectId> = {
                is_deleted: false,
            };
            if (!req.user.organization.is_internal) {
                query.organization_id = req.user.organization._id;
            }
            const skip = (page - 1) * limit;
            const totalCount = await User.countDocuments(query);
            const totalPages = Math.ceil(totalCount / limit);

            const users = await User.aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: "organizations",
                        localField: "organization_id",
                        foreignField: "_id",
                        as: "organization",
                    },
                },
                {
                    $addFields: {
                        organization: { $arrayElemAt: ["$organization", 0] },
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        localField: "created_by",
                        foreignField: "_id",
                        as: "invited_by",
                        pipeline: [
                            {
                                $project: {
                                    name: 1,
                                    username: 1,
                                },
                            },
                        ],
                    },
                },
                {
                    $addFields: {
                        invited_by: { $arrayElemAt: ["$invited_by", 0] },
                    },
                },
                {
                    $project: {
                        password: 0,
                        jwt_tokens: 0,
                        reset_password_token: 0,
                        reset_password_expire: 0,
                    },
                },
                { $skip: skip },
                { $limit: limit },
            ]);

            res.json({ users, totalCount, totalPages });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/invite",
    assignEndpointId.bind(this, endpointIds.INVITE_USER),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageUsers]),
    validateData.bind(this, [
        body("email")
            .isEmail()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("role_id")
            .custom(isIntStrict)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("organization_id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("allowed_vessels")
            .isArray({ min: 1 })
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("allowed_vessels.*")
            .custom(isValidObjectId)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { email, role_id, allowed_vessels, organization_id } = req.body;

            const normalizedEmail = email.trim().toLowerCase();

            let hasManageOrgPermission = false;
            if (req.user && req.user.permissions) {
                hasManageOrgPermission = req.user.permissions.some((p) => p.permission_id === permissions.manageOrganizations);
            }

            if (!hasManageOrgPermission && req.user.organization_id && req.user.organization_id.toString() !== organization_id.toString()) {
                return res.status(403).json({ message: "Forbidden: You can only invite users to your own organization" });
            }

            const existingUser = await User.findOne({ email: { $regex: new RegExp(`^${escapeRegExp(normalizedEmail)}$`, "i") } });
            if (existingUser) return res.status(400).json({ message: "User already exists with this email" });
            const roleDetails = await Role.findOne({ role_id });
            if (!roleDetails) return res.status(400).json({ message: "Role does not exist with this ID" });
            const reqUserRole = await Role.findOne({ role_id: req.user.role_id });
            if (!reqUserRole) return res.status(404).json({ message: "Your own role not found" });
            // Check hierarchy: user can only invite roles with higher hierarchy_number (lower privilege)
            if (reqUserRole.hierarchy_number > roleDetails.hierarchy_number) {
                return res.status(403).json({ message: "Not allowed to invite users with role above your hierarchy level" });
            }
            const organizationDetails = await Organization.findOne({ _id: organization_id });
            if (!organizationDetails) return res.status(400).json({ message: "Organization does not exist with this ID" });
            if (organizationDetails.is_miscellaneous && !roleDetails.denied_permissions.includes(permissions.manageUsers)) {
                return res
                    .status(403)
                    .json({ message: "Miscellaneous organization user are not allowed to assign a role which have manage user permission" });
            }
            const role = roleDetails.role_name;
            const organization = organizationDetails.name;
            const token = generateInvitationLink(
                normalizedEmail,
                role_id.toString(),
                allowed_vessels,
                String(req.user._id),
                String(role),
                String(organization),
                String(organization_id),
            );
            const date = new Date();
            const shortToken = Buffer.from(crypto.randomBytes(16)).toString("hex");
            const inviteToken = new InviteToken({
                token: token,
                invited_by: req.user._id,
                email: normalizedEmail,
                role_id,
                role,
                organization_id,
                allowed_vessels,
                short_token: shortToken,
            });
            await inviteToken.save();
            const link = `${process.env.API_URL}/users/verify-invite?token=${encodeURIComponent(inviteToken.short_token)}`;
            const content = INVITE_EMAIL_CONTENT(link, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);

            await sendEmail({
                to: normalizedEmail,
                subject: "Account Creation Invitation",
                html: content,
            });

            res.status(200).json({ link });
        } catch (error) {
            validateError(error, res);
        }
    },
);

router.get(
    "/verify-invite",
    assignEndpointId.bind(this, endpointIds.VERIFY_INVITE_USER),
    validateData.bind(this, [
        query("token")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        const { token } = req.query;
        try {
            const inviteToken = await InviteToken.findOne({ short_token: token, is_used: false, is_deleted: false });
            if (!inviteToken) return res.status(400).json({ message: "Invalid token" });
            const secretKey = process.env.JWT_SECRET;
            if (!secretKey) throw new Error("JWT_SECRET not configured");
            const decoded = jwt.verify(inviteToken.token, secretKey) as IJwtPayload;
            const { email, role_id, role, organization_id, organization_name } = decoded;
            return res.redirect(
                `${process.env.APP_URL}/signup?token=${inviteToken.short_token}&role=${role}&email=${encodeURIComponent(email)}&role_id=${role_id}&organization_id=${organization_id}&organization_name=${organization_name}`,
            );
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_USER),
    validateData.bind(this, [
        body("name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("username")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("email")
            .isString()
            .isEmail()
            .withMessage((_value, { path }) => `Invalid value '${_value}' provided for field '${path}'`),
        body("password")
            .isString()
            .notEmpty()
            .withMessage((_value, { path }) => `Invalid value '${_value}' provided for field '${path}'`)
            .bail()
            .isLength({ min: 8 })
            .withMessage("Password must be at least 8 characters long"),
        body("organization_id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("role_id")
            .custom(isIntStrict)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("token")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { name, username, email, password, role_id, organization_id, token } = req.body;

            const normalizedEmail = email.trim().toLowerCase();

            const inviteToken = await InviteToken.findOne({ short_token: token, is_deleted: false, is_used: false });
            if (!inviteToken) return res.status(400).json({ message: "Invalid token" });
            const secretKey = process.env.JWT_SECRET;
            if (!secretKey) throw new Error("JWT_SECRET not configured");
            const decoded = jwt.verify(inviteToken.token, secretKey) as IJwtPayload;
            if (normalizedEmail !== decoded.email.trim().toLowerCase())
                return res.status(400).json({ message: "Email is not associated with this link" });
            if (role_id != decoded.role_id) return res.status(400).json({ message: "Role_ID is not associated with this link" });
            if (organization_id != decoded.organization_id)
                return res.status(400).json({ message: "Organization_ID is not associated with this link" });
            // const { email, role_id, admin_id, role } = decoded;
            const escapedUsername = escapeRegExp(username);
            const escapedEmail = escapeRegExp(normalizedEmail);

            if (await User.findOne({ username: { $regex: new RegExp(`^${escapedUsername}$`, "i") } })) {
                return res.status(400).json({ message: "Username is already taken" });
            }

            if (await User.findOne({ email: { $regex: new RegExp(`^${escapedEmail}$`, "i") } })) {
                return res.status(400).json({ message: "Email is already taken" });
            }

            const role = await Role.findOne({ role_id });
            if (!role) return res.status(404).json({ message: "Role does not exist" });
            const organization = await Organization.findOne({ _id: organization_id });
            if (!organization) return res.status(404).json({ message: "Organization does not exist" });
            const hashedPassword = await bcrypt.hash(password, 10);
            await User.create({
                name,
                username,
                email: normalizedEmail,
                password: hashedPassword,
                role_id,
                organization_id,
                allowed_vessels: decoded.allowed_vessels || [],
                created_by: inviteToken.invited_by,
            });
            inviteToken.is_used = true;
            await inviteToken.save();
            const date = new Date();
            const content = WELCOME_EMAIL_CONTENT(name, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);

            await sendEmail({
                to: normalizedEmail,
                subject: "Welcome aboard Quartermaster!",
                html: content,
            });

            res.status(201).json({ message: "User created successfully" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/forgot-password",
    assignEndpointId.bind(this, endpointIds.FETCH_PASSWORD_RESET_TOKEN),
    validateData.bind(this, [
        body("email")
            .isString()
            .isEmail()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { email } = req.body;

            const user = await User.findOne({ email: { $regex: new RegExp(`^${email}$`, "i") } });
            if (!user) return res.status(404).json({ message: "No user found with that email address" });

            if (user.reset_password_token && user.reset_password_expire && user.reset_password_expire > Date.now())
                return res.status(400).json({ message: "A reset token already exists and is still valid. Please check your email" });

            const resetToken = Buffer.from(crypto.randomBytes(32)).toString("hex");
            user.reset_password_token = crypto.createHash("sha256").update(resetToken).digest("hex");
            user.reset_password_expire = Date.now() + 10 * 60 * 1000; // Token valid for 10 minutes

            await user.save();
            const link = `${process.env.APP_URL}/reset-password/${resetToken}`;
            const date = new Date();
            const content = FORGET_PASSWORD_EMAIL_CONTENT(link, `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`);
            await sendEmail({
                to: email,
                subject: "Password Reset Request",
                html: content,
            });

            res.json({ message: "If an account with this email exists, you will receive a password reset link shortly" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/reset-password/:token",
    assignEndpointId.bind(this, endpointIds.UPDATE_PASSWORD),
    validateData.bind(this, [
        param("token")
            .isString()
            .notEmpty()
            .isLength({ min: 64, max: 64 })
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("password")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .bail()
            .isLength({ min: 8 })
            .withMessage("Password must be at least 8 characters long"),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { token } = req.params;
            const { password } = req.body;

            const hashedToken = crypto.createHash("sha256").update(token).digest("hex");
            const user = await User.findOne({
                reset_password_token: hashedToken,
                reset_password_expire: { $gt: Date.now() },
            });

            if (!user) {
                return res.status(400).json({ message: "Invalid or expired token" });
            }

            user.password = await bcrypt.hash(password, 10);
            user.reset_password_token = null;
            user.reset_password_expire = null;

            await user.save();

            res.json({ message: "Password has been reset successfully" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get("/user", assignEndpointId.bind(this, endpointIds.FETCH_USER), async (req: Request, res: Response) => {
    try {
        const authHeader = req.header("authorization");
        if (authHeader && authHeader.startsWith("Bearer ")) {
            const jwt_token = authHeader.split("Bearer ")[1];
            if (!process.env.JWT_SECRET) return res.status(500).json({ message: "Internal server error" });
            const { user_id } = jwt.verify(jwt_token, process.env.JWT_SECRET) as { user_id: string };
            if (!user_id) return res.status(400).json({ message: "Unsupported authentication for this endpoint" });

            const user = await getUser({ user_id });
            if (!user) return res.status(404).json({ message: "User does not exist" });

            res.json(user);
        } else {
            return res.status(401).json({ message: "Unauthorized" });
        }
    } catch (err) {
        validateError(err, res);
    }
});

router.get("/auth", assignEndpointId.bind(this, endpointIds.FETCH_TOKEN), async (req: Request, res: Response) => {
    const logRequestDetails = (req: Request) => {
        const ip = req.headers["x-forwarded-for"] || req.connection.remoteAddress;
        const time = new Date().toISOString();
        const origin = req.headers.origin || "Unknown";
        const endpoint = req.originalUrl || req.url;
        const method = req.method;
        const userAgent = req.headers["user-agent"];
        const deviceIdFromCookie = req.cookies?.deviceId;
        const deviceIdFromHeader = req.headers["device-id"];
        const authHeader = req.headers["authorization"];
        const apiKeyHeader = req.headers["qm-api-key"];
        const referrer = req.headers["referer"];
        const host = req.headers["host"];

        console.log(`\n🛰️ [Request Info - ${time}]`);
        console.log(`🔹 IP: ${ip}`);
        console.log(`🔹 Method: ${method}`);
        console.log(`🔹 Endpoint: ${endpoint}`);
        console.log(`🔹 Origin: ${origin}`);
        console.log(`🔹 Referrer: ${referrer}`);
        console.log(`🔹 Host: ${host}`);
        console.log(`🔹 User-Agent: ${userAgent}`);
        console.log(`🔹 Authorization: ${authHeader || "Not provided"}`);
        console.log(`🔹 API Key: ${apiKeyHeader || "Not provided"}`);
        console.log(`🔹 Device ID (Cookie): ${deviceIdFromCookie || "Not provided"}`);
        console.log(`🔹 Device ID (Header): ${deviceIdFromHeader || "Not provided"}`);
        console.log(`🔹 All Cookies:`, req.cookies);
        console.log("------------------------------------------------------------\n");
    };

    logRequestDetails(req);
    try {
        if (req.header("authorization")) {
            const authHeader = req.header("authorization");
            console.log("authHeader is", authHeader);
            if (!authHeader || !authHeader.startsWith("Basic ")) return res.status(401).json({ message: "Authorization header must be Basic" });

            const base64Credentials = authHeader.split("Basic ")[1];
            const credentials = Buffer.from(base64Credentials, "base64").toString("utf-8");
            const [username, password] = credentials.split(":");
            if (!username || !password) return res.status(400).json({ message: "Username and password is requried" });
            const escapedUsername = escapeRegExp(username);
            const user = await User.findOne({
                $or: [
                    {
                        username: { $regex: new RegExp(`^${escapedUsername}$`, "i") },
                    },
                    {
                        email: { $regex: new RegExp(`^${escapedUsername}$`, "i") },
                    },
                ],
            });

            if (!user) return res.status(400).json({ message: "Invalid credentials" });

            const isMatch = await bcrypt.compare(password, user.password);
            if (!isMatch) return res.status(400).json({ message: "Invalid credentials" });

            const deviceId = req.cookies.deviceId;
            if (user.email_verification_enabled && !user.email_verified_device_ids.includes(deviceId)) {
                return res.status(302).json({
                    message: "Email verification required",
                });
            }

            const token_expiry = new Date(new Date().getTime() + 86400000).toISOString();
            if (!process.env.JWT_SECRET) return res.status(500).json({ message: "Internal server error" });
            const jwt_token = jwt.sign({ user_id: user._id }, process.env.JWT_SECRET, { expiresIn: "7d" });

            if (user.jwt_tokens.length >= 10) {
                user.jwt_tokens.shift();
            }
            user.jwt_tokens.push(jwt_token);

            await user.save();

            res.json({ jwt_token, expires: token_expiry });
        } else if (req.header("qm-api-key")) {
            const api_key = req.header("qm-api-key");

            const apiKey = await ApiKey.findOne({ api_key });
            if (!apiKey) return res.status(401).json({ message: "API key is invalid" });
            if (apiKey.is_deleted) return res.status(400).json({ message: "Your API key has been removed. Please contact an administrator" });
            if (apiKey.is_revoked) return res.status(400).json({ message: "Your access has been revoked. Please contact an administrator" });

            const token_expiry = new Date(new Date().getTime() + 86400000).toISOString();
            if (!process.env.JWT_SECRET) return res.status(500).json({ message: "Internal server error" });
            const jwt_token = jwt.sign({ api_key_id: apiKey._id }, process.env.JWT_SECRET, { expiresIn: "24h" });

            apiKey.jwt_token = jwt_token;
            await apiKey.save();

            res.json({ jwt_token, expires: token_expiry });
        } else {
            return res.status(401).json({ message: "Auth is required" });
        }
    } catch (err) {
        validateError(err, res);
    }
});

router.post(
    "/sendEmailOTP",
    assignEndpointId.bind(this, endpointIds.SEND_OTP),
    validateData.bind(this, [
        body("username")
            .isString()
            .notEmpty()
            .withMessage((_value, { path }) => `Invalid value '${_value}' provided for field '${path}'`)
            .optional(),
    ]),
    async (req: Request, res: Response) => {
        const { username } = req.body;
        if (!username) return res.status(400).json({ message: "Username is required" });
        const user = await User.findOne({
            $or: [
                {
                    username: { $regex: new RegExp(`^${username}$`, "i") },
                },
                {
                    email: { $regex: new RegExp(`^${username}$`, "i") },
                },
            ],
        });

        if (!user) return res.status(400).json({ message: "Invalid credentials" });

        const email = user.email;
        if (!email) return res.status(400).json({ message: "Email is required" });

        try {
            const result = await sendOtp(email, user.name, sendEmail);
            res.status(200).json({ ...result, email });
        } catch (err) {
            res.status(500).json({ message: "Failed to send OTP" });
            console.error("Error sending OTP email:", err);
        }
    },
);

router.post(
    "/emailOTPVerification",
    assignEndpointId.bind(this, endpointIds.VERIFY_OTP),
    validateData.bind(this, [
        body("username")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        body("otp")
            .notEmpty()
            .withMessage((_value, { path }) => `Field '${path}' cannot be empty`)
            .isInt({ min: 100000, max: 999999 })
            .withMessage((_value, { path }) => `Field '${path}' must be a 6-digit integer`),
    ]),
    async (req: Request, res: Response) => {
        const { username, otp } = req.body;
        if (!username) {
            return res.status(400).json({ message: "Username is required" });
        }

        const user = await User.findOne({
            $or: [{ username: { $regex: new RegExp(`^${username}$`, "i") } }, { email: { $regex: new RegExp(`^${username}$`, "i") } }],
        });

        if (!user) {
            return res.status(400).json({ message: "Invalid credentials" });
        }

        const email = user.email;

        if (!email) {
            return res.status(400).json({ message: "Email is required" });
        }

        const { valid, message } = await verifyOtp(email, otp);
        if (!valid) {
            return res.status(400).json({ message });
        }

        const deviceId = req.cookies.deviceId;
        if (deviceId && !user.email_verified_device_ids.includes(deviceId)) {
            user.email_verified_device_ids.push(deviceId);
            await user.save();
        }

        res.status(200).json({ message: "OTP Verified." });
    },
);

router.patch(
    "/userEmailVerification",
    isAuthenticated,
    validateData.bind(this, [
        query("email_verification_enabled")
            .isBoolean()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
    ]),
    async (req: Request, res: Response) => {
        try {
            const emailVerificationEnabled = req.query.email_verification_enabled === "true";
            const user = await User.findById(req.user._id);

            if (!user) return res.status(404).json({ message: "User does not exist" });
            if (emailVerificationEnabled && !user.email) {
                return res.status(400).json({ message: "Email is required" });
            }

            user.email_verification_enabled = emailVerificationEnabled;
            await user.save();

            res.status(200).json({ message: "User email verification status updated successfully" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/role",
    assignEndpointId.bind(this, endpointIds.UPDATE_USER_ROLE),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageUsers]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("role_id")
            .custom(isIntStrict)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            if (!req.user) return res.status(401).json({ message: "Unauthorized" });

            const { id: user_id } = req.params;
            const { role_id } = req.body;

            const user = await User.findOne({ _id: user_id });
            if (!user) return res.status(404).json({ message: "User does not exist" });

            const role = await Role.findOne({ role_id: role_id });
            if (!role) return res.status(404).json({ message: "Role does not exist" });

            if (req.user.role_id === user.role_id)
                return res.status(403).json({ message: "Not allowed to edit role for user having same role as you" });
            const reqUserRole = await Role.findOne({ role_id: req.user.role_id });
            const targetUserRole = await Role.findOne({ role_id });
            if (!reqUserRole || !targetUserRole) {
                return res.status(404).json({ message: "Role not found" });
            }
            if (reqUserRole.hierarchy_number >= targetUserRole.hierarchy_number) {
                return res.status(403).json({ message: "Not allowed to edit role for user having role above you" });
            }
            if (!req.user.organization.is_internal) {
                if (req.user.organization_id.toString() !== user.organization_id.toString()) {
                    return res.status(403).json({ message: "Not allowed to edit role of user from different organization" });
                }
            }

            const userOrganization = await Organization.findOne({ _id: user.organization_id });
            if (!userOrganization) return res.status(404).json({ message: "User organization does not exist" });

            if (userOrganization.is_miscellaneous && !role.denied_permissions.includes(permissions.manageUsers)) {
                return res
                    .status(403)
                    .json({ message: "Miscellaneous organization user are not allowed to assign a role which have manage user permission" });
            }

            user.role_id = role.role_id;

            await user.save();

            res.json({ message: "User role has been updated" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/updateSettings",
    assignEndpointId.bind(this, endpointIds.UPDATE_USER_SETTINGS),
    isAuthenticated,
    validateData.bind(this, [
        query("date_time_format")
            .isString()
            .optional()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        query("use_MGRS")
            .isBoolean()
            .optional()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        query("home_port_filter_mode")
            .isString()
            .optional()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const dateTimeFormat = req.query.date_time_format as string;
            const useMGRS = req.query.use_MGRS as string;
            const home_port_filter_mode = req.query.home_port_filter_mode as "ALL" | "ONLY_HOME_PORTS" | "ONLY_NON_HOME_PORTS";

            if (!dateTimeFormat && !useMGRS && !home_port_filter_mode)
                return res.status(400).json({ message: "Date time format or use MGRS or home_port_filter_mode is required" });

            const user = await User.findById(req.user._id);

            if (!user) return res.status(404).json({ message: "User does not exist" });

            if (dateTimeFormat) {
                user.date_time_format = dateTimeFormat;
            }

            if (useMGRS) {
                user.use_MGRS = useMGRS === "true";
            }

            if (home_port_filter_mode) {
                user.home_port_filter_mode = home_port_filter_mode;
            }

            await user.save();

            res.status(200).json({ message: "User settings updated successfully" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/allowedVessels",
    assignEndpointId.bind(this, endpointIds.UPDATE_USER_ALLOWED_VESSELS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageUsers]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("allowed_vessels")
            .isArray()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("allowed_vessels.*")
            .custom(isValidObjectId)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { id } = req.params;
            const { allowed_vessels } = req.body;

            const user = await User.findOne({ _id: id, is_deleted: false });

            if (!user) return res.status(404).json({ message: "User not found" });

            const inActiveVessels = await Vessel.find({ _id: { $in: allowed_vessels }, is_active: false });
            if (inActiveVessels.length > 0) {
                return res.status(400).json({ message: "Cannot assign inactive vessels to user" });
            }
            user.allowed_vessels = allowed_vessels;

            await user.save();

            res.json({ message: "User allowed vessels updated successfully" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/organization",
    assignEndpointId.bind(this, endpointIds.UPDATE_USER_ORGANIZATION),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageUsers, permissions.manageOrganizations]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("organization_id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            if (!req.user) return res.status(401).json({ message: "Unauthorized" });

            const { id: user_id } = req.params;
            const { organization_id } = req.body;

            const user = await User.findOne({ _id: user_id });
            if (!user) return res.status(404).json({ message: "User does not exist" });
            const userRole = await Role.findOne({ role_id: user.role_id });
            const reqUserRole = await Role.findOne({ role_id: req.user.role_id });

            const organization = await Organization.findOne({ _id: organization_id });
            if (!organization) return res.status(404).json({ message: "Organization does not exist" });

            if (req.user._id.toString() === user_id.toString()) {
                return res.status(403).json({ message: "Not allowed to edit your own organization" });
            }

            if (!reqUserRole || !userRole) {
                return res.status(404).json({ message: "Role not found" });
            }
            if (reqUserRole.hierarchy_number >= userRole.hierarchy_number) {
                return res.status(403).json({ message: "Not allowed to edit organization for user having role above you" });
            }

            if (organization.is_miscellaneous && !userRole.denied_permissions.includes(permissions.manageUsers)) {
                return res.status(403).json({ message: "Miscellaneous organization cannot be assign to user which have manage user permission" });
            }

            user.organization_id = organization._id;

            await user.save();

            res.json({ message: "User organization has been updated" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_USER),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageUsers]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .bail()
            .customSanitizer((v) => new mongoose.Types.ObjectId(v))
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req: Request, res: Response) => {
        try {
            const user = await User.findById(req.params.id);
            if (!user) return res.status(404).json({ message: `User does not exist` });

            if (!user.deletable) return res.status(400).json({ message: `User cannot be deleted` });

            user.username = crypto.randomUUID();
            user.email = `${crypto.randomUUID()}@random.com`;
            user.is_deleted = true;

            await user.save();

            return res.json({ message: `User has been deleted` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;

/**
 * @swagger
 * tags:
 *   - name: Auth
 *     description: Authentication endpoint
 * components:
 *   securitySchemes:
 *     basicAuth:
 *       type: http
 *       scheme: basic
 *     apiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: qm-api-key
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *   schemas:
 *     AuthToken:
 *       type: object
 *       properties:
 *         jwt_token:
 *           type: string
 *           description: The JWT token
 *           example: eyJhbGciOiJIUzI1Ni....eyJzdWIiOiIxMjM0NTY3ODk....SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJ...
 *         expires:
 *           type: string
 *           format: date-time
 *           description: Expiration date and time of the token
 *           example: '2023-09-25T10:20:30Z'
 *     User:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the user
 *           example: 67942a74a7f838634a00190a
 *         name:
 *           type: string
 *           description: The user's full name
 *           example: John Doe
 *         email:
 *           type: string
 *           description: The user's email address
 *           example: <EMAIL>
 *         username:
 *           type: string
 *           description: The unique username for the user
 *           example: johndoe123
 *         role_id:
 *           type: number
 *           description: The role ID of the user
 *           example: 1
 *         allowed_vessels:
 *           type: array
 *           description: The vessels that the user is allowed to access
 *           example: ["67942a74a7f838634a00190a","67942a74a7f838634a00190b"]
 *         email_verification_enabled:
 *           type: boolean
 *           description: Whether the user's email is verified
 *           example: false
 *         email_verified_device_ids:
 *           type: array
 *           description: The devices that the user has verified their email on
 *           items:
 *             type: string
 *             example: "762486b8-d22b-4813-b488-a4242017a47b"
 *         deletable:
 *           type: boolean
 *           description: Whether the user can be deleted
 *           example: false
 *         is_deleted:
 *           type: boolean
 *           description: Whether the user has been soft deleted
 *           example: false
 *         creation_timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the user was created
 *           example: '2023-09-25T10:20:30Z'
 */

/**
 * @swagger
 * /users/auth:
 *   get:
 *     summary: Fetch JWT token
 *     description: Rate limited to 15 requests every 5 seconds
 *     tags: [Auth]
 *     security:
 *       - basicAuth: []
 *       - apiKeyAuth: []
 *     responses:
 *       200:
 *         description: JWT token and expiration date
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               $ref: '#/components/schemas/AuthToken'
 *       400:
 *         description: Invalid credentials
 *       302:
 *         description: Email verification required
 *       500:
 *         description: Server error
 */
