import React, { useEffect, useMemo, useRef } from "react";
import { Box } from "@mui/material";
import { keyframes } from "@mui/system";

// Keyframes for the pulsing ripple animation
const ripple = keyframes`
  0% {
    transform: translate(-50%, -50%) scale(0.6);
    opacity: 0.9;
  }
  80% {
    opacity: 0;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.8);
    opacity: 0;
  }
`;

const EventPingDot = React.forwardRef(({ setAnchorEl, setPanelOpen,
    panelOpen,
    det_nbbox,
    imageDisplayRect,
    setQuadrant,
    containerRef,
    artifactIndex,
    onDotClick
}, ref) => {

    const cx = (det_nbbox?.x1 + det_nbbox?.x2) / 2;
    const cy = (det_nbbox?.y1 + det_nbbox?.y2) / 2;

    const dotPos = useMemo(() => {
        if (!imageDisplayRect) return null;
        const leftOffset = imageDisplayRect.x ?? imageDisplayRect.left ?? 0;
        const topOffset = imageDisplayRect.y ?? imageDisplayRect.top ?? 0;
        const w = imageDisplayRect.width ?? 0;
        const h = imageDisplayRect.height ?? 0;

        return {
            left: leftOffset + cx * w,
            top: topOffset + cy * h,
        };
    }, [imageDisplayRect, cx, cy]);

    const quadrant = React.useMemo(() => {
        if (!containerRef.current || !dotPos) return null;
        const midX = containerRef.current.clientWidth / 2;
        const midY = containerRef.current.clientHeight / 2;
        const isLeft = dotPos.left < midX;
        const isTop = dotPos.top < midY;
        if (isTop && isLeft) {
            console.log("Quadrant Clicked Dot: TL");
            return "TL";
        }
        if (isTop && !isLeft) {
            console.log("Quadrant Clicked Dot: TR");
            return "TR";
        }
        if (!isTop && isLeft) {
            console.log("Quadrant Clicked Dot: BL");
            return "BL";
        }
        console.log("Quadrant Clicked Dot: BR");
        return "BR";
    }, [dotPos, panelOpen]);

    useEffect(() => {
        setQuadrant(quadrant)
    }, [quadrant])

    return (
        <>
            {dotPos &&
                <Box
                    ref={ref}
                    onClick={(e) => {
                        e.stopPropagation();
                        setAnchorEl(e.currentTarget);
                        setPanelOpen((o) => !o);
                        // Update unified index when dot is clicked
                        onDotClick && typeof artifactIndex === 'number' && onDotClick(artifactIndex);
                    }}
                    display={{ xs: "none", sm: "none", md: "none", lg: "block" }}
                    sx={{
                        position: "absolute",
                        left: dotPos?.left,
                        top: dotPos?.top,
                        transform: "translate(-50%, -50%)",
                        width: 10,
                        height: 10,
                        borderRadius: "50%",
                        background: "white",
                        boxShadow: "0 0 0 2px white", // white ring around the dot
                        cursor: "pointer",
                        zIndex: 2,

                        // animated rings
                        "&::before, &::after": {
                            content: '""',
                            position: "absolute",
                            left: "50%",
                            top: "50%",
                            width: 28,
                            height: 28,
                            borderRadius: "50%",
                            border: "4px solid rgba(255,255,255,0.8)",
                            transform: "translate(-50%, -50%)",
                            animation: `${ripple} 1.6s ease-out infinite`,
                            pointerEvents: "none",
                        },
                        "&::after": {
                            animationDelay: ".8s", // offset second ring
                        },

                        "@media (prefers-reduced-motion: reduce)": {
                            "&::before, &::after": {
                                animation: "none",
                            },
                        },
                    }}
                />

            }
        </>
    );
});

EventPingDot.displayName = 'EventPingDot';

export default EventPingDot;
