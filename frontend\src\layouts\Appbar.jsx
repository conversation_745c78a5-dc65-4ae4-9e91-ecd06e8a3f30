import { Grid, Icon<PERSON>utton, Typo<PERSON> } from "@mui/material";
import MenuIcon from "@mui/icons-material/Menu";
import { Link } from "react-router-dom";
import { useApp } from "../hooks/AppHook";
import { version } from "../../package.json";
import ProfileMenu from "./ProfileMenu";

const Appbar = ({ setDrawerOpen }) => {
    const { screenSize } = useApp();

    return (
        <Grid
            container
            bgcolor={"primary.main"}
            flexDirection={{ xs: "row", sm: "row" }}
            justifyContent={"space-between"}
            alignItems={"center"}
            paddingX={2}
            paddingY={1}
        >
            <Grid display={{ xs: "flex", lg: "none" }} className="mobile-step-1">
                <IconButton onClick={() => setDrawerOpen(true)} sx={{ color: "primary.contrastText", padding: 0 }} disableRipple>
                    <MenuIcon />
                </IconButton>
            </Grid>
            <Grid
                container
                display={"flex"}
                alignItems={"center"}
                gap={{ xs: 0, lg: 2 }}
                flexDirection={{ xs: "column-reverse", lg: "row" }}
                size={{
                    xs: "auto",
                    lg: "grow",
                }}
            >
                <Grid display={"flex"} alignItems={"flex-end"}>
                    <Link to="/dashboard/stream" style={{ textDecoration: "none", cursor: "pointer" }}>
                        <Typography color={"#FFFFFF"} fontWeight={"bold"} sx={{ fontSize: { sm: "18px", lg: "2.0243rem" } }}>
                            SMARTMAST
                        </Typography>
                    </Link>
                    <Typography
                        color={"#FFFFFF"}
                        fontWeight={"400"}
                        sx={{ fontSize: { sm: "12px", lg: "14px" }, lineHeight: { xs: "21px", lg: "35px" } }}
                    >
                        v{version}
                    </Typography>
                </Grid>
                <Grid container display={"flex"} alignItems={"center"} gap={{ xs: 1, sm: 2 }} marginLeft={"auto"} marginRight={"auto"} size="auto">
                    <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} gap={"10px"}>
                        <img src="/quartermaster-logo-white.svg" alt="Quartermaster Logo" width={30} />
                        <img src="/quartermaster-text-white.svg" alt="Quartermaster Logo" width={screenSize.xs ? 180 : 230} />
                    </Grid>
                </Grid>
            </Grid>
            <Grid>
                <ProfileMenu avatarOnly={screenSize.xs || screenSize.sm || screenSize.md} />
            </Grid>
        </Grid>
    );
};

export default Appbar;
