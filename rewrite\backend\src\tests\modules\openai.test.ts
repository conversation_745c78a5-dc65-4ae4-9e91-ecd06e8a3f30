import { describe, it, beforeEach, expect, jest, afterEach } from '@jest/globals';

const originalEnv = process.env;

const mockCreate = jest.fn();
const mockEmbeddings = {
    create: mockCreate,
};

const MockOpenAI = jest.fn().mockImplementation(() => ({
    embeddings: mockEmbeddings,
}));

jest.mock('openai', () => ({
    __esModule: true,
    default: MockOpenAI,
}));

describe('OpenAI Module', () => {
    beforeEach(() => {
        process.env = { ...originalEnv };
        jest.clearAllMocks();
        mockCreate.mockClear();
        MockOpenAI.mockClear();
    });

    afterEach(() => {
        process.env = originalEnv;
    });

    it('should generate embedding with default model', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        const mockEmbedding = [0.1, 0.2, 0.3, 0.4, 0.5];
        mockCreate.mockResolvedValue({
            data: [{ embedding: mockEmbedding }],
        } as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        const result = await generateOpenAIEmbedding({ input: 'test input' });

        expect(MockOpenAI).toHaveBeenCalledWith({ apiKey: 'test-api-key' });
        expect(mockCreate).toHaveBeenCalledWith({
            model: 'text-embedding-3-small',
            input: 'test input',
        });
        expect(result).toEqual(mockEmbedding);
    });

    it('should generate embedding with custom model', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        const mockEmbedding = [0.1, 0.2, 0.3];
        mockCreate.mockResolvedValue({
            data: [{ embedding: mockEmbedding }],
        } as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        const result = await generateOpenAIEmbedding({
            input: 'test input',
            model: 'text-embedding-3-large',
        });

        expect(mockCreate).toHaveBeenCalledWith({
            model: 'text-embedding-3-large',
            input: 'test input',
        });
        expect(result).toEqual(mockEmbedding);
    });

    it('should handle different input strings', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        const mockEmbedding = [0.5, 0.6, 0.7];
        mockCreate.mockResolvedValue({
            data: [{ embedding: mockEmbedding }],
        } as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        const result = await generateOpenAIEmbedding({ input: 'different input text' });

        expect(mockCreate).toHaveBeenCalledWith({
            model: 'text-embedding-3-small',
            input: 'different input text',
        });
        expect(result).toEqual(mockEmbedding);
    });

    it('should handle empty input string', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        const mockEmbedding: number[] = [];
        mockCreate.mockResolvedValue({
            data: [{ embedding: mockEmbedding }],
        } as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        const result = await generateOpenAIEmbedding({ input: '' });

        expect(mockCreate).toHaveBeenCalledWith({
            model: 'text-embedding-3-small',
            input: '',
        });
        expect(result).toEqual(mockEmbedding);
    });

    it('should handle long input strings', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        const longInput = 'a'.repeat(10000);
        const mockEmbedding = [0.1, 0.2, 0.3];
        mockCreate.mockResolvedValue({
            data: [{ embedding: mockEmbedding }],
        } as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        const result = await generateOpenAIEmbedding({ input: longInput });

        expect(mockCreate).toHaveBeenCalledWith({
            model: 'text-embedding-3-small',
            input: longInput,
        });
        expect(result).toEqual(mockEmbedding);
    });

    it('should handle API errors', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        const mockError = new Error('API Error');
        mockCreate.mockRejectedValue(mockError as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        await expect(generateOpenAIEmbedding({ input: 'test' })).rejects.toThrow('API Error');
    });

    it('should handle empty response data', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        mockCreate.mockResolvedValue({
            data: [],
        } as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        await expect(generateOpenAIEmbedding({ input: 'test' })).rejects.toThrow();
    });

    it('should handle missing embedding in response', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        mockCreate.mockResolvedValue({
            data: [{}],
        } as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        const result = await generateOpenAIEmbedding({ input: 'test' });
        expect(result).toBeUndefined();
    });

    it('should use OPENAI_API_KEY from environment', async () => {
        process.env.OPENAI_API_KEY = 'custom-api-key-123';

        mockCreate.mockResolvedValue({
            data: [{ embedding: [0.1, 0.2] }],
        } as never);

        jest.resetModules();
        await import('../../modules/openai');

        expect(MockOpenAI).toHaveBeenCalledWith({ apiKey: 'custom-api-key-123' });
    });

    it('should handle undefined OPENAI_API_KEY', async () => {
        delete process.env.OPENAI_API_KEY;

        jest.resetModules();
        await import('../../modules/openai');

        expect(MockOpenAI).toHaveBeenCalledWith({ apiKey: undefined });
    });

    it('should return first embedding from data array', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        const mockEmbedding1 = [0.1, 0.2, 0.3];
        const mockEmbedding2 = [0.4, 0.5, 0.6];
        mockCreate.mockResolvedValue({
            data: [{ embedding: mockEmbedding1 }, { embedding: mockEmbedding2 }],
        } as never);

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        const result = await generateOpenAIEmbedding({ input: 'test' });

        expect(result).toEqual(mockEmbedding1);
    });

    it('should handle all valid embedding models', async () => {
        process.env.OPENAI_API_KEY = 'test-api-key';

        const models = ['text-embedding-3-small', 'text-embedding-3-large', 'text-embedding-ada-002'];

        const { generateOpenAIEmbedding } = await import('../../modules/openai');

        for (const model of models) {
            const mockEmbedding = [0.1, 0.2, 0.3];
            mockCreate.mockResolvedValue({
                data: [{ embedding: mockEmbedding }],
            } as never);

            await generateOpenAIEmbedding({ input: 'test', model: model as any });

            expect(mockCreate).toHaveBeenCalledWith({
                model,
                input: 'test',
            });
        }
    });
});

