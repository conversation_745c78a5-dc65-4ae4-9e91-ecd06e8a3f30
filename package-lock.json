{"name": "quartermaster-webapp", "version": "2.19.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "quartermaster-webapp", "version": "2.19.0", "license": "ISC", "dependencies": {"husky": "^9.1.7"}, "engines": {"node": "18.x"}}, "node_modules/husky": {"version": "9.1.7", "resolved": "https://registry.npmjs.org/husky/-/husky-9.1.7.tgz", "integrity": "sha512-5gs5ytaNjBrh5Ow3zrvdUUY+0VxIuWVL4i9irt6friV+BqdCfmV11CQTWMiBYWHbXhco+J1kHfTOUkePhCDvMA==", "license": "MIT", "bin": {"husky": "bin.js"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/typicode"}}}}