import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import artifactFlagService from '../../services/ArtifactFlag.service';
import ArtifactFlag from '../../models/ArtifactFlag';
import db from '../../modules/db';

jest.mock('../../models/ArtifactFlag', () => require('../mocks/models/artifactFlag.mock'));
jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));

describe('ArtifactFlagService', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    it('flagArtifact throws when artifact not found', async () => {
        (db.qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue(null as never) });
        await expect(artifactFlagService.flagArtifact('a'.repeat(24), 'u'.repeat(24))).rejects.toThrow('Artifact not found');
    });

    it('flagArtifact throws when already flagged', async () => {
        (db.qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 'A' } as never) });
        (ArtifactFlag.findOne as jest.Mock).mockResolvedValue({ _id: 'F' } as never);
        await expect(artifactFlagService.flagArtifact('a'.repeat(24), 'u'.repeat(24))).rejects.toThrow('You have already flagged this artifact');
    });

    it('flagArtifact creates and saves flag', async () => {
        (db.qmai.collection as jest.Mock).mockReturnValue({ findOne: jest.fn().mockResolvedValue({ _id: 'A' } as never) });
        (ArtifactFlag.findOne as jest.Mock).mockResolvedValue(null as never);
        const flag = await artifactFlagService.flagArtifact('a'.repeat(24), 'u'.repeat(24));
        expect(flag).toBeDefined();
    });

    it('getFlaggedArtifacts returns [] when no ids', async () => {
        (ArtifactFlag.distinct as jest.Mock).mockResolvedValue([] as never);
        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([] as never);
        const res = await artifactFlagService.getFlaggedArtifacts(1, 10);
        expect(res).toEqual({ artifacts: [], totalCount: 0, page: 1, pageSize: 10 });
    });

    it('getFlaggedArtifacts maps artifacts and filters archived', async () => {
        const mongoose = require('mongoose');
        const artifactId1 = new mongoose.Types.ObjectId();
        const artifactId2 = new mongoose.Types.ObjectId();

        (ArtifactFlag.distinct as jest.Mock).mockResolvedValue([artifactId1, artifactId2] as never);

        const mockFind1 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                { _id: artifactId1, portal: { is_archived: false } },
                { _id: artifactId2, portal: { is_archived: true } },
            ] as never),
        });

        const mockFind2 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                {
                    _id: artifactId1,
                    unit_id: 'QSX001',
                    bucket_name: 'bucket1',
                    portal: {},
                    timestamp: new Date(),
                },
            ] as never),
        });

        (db.qmai.collection as jest.Mock)
            .mockReturnValueOnce({ find: mockFind1 } as never)
            .mockReturnValueOnce({ find: mockFind2 } as never);

        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([
            {
                _id: artifactId1,
                flags: [{
                    _id: 'F',
                    flaggedBy: 'U',
                    flaggedByUser: { _id: 'U', name: 'User', email: '<EMAIL>' },
                    flaggedAt: new Date()
                }],
                flagCount: 1,
                latestFlagDate: new Date()
            },
        ] as never);

        const res = await artifactFlagService.getFlaggedArtifacts(1, 10);
        expect(res.artifacts.length).toBe(1);
        expect(res.totalCount).toBe(1);
        expect(res.page).toBe(1);
        expect(res.pageSize).toBe(10);
        expect(res.artifacts[0].flags[0].user).toEqual({ _id: 'U', name: 'User', email: '<EMAIL>' });
    });

    it('getFlaggedArtifacts drops entries with missing artifact', async () => {
        const mongoose = require('mongoose');
        const artifactId1 = new mongoose.Types.ObjectId();
        const artifactId2 = new mongoose.Types.ObjectId();

        (ArtifactFlag.distinct as jest.Mock).mockResolvedValue([artifactId1, artifactId2] as never);

        const mockFind1 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                { _id: artifactId1, portal: { is_archived: false } },
                { _id: artifactId2, portal: { is_archived: false } },
            ] as never),
        });

        const mockFind2 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([] as never), // No artifacts found for the flagged IDs
        });

        (db.qmai.collection as jest.Mock)
            .mockReturnValueOnce({ find: mockFind1 } as never)
            .mockReturnValueOnce({ find: mockFind2 } as never);

        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([
            {
                _id: artifactId2,
                flags: [],
                flagCount: 1,
                latestFlagDate: new Date()
            },
        ] as never);

        const res = await artifactFlagService.getFlaggedArtifacts(1, 10);
        expect(res.artifacts).toEqual([]);
        expect(res.totalCount).toBe(2);
    });

    it('unflagArtifact throws when flag missing', async () => {
        (ArtifactFlag.findOneAndDelete as jest.Mock).mockResolvedValue(null as never);
        await expect(artifactFlagService.unflagArtifact('a'.repeat(24), 'u'.repeat(24))).rejects.toThrow('Flag not found');
    });

    it('unflagArtifact returns deleted flag', async () => {
        (ArtifactFlag.findOneAndDelete as jest.Mock).mockResolvedValue({ _id: 'F' } as never);
        const res = await artifactFlagService.unflagArtifact('a'.repeat(24), 'u'.repeat(24));
        expect(res).toEqual({ _id: 'F' });
    });

    it('getUserFlaggedArtifactIds maps ids', async () => {
        (ArtifactFlag.find as any).mockResolvedValue([{ artifactId: { toString: () => 'A1' } }]);
        const res = await artifactFlagService.getUserFlaggedArtifactIds('u'.repeat(24));
        expect(res).toEqual(['A1']);
    });

    it('removeAllFlagsFromArtifact returns deleteMany result', async () => {
        (ArtifactFlag.deleteMany as any).mockResolvedValue({ deletedCount: 3 });
        const res = await artifactFlagService.removeAllFlagsFromArtifact('a'.repeat(24));
        expect(res).toEqual({ deletedCount: 3 });
    });

    it('getFlaggedArtifacts returns empty when no flagged artifacts after filtering archived', async () => {
        const mongoose = require('mongoose');
        const artifactId1 = new mongoose.Types.ObjectId();

        (ArtifactFlag.distinct as jest.Mock).mockResolvedValue([artifactId1] as never);

        const mockFind1 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                { _id: artifactId1, portal: { is_archived: true } },
            ] as never),
        });

        (db.qmai.collection as jest.Mock).mockReturnValue({ find: mockFind1 } as never);

        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([] as never);

        const res = await artifactFlagService.getFlaggedArtifacts(1, 10);
        expect(res.artifacts).toEqual([]);
        expect(res.totalCount).toBe(0);
    });

    it('getFlaggedArtifacts handles pagination correctly', async () => {
        const mongoose = require('mongoose');
        const artifactId1 = new mongoose.Types.ObjectId();
        const artifactId2 = new mongoose.Types.ObjectId();

        (ArtifactFlag.distinct as jest.Mock).mockResolvedValue([artifactId1, artifactId2] as never);

        const mockFind1 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                { _id: artifactId1, portal: { is_archived: false } },
                { _id: artifactId2, portal: { is_archived: false } },
            ] as never),
        });

        const mockFind2 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                {
                    _id: artifactId2,
                    unit_id: 'QSX002',
                    bucket_name: 'bucket2',
                    portal: {},
                    timestamp: new Date(),
                },
            ] as never),
        });

        (db.qmai.collection as jest.Mock)
            .mockReturnValueOnce({ find: mockFind1 } as never)
            .mockReturnValueOnce({ find: mockFind2 } as never);

        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([
            {
                _id: artifactId2,
                flags: [{
                    _id: 'F2',
                    flaggedBy: 'U2',
                    flaggedByUser: { _id: 'U2', name: 'User2', email: '<EMAIL>' },
                    flaggedAt: new Date()
                }],
                flagCount: 1,
                latestFlagDate: new Date()
            },
        ] as never);

        const res = await artifactFlagService.getFlaggedArtifacts(2, 1);
        expect(res.artifacts.length).toBe(1);
        expect(res.totalCount).toBe(2);
        expect(res.page).toBe(2);
        expect(res.pageSize).toBe(1);
    });

    it('getFlaggedArtifacts handles case when all artifacts are archived', async () => {
        const mongoose = require('mongoose');
        const artifactId1 = new mongoose.Types.ObjectId();
        const artifactId2 = new mongoose.Types.ObjectId();

        (ArtifactFlag.distinct as jest.Mock).mockResolvedValue([artifactId1, artifactId2] as never);

        const mockFind1 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                { _id: artifactId1, portal: { is_archived: true } },
                { _id: artifactId2, portal: { is_archived: true } },
            ] as never),
        });

        (db.qmai.collection as jest.Mock).mockReturnValue({ find: mockFind1 } as never);

        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([] as never);

        const res = await artifactFlagService.getFlaggedArtifacts(1, 10);
        expect(res.artifacts).toEqual([]);
        expect(res.totalCount).toBe(0);
    });

    it('getFlaggedArtifacts handles case when no archived IDs to exclude', async () => {
        const mongoose = require('mongoose');
        const artifactId1 = new mongoose.Types.ObjectId();

        (ArtifactFlag.distinct as jest.Mock).mockResolvedValue([artifactId1] as never);

        const mockFind1 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                { _id: artifactId1, portal: { is_archived: false } },
            ] as never),
        });

        const mockFind2 = jest.fn().mockReturnValue({
            toArray: jest.fn().mockResolvedValue([
                {
                    _id: artifactId1,
                    unit_id: 'QSX001',
                    bucket_name: 'bucket1',
                    portal: {},
                    timestamp: new Date(),
                },
            ] as never),
        });

        (db.qmai.collection as jest.Mock)
            .mockReturnValueOnce({ find: mockFind1 } as never)
            .mockReturnValueOnce({ find: mockFind2 } as never);

        (ArtifactFlag.aggregate as jest.Mock).mockResolvedValue([
            {
                _id: artifactId1,
                flags: [{
                    _id: 'F',
                    flaggedBy: 'U',
                    flaggedByUser: { _id: 'U', name: 'User', email: '<EMAIL>' },
                    flaggedAt: new Date()
                }],
                flagCount: 1,
                latestFlagDate: new Date()
            },
        ] as never);

        const res = await artifactFlagService.getFlaggedArtifacts(1, 10);
        expect(res.artifacts.length).toBe(1);
        expect(res.totalCount).toBe(1);
    });

    describe('getEvaluatedArtifacts', () => {
        it('should return empty array when no evaluated artifacts', async () => {
            const mockCollection = {
                countDocuments: jest.fn().mockResolvedValue(0),
                find: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([] as never),
                            }),
                        }),
                    }),
                }),
            };
            (db.qmai.collection as jest.Mock).mockReturnValue(mockCollection);
            (ArtifactFlag.find as jest.Mock).mockReturnValue({
                populate: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        lean: jest.fn().mockResolvedValue([] as never),
                    }),
                }),
            });

            const result = await artifactFlagService.getEvaluatedArtifacts(1, 10);
            expect(result).toEqual({ artifacts: [], totalCount: 0, page: 1, pageSize: 10 });
        });

        it('should return evaluated artifacts without evaluation types filter', async () => {
            const mongoose = require('mongoose');
            const artifactId = new mongoose.Types.ObjectId();
            const mockArtifact = {
                _id: artifactId,
                unit_id: 'QSX001',
                bucket_name: 'bucket1',
                portal: {
                    ais_discrepancy: 'both_matched',
                    ais_discrepancy_timestamp: new Date('2023-01-01'),
                    is_archived: false,
                },
                timestamp: new Date('2023-01-01'),
            };

            const mockCollection = {
                countDocuments: jest.fn().mockResolvedValue(1),
                find: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([mockArtifact] as never),
                            }),
                        }),
                    }),
                }),
            };
            (db.qmai.collection as jest.Mock).mockReturnValue(mockCollection);
            (ArtifactFlag.find as jest.Mock).mockReturnValue({
                populate: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        lean: jest.fn().mockResolvedValue([] as never),
                    }),
                }),
            });

            const result = await artifactFlagService.getEvaluatedArtifacts(1, 10);
            expect(result.artifacts.length).toBe(1);
            expect(result.totalCount).toBe(1);
            expect(mockCollection.find).toHaveBeenCalledWith(
                expect.objectContaining({
                    'portal.ais_discrepancy': { $exists: true, $ne: null },
                    'portal.is_archived': { $ne: true },
                }),
                expect.any(Object)
            );
        });

        it('should filter by evaluation types when provided', async () => {
            const mongoose = require('mongoose');
            const artifactId = new mongoose.Types.ObjectId();
            const mockArtifact = {
                _id: artifactId,
                unit_id: 'QSX001',
                bucket_name: 'bucket1',
                portal: {
                    ais_discrepancy: 'both_matched',
                    ais_discrepancy_timestamp: new Date('2023-01-01'),
                    is_archived: false,
                },
                timestamp: new Date('2023-01-01'),
            };

            const mockCollection = {
                countDocuments: jest.fn().mockResolvedValue(1),
                find: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([mockArtifact] as never),
                            }),
                        }),
                    }),
                }),
            };
            (db.qmai.collection as jest.Mock).mockReturnValue(mockCollection);
            (ArtifactFlag.find as jest.Mock).mockReturnValue({
                populate: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        lean: jest.fn().mockResolvedValue([] as never),
                    }),
                }),
            });

            const evaluationTypes = ['both_matched', 'detection_not_matched'];
            const result = await artifactFlagService.getEvaluatedArtifacts(1, 10, evaluationTypes);
            expect(result.artifacts.length).toBe(1);
            expect(mockCollection.find).toHaveBeenCalledWith(
                expect.objectContaining({
                    'portal.ais_discrepancy': { $in: evaluationTypes },
                    'portal.is_archived': { $ne: true },
                }),
                expect.any(Object)
            );
        });

        it('should include flag information for evaluated artifacts', async () => {
            const mongoose = require('mongoose');
            const artifactId = new mongoose.Types.ObjectId();
            const mockArtifact = {
                _id: artifactId,
                unit_id: 'QSX001',
                bucket_name: 'bucket1',
                portal: {
                    ais_discrepancy: 'both_matched',
                    ais_discrepancy_timestamp: new Date('2023-01-01'),
                    is_archived: false,
                },
                timestamp: new Date('2023-01-01'),
            };

            const mockFlags = [
                {
                    _id: 'flag1',
                    flaggedBy: {
                        _id: 'user1',
                        name: 'User One',
                        email: '<EMAIL>',
                    },
                    flaggedAt: new Date('2023-01-02'),
                },
            ];

            const mockCollection = {
                countDocuments: jest.fn().mockResolvedValue(1),
                find: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([mockArtifact] as never),
                            }),
                        }),
                    }),
                }),
            };
            (db.qmai.collection as jest.Mock).mockReturnValue(mockCollection);
            (ArtifactFlag.find as jest.Mock).mockReturnValue({
                populate: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        lean: jest.fn().mockResolvedValue(mockFlags as never),
                    }),
                }),
            });

            const result = await artifactFlagService.getEvaluatedArtifacts(1, 10);
            expect(result.artifacts.length).toBe(1);
            expect(result.artifacts[0].flags.length).toBe(1);
            expect(result.artifacts[0].flagCount).toBe(1);
            expect(result.artifacts[0].flags[0].user).toEqual({
                _id: 'user1',
                name: 'User One',
                email: '<EMAIL>',
            });
        });

        it('should handle pagination correctly', async () => {
            const mockCollection = {
                countDocuments: jest.fn().mockResolvedValue(20),
                find: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([] as never),
                            }),
                        }),
                    }),
                }),
            };
            (db.qmai.collection as jest.Mock).mockReturnValue(mockCollection);
            (ArtifactFlag.find as jest.Mock).mockReturnValue({
                populate: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        lean: jest.fn().mockResolvedValue([] as never),
                    }),
                }),
            });

            const result = await artifactFlagService.getEvaluatedArtifacts(2, 5);
            expect(result.page).toBe(2);
            expect(result.pageSize).toBe(5);
            expect(result.totalCount).toBe(20);
        });

        it('should exclude archived artifacts', async () => {
            const mockCollection = {
                countDocuments: jest.fn().mockResolvedValue(0),
                find: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([] as never),
                            }),
                        }),
                    }),
                }),
            };
            (db.qmai.collection as jest.Mock).mockReturnValue(mockCollection);
            (ArtifactFlag.find as jest.Mock).mockReturnValue({
                populate: jest.fn().mockReturnValue({
                    sort: jest.fn().mockReturnValue({
                        lean: jest.fn().mockResolvedValue([] as never),
                    }),
                }),
            });

            await artifactFlagService.getEvaluatedArtifacts(1, 10);
            expect(mockCollection.find).toHaveBeenCalledWith(
                expect.objectContaining({
                    'portal.is_archived': { $ne: true },
                }),
                expect.any(Object)
            );
        });
    });
});


