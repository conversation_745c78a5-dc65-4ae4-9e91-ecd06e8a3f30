import { Dialog, DialogTitle, DialogContent, DialogActions, Button, FormControl, Select, MenuItem, Typography, IconButton, Stack, Skeleton, CircularProgress, Box } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import StarIcon from "@mui/icons-material/Star";
import { useState, useEffect } from "react";
import theme from "../../../../theme";
import ListModal from "./ListModal";
import { useUser } from "../../../../hooks/UserHook";
import useStore from "../../../../hooks/Store";

const MAX_DISPLAY_LENGTH = 20;

const truncateName = (name, maxLength = MAX_DISPLAY_LENGTH) => {
    if (!name) return "";
    if (name.length <= maxLength) return name;
    return name.substring(0, maxLength) + "...";
};

export default function SetSaveListModal({ open, onClose, lists = [], isLoadingLists = false }) {
    const { user } = useUser();
    const { selectedSaveList: storeSelectedSaveList, setSelectedSaveList } = useStore();
    const [selectedListId, setSelectedListId] = useState("favorites");
    const [showAddModal, setShowAddModal] = useState(false);
    const [loading, setLoading] = useState(false);
    const [newlyCreatedLists, setNewlyCreatedLists] = useState([]);

    useEffect(() => {
        if (open) {
            if (storeSelectedSaveList?.id) {
                setSelectedListId(storeSelectedSaveList.id);
            } else {
                const saved = localStorage.getItem("selectedSaveList");
                if (saved) {
                    try {
                        const parsed = JSON.parse(saved);
                        setSelectedListId(parsed.id || "favorites");
                    } catch (e) {
                        setSelectedListId("favorites");
                    }
                } else {
                    setSelectedListId("favorites");
                }
            }
        } else {
            setNewlyCreatedLists([]);
        }
    }, [open, storeSelectedSaveList]);

    const allLists = [...newlyCreatedLists, ...lists];

    const yourLists = allLists.filter((l) => String(l.owner_id) === String(user?._id));
    const sharedWithYou = allLists.filter((l) => String(l.owner_id) !== String(user?._id));

    const renderSelectedLabel = () => {
        if (selectedListId === "favorites") {
            return (
                <Stack direction="row" alignItems="center" spacing={0.5}>
                    <Typography sx={{ color: "#FFFFFF" }}>Favorites (private)</Typography>
                    <StarIcon sx={{ color: "#FFD700", fontSize: "16px" }} />
                </Stack>
            );
        }
        const selected = allLists.find((l) => String(l._id) === String(selectedListId));
        if (!selected) return null;
        const suffix = selected.shared_with_organization || String(selected.owner_id) !== String(user?._id) ? "(shared)" : "(private)";
        const displayName = truncateName(selected.name);
        const fullText = `${selected.name} ${suffix}`;
        return (
            <Stack direction="row" alignItems="center" spacing={0.5}>
                <Typography
                    sx={{
                        color: "#FFFFFF",
                        overflow: "hidden",
                        textOverflow: "ellipsis",
                        whiteSpace: "nowrap",
                        maxWidth: "100%"
                    }}
                    title={fullText}
                >
                    {`${displayName} ${suffix}`}
                </Typography>
                <StarIcon sx={{ color: "#FFD700", fontSize: "16px", flexShrink: 0 }} />
            </Stack>
        );
    };

    const handleSave = () => {
        let saveListObj;
        if (selectedListId === "favorites") {
            saveListObj = { id: "favorites", name: "Favorites" };
        } else {
            const selected = allLists.find((l) => String(l._id) === String(selectedListId));
            if (!selected) {
                saveListObj = { id: "favorites", name: "Favorites" };
                setSelectedListId("favorites");
            } else {
                saveListObj = { id: selected._id, name: selected.name };
            }
        }
        setSelectedSaveList(saveListObj);
        if (typeof window !== "undefined") {
            localStorage.setItem("selectedSaveList", JSON.stringify(saveListObj));
            window.dispatchEvent(
                new CustomEvent("localStorageChange", {
                    detail: {
                        key: "selectedSaveList",
                        newValue: JSON.stringify(saveListObj),
                    },
                }),
            );
        }
        onClose();
    };

    const handleAddListSuccess = (newList) => {
        setNewlyCreatedLists((prev) => {
            const exists = prev.some((l) => String(l._id) === String(newList._id));
            if (exists) return prev;
            return [newList, ...prev];
        });
        window.dispatchEvent(new CustomEvent("showManageLists", { detail: { show: true } }));
        setShowAddModal(false);
    };

    return (
        <>
            <Dialog
                open={open}
                onClose={onClose}
                maxWidth="sm"
                fullWidth
                PaperProps={{
                    sx: {
                        backgroundColor: theme.palette.custom.darkBlue,
                        borderRadius: "8px",
                    },
                }}
            >
                <DialogTitle
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        padding: "20px 24px",
                    }}
                >
                    <Typography sx={{ color: "#FFFFFF", fontWeight: 600, fontSize: "18px" }}>Set save list</Typography>
                    <IconButton onClick={onClose} sx={{ color: "#FFFFFF", padding: 0 }}>
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{ padding: "24px", paddingBottom: 0 }}>
                    <Typography sx={{ color: "#FFFFFF", marginBottom: "20px", fontSize: "14px" }}>
                        Clicking the star on an artifact will save it to the list selected below. You may change this at any time.
                    </Typography>
                    <Stack spacing={2}>
                        <FormControl fullWidth variant="outlined">
                            {isLoadingLists ? (
                                <Box
                                    sx={{
                                        padding: "12px",
                                        backgroundColor: theme.palette.custom.darkBlue,
                                        borderRadius: "5px",
                                        border: `1px solid ${theme.palette.custom.borderColor}`,
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 2,
                                    }}
                                >
                                    <CircularProgress size={20} sx={{ color: "#FFFFFF" }} />
                                    <Skeleton variant="text" width="60%" height={24} sx={{ bgcolor: "rgba(255, 255, 255, 0.1)" }} />
                                </Box>
                            ) : (
                                <Select
                                    value={selectedListId}
                                    onChange={(e) => setSelectedListId(e.target.value)}
                                    displayEmpty
                                    renderValue={() => renderSelectedLabel()}
                                    sx={{
                                        "& .MuiOutlinedInput-notchedOutline": {
                                            border: `1px solid ${theme.palette.custom.borderColor}`,
                                        },
                                        "& .MuiSelect-select": {
                                            padding: "12px",
                                            backgroundColor: theme.palette.custom.darkBlue,
                                            borderRadius: "5px",
                                            color: "#FFFFFF",
                                        },
                                        "& .MuiSvgIcon-root": {
                                            color: "#FFFFFF",
                                        },
                                    }}
                                    MenuProps={{
                                        PaperProps: {
                                            sx: {
                                                backgroundColor: theme.palette.custom.darkBlue,
                                                "& .MuiMenuItem-root": {
                                                    color: "#FFFFFF",
                                                    "&:hover": {
                                                        backgroundColor: theme.palette.custom.borderColor,
                                                    },
                                                },
                                            },
                                        },
                                    }}
                                >
                                    <MenuItem value="favorites">
                                        <Stack direction="row" alignItems="center" spacing={0.5}>
                                            <Typography>Favorites (private)</Typography>
                                            {selectedListId === "favorites" && <StarIcon sx={{ color: "#FFD700", fontSize: "16px" }} />}
                                        </Stack>
                                    </MenuItem>
                                    {yourLists.map((l) => {
                                        const suffix = l.shared_with_organization ? "(shared)" : "(private)";
                                        const displayName = truncateName(l.name);
                                        const fullText = `${l.name} ${suffix}`;
                                        return (
                                            <MenuItem key={l._id} value={l._id}>
                                                <Stack direction="row" alignItems="center" spacing={0.5} sx={{ width: "100%", minWidth: 0 }}>
                                                    <Typography
                                                        sx={{
                                                            overflow: "hidden",
                                                            textOverflow: "ellipsis",
                                                            whiteSpace: "nowrap",
                                                            flex: 1,
                                                        }}
                                                        title={fullText}
                                                    >
                                                        {displayName} {suffix}
                                                    </Typography>
                                                    {selectedListId === l._id && <StarIcon sx={{ color: "#FFD700", fontSize: "16px", flexShrink: 0 }} />}
                                                </Stack>
                                            </MenuItem>
                                        );
                                    })}
                                    {sharedWithYou.map((l) => {
                                        const displayName = truncateName(l.name);
                                        const fullText = `${l.name} (shared)`;
                                        return (
                                            <MenuItem key={l._id} value={l._id}>
                                                <Stack direction="row" alignItems="center" spacing={0.5} sx={{ width: "100%", minWidth: 0 }}>
                                                    <Typography
                                                        sx={{
                                                            overflow: "hidden",
                                                            textOverflow: "ellipsis",
                                                            whiteSpace: "nowrap",
                                                            flex: 1,
                                                        }}
                                                        title={fullText}
                                                    >
                                                        {displayName} (shared)
                                                    </Typography>
                                                    {selectedListId === l._id && <StarIcon sx={{ color: "#FFD700", fontSize: "16px", flexShrink: 0 }} />}
                                                </Stack>
                                            </MenuItem>
                                        );
                                    })}
                                </Select>
                            )}
                        </FormControl>
                        <Button
                            variant="outlined"
                            onClick={() => setShowAddModal(true)}
                            startIcon={<AddIcon />}
                            sx={{
                                color: "#FFFFFF",
                                borderColor: theme.palette.custom.borderColor,
                                textTransform: "uppercase",
                                fontSize: "12px",
                                width: "fit-content",
                                padding: "10px 16px",
                            }}
                        >
                            Add List
                        </Button>
                    </Stack>
                </DialogContent>
                <DialogActions
                    sx={{
                        padding: "16px 24px",
                        display: "flex",
                        justifyContent: "center",
                        gap: 1,
                    }}
                >
                    <Button
                        onClick={onClose}
                        variant="outlined"
                        sx={{
                            color: "#FFFFFF",
                            borderColor: theme.palette.custom.borderColor,
                            textTransform: "none",
                        }}
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSave}
                        variant="contained"
                        disabled={loading}
                        sx={{
                            backgroundColor: theme.palette.custom.mainBlue,
                            color: "#FFFFFF",
                            textTransform: "none",
                        }}
                    >
                        Save
                    </Button>
                </DialogActions>
            </Dialog>
            <ListModal open={showAddModal} onClose={() => setShowAddModal(false)} onSuccess={handleAddListSuccess} />
        </>
    );
}

