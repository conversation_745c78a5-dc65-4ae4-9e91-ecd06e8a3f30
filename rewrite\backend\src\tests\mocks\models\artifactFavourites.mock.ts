import { jest } from '@jest/globals';

type AnyObject = Record<string, any>;

function MockArtifactFavourite(this: AnyObject, data: AnyObject) {
    Object.assign(this, data);
}

const createChainableMock = (mockResolvedValue: any) => {
    const chainable = {
        lean: jest.fn().mockResolvedValue(mockResolvedValue as never),
        select: jest.fn().mockReturnThis(),
    };
    chainable.select.mockReturnValue(chainable);
    return chainable;
};

(MockArtifactFavourite as AnyObject).find = jest.fn().mockReturnValue(createChainableMock([]));
(MockArtifactFavourite as AnyObject).findOne = jest.fn();
(MockArtifactFavourite as AnyObject).findOneAndDelete = jest.fn();
(MockArtifactFavourite as AnyObject).countDocuments = jest.fn();
(MockArtifactFavourite as AnyObject).bulkWrite = jest.fn().mockResolvedValue({} as never);
(MockArtifactFavourite as AnyObject).create = jest.fn();

(MockArtifactFavourite as AnyObject).prototype = {
    save: jest.fn().mockResolvedValue(true as never),
};

export default MockArtifactFavourite as any;

