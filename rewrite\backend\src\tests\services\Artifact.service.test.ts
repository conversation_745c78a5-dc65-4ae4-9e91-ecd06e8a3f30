import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import artifactService from '../../services/Artifact.service';
import db from '../../modules/db';
import { processSearchQuery, matchToSearchFilter, groupByImage, groupArtifactsByDuplicateIndex } from '../../utils/functions';
import { generateOpenAIEmbedding } from '../../modules/openai';
import { processBatchItem } from '../../modules/awsS3';

process.env.CLOUDFRONT_KEY_PAIR_ID = 'test-key-pair-id';
process.env.CLOUDFRONT_PRIVATE_KEY = 'test-private-key';
process.env.AWS_ACCESS_KEY_ID = 'test-access-key';
process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
jest.mock('../../modules/openai', () => require('../mocks/modules/openai.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));

describe('ArtifactService', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('searchArtifactsIds', () => {
        it('should return empty result when searchQuery is empty', async () => {
            const result = await artifactService.searchArtifactsIds({
                searchQuery: '',
                filterQuery: {},
                skip: 0,
                limit: 10,
            });
            expect(result).toEqual({ searchIds: [] });
            expect(processSearchQuery).not.toHaveBeenCalled();
        });

        it('should return empty result when processedSearchQuery is undefined', async () => {
            (processSearchQuery as jest.Mock).mockReturnValue(undefined);
            const result = await artifactService.searchArtifactsIds({
                searchQuery: 'test',
                filterQuery: {},
                skip: 0,
                limit: 10,
            });
            expect(result).toEqual({ searchIds: [] });
        });

        it('should return empty result when processedSearchQuery is empty string', async () => {
            (processSearchQuery as jest.Mock).mockReturnValue('');
            const result = await artifactService.searchArtifactsIds({
                searchQuery: 'test',
                filterQuery: {},
                skip: 0,
                limit: 10,
            });
            expect(result).toEqual({ searchIds: [] });
        });

        it('should return empty result when totalCount aggregation returns empty array', async () => {
            (processSearchQuery as jest.Mock).mockReturnValue('test query');
            (generateOpenAIEmbedding as jest.Mock).mockResolvedValue([0.1, 0.2, 0.3] as never);
            (matchToSearchFilter as jest.Mock).mockReturnValue({ must: [], mustNot: [] });

            const mockAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([] as never),
            });
            (db.searchArtifacts.collection as jest.Mock).mockReturnValue({
                aggregate: mockAggregate,
            });

            const result = await artifactService.searchArtifactsIds({
                searchQuery: 'test query',
                filterQuery: {},
                skip: 0,
                limit: 10,
            });

            expect(result).toEqual({ searchIds: [] });
        });

        it('should successfully search artifacts with valid query and filters', async () => {
            const mockEmbedding = [0.1, 0.2, 0.3, 0.4, 0.5];
            const mockFilterResult = {
                must: [{ equals: { value: 'test', path: 'category' } }],
                mustNot: [{ equals: { value: 'excluded', path: 'status' } }],
            };

            (processSearchQuery as jest.Mock).mockReturnValue('test query');
            (generateOpenAIEmbedding as jest.Mock).mockResolvedValue(mockEmbedding as never);
            (matchToSearchFilter as jest.Mock).mockReturnValue(mockFilterResult);

            const mockCountAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ totalCount: 5 }] as never),
            });

            const mockSearchIds = [
                { _id: 'id1', score: 0.9 },
                { _id: 'id2', score: 0.8 },
                { _id: 'id3', score: 0.7 },
            ];

            const mockSearchAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue(mockSearchIds as never),
            });

            (db.searchArtifacts.collection as jest.Mock).mockReturnValue({
                aggregate: jest.fn((pipeline: any[]) => {
                    const hasCountStage = pipeline.some((stage: any) => stage.$count);
                    if (hasCountStage) {
                        return mockCountAggregate();
                    }
                    return mockSearchAggregate();
                }),
            });

            const result = await artifactService.searchArtifactsIds({
                searchQuery: 'test query',
                filterQuery: { category: 'test', status: { $ne: 'excluded' } },
                skip: 0,
                limit: 10,
            });

            expect(result).toEqual({
                searchIds: ['id1', 'id2', 'id3'],
            });
            expect(processSearchQuery).toHaveBeenCalledWith('test query');
            expect(generateOpenAIEmbedding).toHaveBeenCalledWith({ input: 'test query' });
            // expect(matchToSearchFilter).toHaveBeenCalledWith({
            //     category: 'test',
            //     status: { $ne: 'excluded' },
            // });
        });

        it('should handle skip and limit parameters correctly', async () => {
            const mockEmbedding = [0.1, 0.2, 0.3];
            (processSearchQuery as jest.Mock).mockReturnValue('test query');
            (generateOpenAIEmbedding as jest.Mock).mockResolvedValue(mockEmbedding as never);
            (matchToSearchFilter as jest.Mock).mockReturnValue({ must: [], mustNot: [] });

            const mockCountAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ totalCount: 20 }] as never),
            });

            const mockSearchIds = [{ _id: 'id1' }, { _id: 'id2' }];
            const mockSearchAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue(mockSearchIds as never),
            });

            (db.searchArtifacts.collection as jest.Mock).mockReturnValue({
                aggregate: jest.fn((pipeline: any[]) => {
                    const hasCountStage = pipeline.some((stage: any) => stage.$count);
                    if (hasCountStage) {
                        return mockCountAggregate();
                    }
                    return mockSearchAggregate();
                }),
            });

            const result = await artifactService.searchArtifactsIds({
                searchQuery: 'test query',
                filterQuery: {},
                skip: 5,
                limit: 10,
            });

            expect(result.searchIds).toEqual(['id1', 'id2']);
        });

        it('should calculate fetchN correctly based on skip and limit', async () => {
            const mockEmbedding = [0.1, 0.2, 0.3];
            (processSearchQuery as jest.Mock).mockReturnValue('test query');
            (generateOpenAIEmbedding as jest.Mock).mockResolvedValue(mockEmbedding as never);
            (matchToSearchFilter as jest.Mock).mockReturnValue({ must: [], mustNot: [] });

            const mockCountAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ totalCount: 100 }] as never),
            });

            const mockSearchIds = Array.from({ length: 15 }, (_, i) => ({ _id: `id${i}` }));
            const mockSearchAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue(mockSearchIds as never),
            });

            let capturedPipeline: any[] = [];
            (db.searchArtifacts.collection as jest.Mock).mockReturnValue({
                aggregate: jest.fn((pipeline: any[]) => {
                    const hasCountStage = pipeline.some((stage: any) => stage.$count);
                    if (hasCountStage) {
                        return mockCountAggregate();
                    }
                    capturedPipeline = pipeline;
                    return mockSearchAggregate();
                }),
            });

            await artifactService.searchArtifactsIds({
                searchQuery: 'test query',
                filterQuery: {},
                skip: 5,
                limit: 10,
            });

            const vectorSearchStage = capturedPipeline.find((stage: any) => stage.$vectorSearch);
            expect(vectorSearchStage).toBeDefined();
            expect(vectorSearchStage.$vectorSearch.numCandidates).toBe(75);
            expect(vectorSearchStage.$vectorSearch.limit).toBe(15);
        });

        it('should handle large skip and limit values for numCandidates calculation', async () => {
            const mockEmbedding = [0.1, 0.2, 0.3];
            (processSearchQuery as jest.Mock).mockReturnValue('test query');
            (generateOpenAIEmbedding as jest.Mock).mockResolvedValue(mockEmbedding as never);
            (matchToSearchFilter as jest.Mock).mockReturnValue({ must: [], mustNot: [] });

            // const mockCountAggregate = jest.fn().mockReturnValue({
            //     toArray: jest.fn().mockResolvedValue([{ totalCount: 1000 }] as never),
            // });

            const mockSearchIds = Array.from({ length: 200 }, (_, i) => ({ _id: `id${i}` }));
            const mockSearchAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue(mockSearchIds as never),
            });

            let capturedPipeline: any[] = [];
            (db.searchArtifacts.collection as jest.Mock).mockReturnValue({
                aggregate: jest.fn((pipeline: any[]) => {
                    // const hasCountStage = pipeline.some((stage: any) => stage.$count);
                    // if (hasCountStage) {
                    //     return mockCountAggregate();
                    // }
                    capturedPipeline = pipeline;
                    return mockSearchAggregate();
                }),
            });

            await artifactService.searchArtifactsIds({
                searchQuery: 'test query',
                filterQuery: {},
                skip: 50,
                limit: 100,
            });

            const vectorSearchStage = capturedPipeline.find((stage: any) => stage.$vectorSearch);
            expect(vectorSearchStage).toBeDefined();
            expect(vectorSearchStage.$vectorSearch.numCandidates).toBe(750);
            expect(vectorSearchStage.$vectorSearch.limit).toBe(150);
        });

        it('should use filterQuery in vectorSearch filter', async () => {
            const mockEmbedding = [0.1, 0.2, 0.3];
            (processSearchQuery as jest.Mock).mockReturnValue('test query');
            (generateOpenAIEmbedding as jest.Mock).mockResolvedValue(mockEmbedding as never);
            (matchToSearchFilter as jest.Mock).mockReturnValue({ must: [], mustNot: [] });

            // const mockCountAggregate = jest.fn().mockReturnValue({
            //     toArray: jest.fn().mockResolvedValue([{ totalCount: 10 }] as never),
            // });

            const mockSearchIds = [{ _id: 'id1' }];
            const mockSearchAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue(mockSearchIds as never),
            });

            let capturedPipeline: any[] = [];
            (db.searchArtifacts.collection as jest.Mock).mockReturnValue({
                aggregate: jest.fn((pipeline: any[]) => {
                    // const hasCountStage = pipeline.some((stage: any) => stage.$count);
                    // if (hasCountStage) {
                    //     return mockCountAggregate();
                    // }
                    capturedPipeline = pipeline;
                    return mockSearchAggregate();
                }),
            });

            const filterQuery = { category: 'test' };
            await artifactService.searchArtifactsIds({
                searchQuery: 'test query',
                filterQuery,
                skip: 0,
                limit: 10,
            });

            console.log("capturedPipeline", JSON.stringify(capturedPipeline, null, 2));
            const vectorSearchStage = capturedPipeline.find((stage: any) => stage.$vectorSearch);
            expect(vectorSearchStage).toBeDefined();
            expect(vectorSearchStage.$vectorSearch.filter).toEqual(filterQuery);
        });

        it('should handle empty searchIds array', async () => {
            const mockEmbedding = [0.1, 0.2, 0.3];
            (processSearchQuery as jest.Mock).mockReturnValue('test query');
            (generateOpenAIEmbedding as jest.Mock).mockResolvedValue(mockEmbedding as never);
            (matchToSearchFilter as jest.Mock).mockReturnValue({ must: [], mustNot: [] });

            // const mockCountAggregate = jest.fn().mockReturnValue({
            //     toArray: jest.fn().mockResolvedValue([{ totalCount: 5 }] as never),
            // });

            const mockSearchAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([] as never),
            });

            (db.searchArtifacts.collection as jest.Mock).mockReturnValue({
                aggregate: jest.fn(() => {
                    // const hasCountStage = pipeline.some((stage: any) => stage.$count);
                    // if (hasCountStage) {
                    //     return mockCountAggregate();
                    // }
                    return mockSearchAggregate();
                }),
            });

            const result = await artifactService.searchArtifactsIds({
                searchQuery: 'test query',
                filterQuery: {},
                skip: 0,
                limit: 10,
            });

            expect(result).toEqual({ searchIds: [] });
        });

        it('should map searchIds correctly from aggregation result', async () => {
            const mockEmbedding = [0.1, 0.2, 0.3];
            (processSearchQuery as jest.Mock).mockReturnValue('test query');
            (generateOpenAIEmbedding as jest.Mock).mockResolvedValue(mockEmbedding as never);
            (matchToSearchFilter as jest.Mock).mockReturnValue({ must: [], mustNot: [] });

            // const mockCountAggregate = jest.fn().mockReturnValue({
            //     toArray: jest.fn().mockResolvedValue([{ totalCount: 3 }] as never),
            // });

            const mockSearchIds = [
                { _id: 'id1', score: 0.95 },
                { _id: 'id2', score: 0.85 },
                { _id: 'id3', score: 0.75 },
            ];
            const mockSearchAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue(mockSearchIds as never),
            });

            (db.searchArtifacts.collection as jest.Mock).mockReturnValue({
                aggregate: jest.fn(() => {
                    // const hasCountStage = pipeline.some((stage: any) => stage.$count);
                    // if (hasCountStage) {
                    //     return mockCountAggregate();
                    // }
                    return mockSearchAggregate();
                }),
            });

            const result = await artifactService.searchArtifactsIds({
                searchQuery: 'test query',
                filterQuery: {},
                skip: 0,
                limit: 10,
            });

            expect(result.searchIds).toEqual(['id1', 'id2', 'id3']);
        });
    });

    describe('unifyArtifacts', () => {
        it('should call groupByImage with artifacts and return result', () => {
            const artifacts = [{ _id: '1', image_path: 'path1' }, { _id: '2', image_path: 'path2' }];
            const unified = [{ _id: '1', image_path: 'path1' }];
            (groupByImage as jest.Mock).mockReturnValue(unified);

            const result = artifactService.unifyArtifacts(artifacts);

            expect(groupByImage).toHaveBeenCalledWith(artifacts);
            expect(result).toEqual(unified);
        });

        it('should handle empty artifacts array', () => {
            (groupByImage as jest.Mock).mockReturnValue([]);

            const result = artifactService.unifyArtifacts([]);

            expect(groupByImage).toHaveBeenCalledWith([]);
            expect(result).toEqual([]);
        });
    });

    describe('groupArtifacts', () => {
        it('should call groupArtifactsByDuplicateIndex with artifacts and threshold', () => {
            const artifacts = [{ _id: '1' }, { _id: '2' }];
            const grouped = [['1', '2']];
            (groupArtifactsByDuplicateIndex as jest.Mock).mockReturnValue(grouped);

            const result = artifactService.groupArtifacts(artifacts, 0.7);

            expect(groupArtifactsByDuplicateIndex).toHaveBeenCalledWith(artifacts, 0.7);
            expect(result).toEqual(grouped);
        });

        it('should use default threshold of 0.7 when not provided', () => {
            const artifacts = [{ _id: '1' }];
            (groupArtifactsByDuplicateIndex as jest.Mock).mockReturnValue([]);

            artifactService.groupArtifacts(artifacts);

            expect(groupArtifactsByDuplicateIndex).toHaveBeenCalledWith(artifacts, 0.7);
        });

        it('should use custom threshold when provided', () => {
            const artifacts = [{ _id: '1' }];
            (groupArtifactsByDuplicateIndex as jest.Mock).mockReturnValue([]);

            artifactService.groupArtifacts(artifacts, 0.9);

            expect(groupArtifactsByDuplicateIndex).toHaveBeenCalledWith(artifacts, 0.9);
        });
    });

    describe('buildArtifactsQuery', () => {
        const allowedVessels = ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'];

        it('should build basic query with allowed vessels', () => {
            const result = artifactService.buildArtifactsQuery({ allowedVessels });

            expect(result.query).toMatchObject({
                'portal.is_archived': { $ne: true },
                location: { $ne: null },
                vessel_presence: true,
                super_category: { $ne: null },
            });
            expect(result.query.onboard_vessel_id).toBeDefined();
            expect(result.error).toBeUndefined();
        });

        it('should include excludeIds in query', () => {
            const excludeIds = ['507f1f77bcf86cd799439013'];
            const result = artifactService.buildArtifactsQuery({ allowedVessels, excludeIds });

            expect(result.query._id).toEqual({ $nin: excludeIds });
        });

        it('should handle filters with id', () => {
            const filters = { id: '507f1f77bcf86cd799439014' };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query._id).toBeDefined();
        });

        it('should handle filters with start_time and end_time', () => {
            const filters = {
                start_time: 1622547800,
                end_time: 1622547900,
            };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.timestamp).toBeDefined();
            expect(result.query.timestamp.$gt).toBeInstanceOf(Date);
            expect(result.query.timestamp.$lt).toBeInstanceOf(Date);
        });

        it('should handle filters with categories', () => {
            const filters = { categories: ['metal', 'plastic'] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.super_category).toEqual({ $in: ['metal', 'plastic'] });
        });

        it('should handle filters with colors when no searchQuery', () => {
            const filters = { colors: ['red', 'blue'] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.$and).toBeDefined();
            expect(result.query.$and.length).toBe(2);
        });

        it('should warn and skip colors filter when searchQuery is provided', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            const filters = { colors: ['red'] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters, searchQuery: 'test' });

            expect(consoleSpy).toHaveBeenCalledWith('/artifacts colors filter is not supported when searchQuery is provided');
            expect(result.query.$and).toBeUndefined();
            consoleSpy.mockRestore();
        });

        it('should handle filters with sizes when no searchQuery', () => {
            const filters = { sizes: ['small', 'large'] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.$or).toBeDefined();
            expect(result.query.$or.length).toBe(2);
        });

        it('should warn and skip sizes filter when searchQuery is provided', () => {
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
            const filters = { sizes: ['small'] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters, searchQuery: 'test' });

            expect(consoleSpy).toHaveBeenCalledWith('/artifacts sizes filter is not supported when searchQuery is provided');
            expect(result.query.$or).toBeUndefined();
            consoleSpy.mockRestore();
        });

        it('should handle filters with vessel_ids that are allowed', () => {
            const filters = { vessel_ids: [allowedVessels[0]] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.onboard_vessel_id).toBeDefined();
            expect(result.error).toBeUndefined();
        });

        it('should return error when vessel_ids are not allowed', () => {
            const filters = { vessel_ids: ['507f1f77bcf86cd799439099'] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            // The error is returned in query.error format
            expect(result.query.error).toBeDefined();
            expect(result.query.error?.status).toBe(403);
            expect(result.query.error?.message).toContain('Cannot access artifacts');
        });

        it('should handle filters with country_flags', () => {
            const filters = { country_flags: ['US', 'UK'] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.country_flag).toEqual({ $in: ['US', 'UK'] });
        });

        it('should handle filters with weapons array', () => {
            const filters = { weapons: ['gun', 'knife'] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.weapons).toEqual({ $in: ['gun', 'knife'] });
        });

        it('should handle filters with type video', () => {
            const filters = { type: 'video' };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.video_path).toEqual({ $ne: null });
        });

        it('should handle filters with type image', () => {
            const filters = { type: 'image' };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.video_path).toBeNull();
        });

        it('should handle filters with host_vessel true', () => {
            const filters = { host_vessel: true };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.host_vessel).toBe(true);
        });

        it('should handle filters with host_vessel false', () => {
            const filters = { host_vessel: false };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.host_vessel).toEqual({ $ne: true });
        });

        it('should handle filters with ais_filter true', () => {
            const filters = { ais_filter: true };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query['portal.ais_info.data']).toEqual({ $ne: null });
        });

        it('should handle filters with ais_filter false', () => {
            const filters = { ais_filter: false };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query['portal.ais_info.data']).toBeNull();
        });

        it('should handle filters with weaponized true', () => {
            const filters = { weaponized: true };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.weapons).toEqual({ $ne: null });
        });

        it('should handle empty colors array', () => {
            const filters = { colors: [] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.$and).toBeUndefined();
        });

        it('should handle empty sizes array', () => {
            const filters = { sizes: [] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.$or).toBeUndefined();
        });

        it('should handle empty weapons array', () => {
            const filters = { weapons: [] };
            const result = artifactService.buildArtifactsQuery({ allowedVessels, filters });

            expect(result.query.weapons).toBeUndefined();
        });
    });

    describe('fetchArtifacts', () => {
        const mockArtifacts = [
            {
                _id: '507f1f77bcf86cd799439011',
                image_path: 'path1.jpg',
                video_path: 'path1.mp4',
                thumbnail_image_path: 'thumb1.jpg',
                bucket_name: 'test-bucket',
                aws_region: 'us-east-1',
            },
            {
                _id: '507f1f77bcf86cd799439012',
                image_path: 'path2.jpg',
                bucket_name: 'test-bucket',
                aws_region: 'us-east-1',
            },
        ];

        beforeEach(() => {
            (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'mock-url' });
        });

        it('should fetch artifacts without searchQuery', async () => {
            const query = { 'portal.is_archived': { $ne: true } };
            const mockAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ total: 50 }] as never),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue(mockArtifacts as never),
                            }),
                        }),
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                aggregate: mockAggregate,
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
            });

            expect(result.artifacts).toHaveLength(2);
            expect(result.totalCount).toBe(50);
            expect(processBatchItem).toHaveBeenCalled();
        });

        it('should fetch artifacts with searchQuery', async () => {
            const query = { 'portal.is_archived': { $ne: true } };
            const searchIds = ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'];

            const searchSpy = jest.spyOn(artifactService, 'searchArtifactsIds').mockResolvedValue({ searchIds } as never);

            const mockSkip = jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                    toArray: jest.fn().mockResolvedValue(mockArtifacts as never),
                }),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: mockSkip,
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
                searchQuery: 'test query',
            });

            expect(result.artifacts).toHaveLength(2);
            expect(result.totalCount).toBeNull();
            expect(searchSpy).toHaveBeenCalled();
            expect(mockFind().sort).toHaveBeenCalledWith({});
            searchSpy.mockRestore();
        });

        it('should handle empty searchIds in searchQuery', async () => {
            const query = { 'portal.is_archived': { $ne: true } };

            const searchSpy = jest.spyOn(artifactService, 'searchArtifactsIds').mockResolvedValue({ searchIds: [] } as never);

            const mockSkip = jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                    toArray: jest.fn().mockResolvedValue([] as never),
                }),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: mockSkip,
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
                searchQuery: 'test query',
            });

            expect(result.artifacts).toHaveLength(0);
            expect(result.totalCount).toBeNull();
            expect(mockFind().sort).toHaveBeenCalledWith({});
            searchSpy.mockRestore();
        });

        it('should sort artifacts by searchIds order when searchQuery is provided', async () => {
            const query = { 'portal.is_archived': { $ne: true } };
            const searchIds = ['507f1f77bcf86cd799439012', '507f1f77bcf86cd799439011'];
            const artifacts = [
                { _id: '507f1f77bcf86cd799439011', image_path: 'path1.jpg', bucket_name: 'test', aws_region: 'us-east-1' },
                { _id: '507f1f77bcf86cd799439012', image_path: 'path2.jpg', bucket_name: 'test', aws_region: 'us-east-1' },
            ];

            const searchSpy = jest.spyOn(artifactService, 'searchArtifactsIds').mockResolvedValue({ searchIds } as never);

            const mockSkip = jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                    toArray: jest.fn().mockResolvedValue(artifacts as never),
                }),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: mockSkip,
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
                searchQuery: 'test query',
            });

            expect(result.artifacts[0]._id.toString()).toBe('507f1f77bcf86cd799439012');
            expect(result.artifacts[1]._id.toString()).toBe('507f1f77bcf86cd799439011');
            expect(mockFind().sort).toHaveBeenCalledWith({});
            expect(mockSkip).toHaveBeenCalledWith(0);
            searchSpy.mockRestore();
        });

        it('should handle missing IDs when searchQuery is provided', async () => {
            const query = { 'portal.is_archived': { $ne: true } };
            const searchIds = ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012', '507f1f77bcf86cd799439013'];
            // Only return 2 artifacts, one ID is missing
            const artifacts = [
                { _id: '507f1f77bcf86cd799439011', image_path: 'path1.jpg', bucket_name: 'test', aws_region: 'us-east-1' },
                { _id: '507f1f77bcf86cd799439012', image_path: 'path2.jpg', bucket_name: 'test', aws_region: 'us-east-1' },
            ];

            const searchSpy = jest.spyOn(artifactService, 'searchArtifactsIds').mockResolvedValue({ searchIds } as never);
            const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

            const mockSkip = jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                    toArray: jest.fn().mockResolvedValue(artifacts as never),
                }),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: mockSkip,
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
                searchQuery: 'test query',
            });

            expect(result.artifacts).toHaveLength(2);
            // Verify console.log was called for missing IDs
            expect(consoleSpy).toHaveBeenCalledWith(
                '[ArtifactService] Missing IDs:',
                expect.arrayContaining(['507f1f77bcf86cd799439013'])
            );
            consoleSpy.mockRestore();
            searchSpy.mockRestore();
        });

        it('should add signed URLs to artifacts with image_path', async () => {
            const query = { 'portal.is_archived': { $ne: true } };
            const artifacts = [
                { _id: '1', image_path: 'path1.jpg', bucket_name: 'test', aws_region: 'us-east-1' },
            ];

            const mockAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ total: 1 }] as never),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue(artifacts as never),
                            }),
                        }),
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                aggregate: mockAggregate,
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
            });

            expect(result.artifacts[0].image_url).toBe('mock-url');
            expect(processBatchItem).toHaveBeenCalledWith({
                bucketName: 'test',
                key: 'path1.jpg',
                region: 'us-east-1',
            });
        });

        it('should add signed URLs to artifacts with video_path', async () => {
            const query = { 'portal.is_archived': { $ne: true } };
            const artifacts = [
                { _id: '1', video_path: 'path1.mp4', bucket_name: 'test', aws_region: 'us-east-1' },
            ];

            const mockAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ total: 1 }] as never),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue(artifacts as never),
                            }),
                        }),
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                aggregate: mockAggregate,
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
            });

            expect(result.artifacts[0].video_url).toBe('mock-url');
        });

        it('should add signed URLs to artifacts with thumbnail_image_path', async () => {
            const query = { 'portal.is_archived': { $ne: true } };
            const artifacts = [
                { _id: '1', thumbnail_image_path: 'thumb1.jpg', bucket_name: 'test', aws_region: 'us-east-1' },
            ];

            const mockAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ total: 1 }] as never),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue(artifacts as never),
                            }),
                        }),
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                aggregate: mockAggregate,
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
            });

            expect(result.artifacts[0].thumbnail_url).toBe('mock-url');
        });

        it('should use custom projection when provided', async () => {
            const query = { 'portal.is_archived': { $ne: true } };
            const projection = { _id: 1, image_path: 1 };

            const mockAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ total: 1 }] as never),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue(mockArtifacts as never),
                            }),
                        }),
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                aggregate: mockAggregate,
                find: mockFind,
            });

            await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
                projection,
            });

            const projectCall = mockFind().sort().project;
            expect(projectCall).toHaveBeenCalledWith(projection);
        });

        it('should handle totalCount aggregation returning empty array', async () => {
            const query = { 'portal.is_archived': { $ne: true } };

            const mockAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([] as never),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: jest.fn().mockReturnValue({
                            limit: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([] as never),
                            }),
                        }),
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                aggregate: mockAggregate,
                find: mockFind,
            });

            const result = await artifactService.fetchArtifacts({
                query,
                page: 1,
                pageSize: 10,
            });

            expect(result.totalCount).toBe(0);
        });

        it('should calculate skip correctly based on page and pageSize', async () => {
            const query = { 'portal.is_archived': { $ne: true } };

            const mockAggregate = jest.fn().mockReturnValue({
                toArray: jest.fn().mockResolvedValue([{ total: 100 }] as never),
            });
            const mockSkip = jest.fn().mockReturnValue({
                limit: jest.fn().mockReturnValue({
                    toArray: jest.fn().mockResolvedValue([] as never),
                }),
            });
            const mockFind = jest.fn().mockReturnValue({
                sort: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        skip: mockSkip,
                    }),
                }),
            });

            (db.qmai.collection as jest.Mock).mockReturnValue({
                aggregate: mockAggregate,
                find: mockFind,
            });

            await artifactService.fetchArtifacts({
                query,
                page: 3,
                pageSize: 10,
            });

            expect(mockSkip).toHaveBeenCalledWith(20); // (3-1) * 10
        });
    });
});

