import { useEffect, useState } from "react";
import "video.js/dist/video-js.css";
import { Alert, alpha, Grid, Skeleton, Typography } from "@mui/material";
import { useApp } from "../../../hooks/AppHook";
import MainMap from "./MainMap";
import dayjs from "dayjs";
import { defaultValues } from "../../../utils";
import FloatingMenu from "../../../components/FloatingMenu";
import VesselList from "./VesselList";
import Filters from "./Filters";
import AdvancedSettings from "./AdvancedSettings";
import useVesselInfo from "../../../hooks/VesselInfoHook";
import { useLocation, useSearchParams } from "react-router-dom";
import theme from "../../../theme";
import { useUser } from "../../../hooks/UserHook";
import { logEvent } from "../../../utils";

const menuItems = [
    { title: "Vessels", imgSrc: "/icons/vessels-icon.svg", className: "map-step-1" },
    { title: "Filters", imgSrc: "/icons/filters-icon.svg", className: "map-step-3" },
    { title: "Advanced Settings", imgSrc: "/icons/advance-settings-icon.svg", className: "map-step-5" },
];

const FullMap = () => {
    const { isMobile, selectedVessel, setSelectedVessel, devMode, google } = useApp();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const { user } = useUser();
    const [journeyStart, setJourneyStart] = useState(defaultValues.journeyStart);
    const [journeyEnd, setJourneyEnd] = useState("now");

    const [interval, setInterval] = useState(defaultValues.interval);
    const [datapointsDistance, setDatapointsDistance] = useState(defaultValues.datapointsDistance);
    const [focusedVessel, setFocusedVessel] = useState();

    const [vessels, setVessels] = useState([]);
    const [selectedVessels, setSelectedVessels] = useState([]);

    const [loadingVessels, setLoadingVessels] = useState([]);
    const [availableVessels, setAvailableVessels] = useState([]);
    const [emptyVessels, setEmptyVessels] = useState([]);
    const [errorsomeVessels, setErrorsomeVessels] = useState([]);

    const [artifactsCategories, setArtifactsCategories] = useState();
    const [selectedArtifactsCategory, setSelectedArtifactsCategory] = useState([]);

    const [disableFiltersReset, setDisableFiltersReset] = useState(true);
    const [disableAdvancedSettingsReset, setDisableAdvancedSettingsReset] = useState(true);

    const [timeSlider, setTimeSlider] = useState([journeyStart.valueOf(), dayjs().valueOf()]);
    const [timeSliderKey, setTimeSliderKey] = useState(0);

    const [showDatapoints, setShowDatapoints] = useState(true);
    const [showArtifacts, setShowArtifacts] = useState(true);
    const [showAisData, setShowAisData] = useState(true);
    const [showAudioData, setShowAudioData] = useState(true);
    const [showEEZLayers, setShowEEZLayers] = useState(false);
    const [showPortsLayers, setShowPortsLayers] = useState(false);
    const [loadingEEZLayers, setLoadingEEZLayers] = useState(false);
    const [homePortsArtifactsMode, setHomePortsArtifactsMode] = useState(user?.home_port_filter_mode || "ONLY_NON_HOME_PORTS");
    const [activeComponentTitle, setActiveComponentTitle] = useState(null);
    const [mapError, setMapError] = useState("");
    const [selectedNumberOfArtifacts, setSelectedNumberOfArtifacts] = useState("all");
    const { pathname } = useLocation();
    const [searchParams] = useSearchParams();
    const [artifactsType, setArtifactsType] = useState("both");
    const [showFilteredCoordinates, setShowFilteredCoordinates] = useState(false);
    const [fromDate, setFromDate] = useState(defaultValues.journeyStart);
    const [toDate, setToDate] = useState(dayjs(defaultValues.journeyStart).add(1, "hour"));
    const [showCoordinatesPoints, setShowCoordinatesPoints] = useState(true);
    const [targetArtifactId, setTargetArtifactId] = useState(null);
    const [aisArtifactsFilter, setAisArtifactsFilter] = useState("both");
    const [totalArtifactsCount, setTotalArtifactsCount] = useState(0);

    useEffect(() => {
        const start = dayjs(journeyStart);
        const end = journeyEnd === "now" ? dayjs() : dayjs(journeyEnd);
        const differenceInDays = end.diff(start, "day");
        const differenceInMinutes = differenceInDays * 24 * 60;
        const interval = Math.min(60, Math.floor(differenceInMinutes / 280));

        setInterval(interval);
    }, [journeyStart, journeyEnd]);

    useEffect(() => {
        setHomePortsArtifactsMode(user?.home_port_filter_mode || "ONLY_NON_HOME_PORTS");
    }, [user?.home_port_filter_mode]);

    useEffect(() => {
        if (!pathname.includes("/map")) {
            setSelectedNumberOfArtifacts("all");
        }
    }, [pathname]);

    // When navigating to the map from events page modal location coordinates
    useEffect(() => {
        const artifactId = searchParams.get("artifact");
        if (artifactId) {
            setTargetArtifactId(artifactId);
        }
    }, [searchParams, pathname]);

    useEffect(() => {
        if (selectedVessel) {
            const isAvailable = availableVessels.some((id) => id === selectedVessel);
            const isNotAvailable = emptyVessels.concat(errorsomeVessels).some((id) => id === selectedVessel);

            if (isAvailable) {
                setFocusedVessel(selectedVessel);
                setSelectedVessel(null);
            } else if (isNotAvailable) {
                setSelectedVessel(null);
            }
        }
    }, [availableVessels, emptyVessels, errorsomeVessels]);

    // Comment from Mahsam: DO NOT DO THIS. THIS FORCES THE SHOW ARTIFACTS VALUE WITHOUT USER INTERACTION
    // Clear artifacts on map when all vessels are unselected
    // useEffect(() => {
    //     if (selectedVessels.length === 0) {
    //         setShowArtifacts(false);
    //         setLoadingVessels([]);
    //         setAvailableVessels([]);
    //         setEmptyVessels([]);
    //         setErrorsomeVessels([]);
    //     } else {
    //         setShowArtifacts(true);
    //     }
    // }, [selectedVessels]);

    // This is used for redirecting to the map when a vessel is selected from the streams map
    useEffect(() => {
        if (selectedVessel) {
            const vessel = Array.isArray(vessels) && vessels.find((vessel) => vessel.id === selectedVessel);
            if (vessel) setSelectedVessels([vessel]);
            if (selectedVessels.some((vessel) => vessel.id === selectedVessel)) {
                const isAvailable = availableVessels.some((id) => id === selectedVessel);
                if (isAvailable) {
                    setFocusedVessel(selectedVessel);
                }
                setSelectedVessel(null);
            }
        }
    }, [selectedVessel]);

    useEffect(() => {
        setVessels();
        setFocusedVessel();
    }, []);

    useEffect(() => {
        if (vesselInfo && vesselInfo.length > 0) {
            fetchStreams();
        }
    }, [vesselInfo]);

    useEffect(() => {
        if (!vessels || !Array.isArray(vessels) || vessels.length === 0) return setSelectedVessels([]);
        setSelectedVessels([vessels[0]]);
    }, [vessels]);

    useEffect(() => {
        if (availableVessels.length === 0) return;
        if (focusedVessel !== undefined && (!focusedVessel || !availableVessels.some((id) => id === focusedVessel)))
            setFocusedVessel(availableVessels[0]);
    }, [availableVessels]);

    useEffect(() => {
        if (journeyStart === defaultValues.journeyStart && journeyEnd === "now") setDisableFiltersReset(true);
        else setDisableFiltersReset(false);
    }, [journeyStart, journeyEnd]);

    useEffect(() => {
        if (interval === defaultValues.interval && datapointsDistance === defaultValues.datapointsDistance)
            setDisableAdvancedSettingsReset(true);
        else setDisableAdvancedSettingsReset(false);
    }, [interval, datapointsDistance]);

    const fetchStreams = async () => {
        setMapError("");
        try {
            if (vesselInfo && Array.isArray(vesselInfo)) {
                const vesselsList = vesselInfo.filter((vessel) => (devMode ? true : vessel.is_active));
                if (vesselsList.length === 0) {
                    setMapError("No vessel coordinates found.");
                } else {
                    setMapError("");
                    setVessels(
                        vesselsList
                            .sort((a, b) => (a.is_live && !b.is_live ? -1 : 1))
                            .map((vessel) => ({
                                id: vessel.vessel_id,
                                unit_id: vessel.unit_id,
                                name: vessel.name || "Unregistered",
                                region_group: vessel.region_group_id,
                                vessel_id: vessel.vessel_id,
                            })),
                    );
                }
            } else {
                fetchVesselsInfo();
            }
        } catch (err) {
            console.error("Error fetching streams in FullMap", err);
            setMapError("Failed to fetch vessels coordinates. Please try again later. " + err?.response?.data?.message);
        }
    };

    const handleVesselSelect = (vessel) => {
        const selected = selectedVessels.some((_vessel) => _vessel.id === vessel.id);
        logEvent("VesselSelected", { vesselId: vessel.id, selected: !selected });
        setSelectedVessels((v) => {
            if (v.some((_vessel) => _vessel.id === vessel.id)) return v.filter((_vessel) => _vessel.id !== vessel.id);
            else return [...v, vessel];
        });
    };

    const resetFilters = () => {
        setJourneyStart(defaultValues.journeyStart);
        setJourneyEnd("now");
        setTimeSlider([defaultValues.journeyStart.valueOf(), dayjs().valueOf()]);
        setTimeSliderKey((v) => v + 1);
        setHomePortsArtifactsMode(user?.home_port_filter_mode || "ONLY_NON_HOME_PORTS");
        setAisArtifactsFilter("both");
    };

    const resetAdvancedSettings = () => {
        setInterval(defaultValues.interval);
        setDatapointsDistance(defaultValues.datapointsDistance);
    };

    const handleMenuUpdate = (title) => {
        setActiveComponentTitle(title);
    };

    const componentMap = {
        "Advanced Settings": (
            <AdvancedSettings
                interval={interval}
                datapointsDistance={datapointsDistance}
                disableAdvancedSettingsReset={disableAdvancedSettingsReset}
                resetAdvancedSettings={resetAdvancedSettings}
                setInterval={setInterval}
                setDatapointsDistance={setDatapointsDistance}
                showFilteredCoordinates={showFilteredCoordinates}
                setShowFilteredCoordinates={setShowFilteredCoordinates}
                fromDate={fromDate}
                setFromDate={setFromDate}
                toDate={toDate}
                setToDate={setToDate}
                showCoordinatesPoints={showCoordinatesPoints}
                setShowCoordinatesPoints={setShowCoordinatesPoints}
            />
        ),
        Filters: (
            <Filters
                journeyStart={journeyStart}
                journeyEnd={journeyEnd}
                timeSlider={timeSlider}
                timeSliderKey={timeSliderKey}
                selectedArtifactsCategory={selectedArtifactsCategory}
                showDatapoints={showDatapoints}
                showArtifacts={showArtifacts}
                showAisData={showAisData}
                showAudioData={showAudioData}
                homePortsArtifactsMode={homePortsArtifactsMode}
                disableFiltersReset={disableFiltersReset}
                artifactsCategories={artifactsCategories}
                setJourneyStart={setJourneyStart}
                setJourneyEnd={setJourneyEnd}
                setTimeSlider={setTimeSlider}
                setTimeSliderKey={setTimeSliderKey}
                setSelectedArtifactsCategory={setSelectedArtifactsCategory}
                setShowDatapoints={setShowDatapoints}
                setShowArtifacts={setShowArtifacts}
                setShowAisData={setShowAisData}
                setShowAudioData={setShowAudioData}
                setHomePortsArtifactsMode={setHomePortsArtifactsMode}
                resetFilters={resetFilters}
                loadingVessels={loadingVessels}
                selectedNumberOfArtifacts={selectedNumberOfArtifacts}
                setSelectedNumberOfArtifacts={setSelectedNumberOfArtifacts}
                artifactsType={artifactsType}
                setArtifactsType={setArtifactsType}
                aisArtifactsFilter={aisArtifactsFilter}
                setAisArtifactsFilter={setAisArtifactsFilter}
                setShowEEZLayers={setShowEEZLayers}
                showEEZLayers={showEEZLayers}
                setShowPortsLayers={setShowPortsLayers}
                showPortsLayers={showPortsLayers}
                loadingEEZLayers={loadingEEZLayers}
                totalArtifactsCount={totalArtifactsCount}
            />
        ),
        Vessels: (
            <VesselList
                vessels={vessels}
                selectedVessels={selectedVessels}
                availableVessels={availableVessels}
                emptyVessels={emptyVessels}
                errorsomeVessels={errorsomeVessels}
                loadingVessels={loadingVessels}
                handleVesselSelect={handleVesselSelect}
                setFocusedVessel={setFocusedVessel}
                focusedVessel={focusedVessel}
            />
        ),
    };

    return (
        <Grid
            container
            height={"100%"}
            flexWrap={"nowrap"}
            overflow={"auto"}
            flexDirection={{ xs: "column", lg: "row" }}
            color={"#FFFFFF"}
            position={"relative"}
        >
            <Grid minHeight={{ xs: 400, lg: "auto" }} position="relative" size="grow">
                {!vessels || !google ? (
                    <Skeleton animation="wave" variant="rectange" width={"100%"} height={"100%"} />
                ) : (
                    <MainMap
                        setAvailableVessels={setAvailableVessels}
                        setEmptyVessels={setEmptyVessels}
                        setErrorsomeVessels={setErrorsomeVessels}
                        focusedVessel={focusedVessel}
                        datapointsDistance={datapointsDistance}
                        vessels={selectedVessels}
                        journeyStart={journeyStart}
                        journeyEnd={journeyEnd}
                        initialZoom={3}
                        interval={interval}
                        setArtifactsCategories={setArtifactsCategories}
                        selectedArtifactsCategory={selectedArtifactsCategory}
                        timeSlider={timeSlider}
                        showDatapoints={showDatapoints}
                        showArtifacts={showArtifacts}
                        showAisData={showAisData}
                        showEEZLayers={showEEZLayers}
                        showPortsLayers={showPortsLayers}
                        showAudioData={showAudioData}
                        setLoadingEEZLayers={setLoadingEEZLayers}
                        setLoadingVessels={setLoadingVessels}
                        loadingVessels={loadingVessels}
                        emptyVessels={emptyVessels}
                        errorsomeVessels={errorsomeVessels}
                        selectedNumberOfArtifacts={selectedNumberOfArtifacts}
                        setSelectedNumberOfArtifacts={setSelectedNumberOfArtifacts}
                        artifactsType={artifactsType}
                        showFilteredCoordinates={showFilteredCoordinates}
                        fromDate={fromDate}
                        toDate={toDate}
                        showCoordinatesPoints={showCoordinatesPoints}
                        setSelectedArtifactsCategory={setSelectedArtifactsCategory}
                        homePortsArtifactsMode={homePortsArtifactsMode}
                        targetArtifactId={targetArtifactId}
                        setTargetArtifactId={setTargetArtifactId}
                        aisArtifactsFilter={aisArtifactsFilter}
                        setTotalArtifactsCount={setTotalArtifactsCount}
                        clearUrlParameter={() => {
                            const newSearchParams = new URLSearchParams(searchParams);
                            newSearchParams.delete("artifact");
                            const newUrl = `${pathname}${newSearchParams.toString() ? "?" + newSearchParams.toString() : ""}`;
                            window.history.replaceState({}, "", newUrl);
                        }}
                    />
                )}
                {mapError && (
                    <Grid sx={{ position: "absolute", bottom: 16, left: 16, zIndex: 10, display: "flex" }} size="grow">
                        <Alert severity="error">{mapError}</Alert>
                    </Grid>
                )}
            </Grid>
            <FloatingMenu menuItems={menuItems} activeComponentTitle={activeComponentTitle} onMenuUpdate={handleMenuUpdate} withPadding={false}>
                {!isMobile
                    ? componentMap[activeComponentTitle] || null
                    : menuItems.map((item, index) => (
                        <Grid
                            key={index}
                            container
                            display={{ xs: "flex", lg: "none" }}
                            flexDirection={"column"}
                            gap={2}
                            paddingTop={4}
                            paddingX={1.2}
                            sx={{
                                backgroundColor: theme.palette.custom.darkBlue,
                                paddingBottom: 1.2,
                                position: "relative",
                                maxWidth: "calc(100vw - 10px)",
                            }}
                        >
                            <Grid size={12}>
                                <Typography fontSize={"16px"} fontWeight={"600"} color={"#FFFFFF"}>
                                    {item.title}
                                </Typography>
                            </Grid>
                            <Grid
                                sx={{
                                    backgroundColor: alpha(theme.palette.primary.light, 0.5),
                                    borderRadius: "10px",
                                    padding: item.title === "Vessels" ? 0 : "15px 20px",
                                }}
                            >
                                {componentMap[item.title]}
                            </Grid>
                        </Grid>
                    ))}
            </FloatingMenu>
        </Grid>
    );
};

export default FullMap;
