import { useEffect, useState, useRef, useCallback, memo } from "react";
import { Grid, CircularProgress, Typography } from "@mui/material";
import { Assessment } from "@mui/icons-material";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import VirtualizedCardList from "./VirtualizedCardList";
import EvaluatedCard from "./EvaluatedCard";
import { getSocket } from "../../../socket";
import DetailModal from "./DetailModal";
import theme from "../../../theme";
import FilterEvaluatedArtifactsModal from "./FilterEvaluatedArtifactsModal";

const EvaluatedArtifacts = ({ showFilterModal, setShowFilterModal }) => {
    const [evaluatedArtifacts, setEvaluatedArtifacts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedArtifact, setSelectedArtifact] = useState(null);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedEvaluationTypes, setSelectedEvaluationTypes] = useState([]);
    const containerRef = useRef();
    const listRef = useRef();
    const hasMore = useRef(true);
    const pageRef = useRef(1);
    const loadMoreTimeoutRef = useRef();
    const isLoadingMoreRef = useRef(false);
    const totalLoadedRef = useRef(0);

    const evaluationTypeOptions = [
        { value: "both_matched", label: "Both Matched" },
        { value: "detection_not_matched", label: "Detection Not Matched" },
        { value: "ais_not_matched", label: "AIS Not Matched" },
        { value: "both_not_matched", label: "Both Not Matched" },
    ];

    const fetchEvaluatedArtifacts = useCallback(
        async (isSocketTriggered = false, isLoadMore = false, evaluationTypesOverride = null) => {
            if (isLoadMore && isLoadingMoreRef.current) {
                return;
            }

            if (isLoadMore && !hasMore.current) {
                return;
            }

            if (!isSocketTriggered) {
                setLoading(true);
            }

            if (isLoadMore) {
                isLoadingMoreRef.current = true;
            }

            try {
                const currentPage = isLoadMore ? pageRef.current + 1 : 1;
                const pageSize = 30;
                const evaluationTypesToUse = evaluationTypesOverride !== null ? evaluationTypesOverride : selectedEvaluationTypes;
                const response = await artifactFlagController.getEvaluatedArtifacts(
                    currentPage,
                    pageSize,
                    evaluationTypesToUse.length > 0 ? evaluationTypesToUse : undefined,
                );
                const { artifacts, totalCount, page: responsePage, pageSize: responsePageSize } = response;

                if (artifacts.length === 0) {
                    if (isLoadMore) {
                        hasMore.current = false;
                    } else {
                        setEvaluatedArtifacts([]);
                        pageRef.current = 1;
                        totalLoadedRef.current = 0;
                        hasMore.current = false;
                    }
                    setLoading(false);
                    isLoadingMoreRef.current = false;
                    return;
                }


                if (isLoadMore) {
                    setEvaluatedArtifacts((prev) => {
                        const newList = [...prev, ...artifacts];
                        totalLoadedRef.current = newList.length;
                        return newList;
                    });
                    pageRef.current = responsePage || currentPage;
                } else {
                    setEvaluatedArtifacts(artifacts);
                    pageRef.current = responsePage || 1;
                    totalLoadedRef.current = artifacts.length;
                }

                const gotFullPage = artifacts.length >= responsePageSize;
                const currentTotal = isLoadMore ? totalLoadedRef.current : artifacts.length;
                hasMore.current = gotFullPage && currentTotal < totalCount;

                setLoading(false);
                isLoadingMoreRef.current = false;
            } catch (error) {
                console.error("Error fetching evaluated artifacts:", error);
                setLoading(false);
                isLoadingMoreRef.current = false;
            }
        },
        [selectedEvaluationTypes],
    );

    useEffect(() => {
        fetchEvaluatedArtifacts();
    }, []);

    const handleLoadMore = useCallback(() => {
        if (loading || !hasMore.current || isLoadingMoreRef.current) {
            return;
        }

        if (loadMoreTimeoutRef.current) {
            clearTimeout(loadMoreTimeoutRef.current);
        }

        loadMoreTimeoutRef.current = setTimeout(() => {
            fetchEvaluatedArtifacts(false, true);
        }, 500);
    }, [loading, fetchEvaluatedArtifacts]);

    useEffect(() => {
        const socket = getSocket();

        const handleArtifactChanged = () => {
            fetchEvaluatedArtifacts(true, false);
        };

        socket.on("artifact/changed", handleArtifactChanged);

        return () => {
            socket.off("artifact/changed", handleArtifactChanged);
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }
        };
    }, [fetchEvaluatedArtifacts]);

    const handleFlaggedByClick = useCallback((artifact) => {
        setSelectedArtifact(artifact);
        setShowDetailModal(true);
    }, []);


    const handleApplyFilters = useCallback((newEvaluationTypes) => {
        pageRef.current = 1;
        totalLoadedRef.current = 0;
        hasMore.current = true;
        fetchEvaluatedArtifacts(false, false, newEvaluationTypes);
    }, [fetchEvaluatedArtifacts]);

    return (
        <>
            <FilterEvaluatedArtifactsModal
                showFilterModal={showFilterModal}
                setShowFilterModal={setShowFilterModal}
                selectedEvaluationTypes={selectedEvaluationTypes}
                setSelectedEvaluationTypes={setSelectedEvaluationTypes}
                evaluationTypeOptions={evaluationTypeOptions}
                onApply={handleApplyFilters}
            />
            {loading && evaluatedArtifacts.length === 0 ? (
                <Grid
                    container
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                    height={{ xs: "90%", sm: "90%" }}
                    overflow={"auto"}
                    marginBottom={2}
                    size="grow"
                >
                    <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
                </Grid>
            ) : evaluatedArtifacts.length === 0 ? (
                <Grid
                    container
                    padding={6}
                    justifyContent={"center"}
                    alignItems={"center"}
                    flexDirection={"column"}
                    gap={2}
                    size="grow"
                >
                    <Assessment sx={{ fontSize: "64px", color: theme.palette.custom.mainBlue, opacity: 0.5 }} />
                    <Typography fontSize={"18px"} fontWeight={500} color="#FFFFFF">
                        No evaluated artifacts found
                    </Typography>
                    <Typography fontSize={"14px"} color={theme.palette.custom.mainBlue} textAlign={"center"}>
                        {selectedEvaluationTypes.length > 0
                            ? "Try adjusting your filters to see more results"
                            : "Artifacts with AIS discrepancy evaluations will appear here"}
                    </Typography>
                </Grid>
            ) : (
                <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
                    <Grid
                        container
                        overflow={"auto"}
                        display={"block"}
                        border={`1px solid ${theme.palette.custom.borderColor}`}
                        borderRadius={"12px"}
                        padding={"10px 24px"}
                        size="grow"
                        sx={{
                            backgroundColor: theme.palette.custom.darkBlue,
                        }}
                    >
                        <Grid container height={"100%"} overflow={"hidden"} ref={containerRef}>
                            <VirtualizedCardList
                                ref={listRef}
                                events={evaluatedArtifacts}
                                isLoading={loading}
                                onLoadMore={handleLoadMore}
                                hasMore={hasMore.current}
                                containerRef={containerRef}
                                setShowDetailModal={setShowDetailModal}
                                setSelectedCard={setSelectedArtifact}
                                CustomCard={EvaluatedCard}
                                onFlaggedByClick={handleFlaggedByClick}
                            />
                        </Grid>
                    </Grid>
                    <DetailModal
                        showDetailModal={showDetailModal}
                        setShowDetailModal={setShowDetailModal}
                        selectedCard={selectedArtifact}
                        setSelectedCard={setSelectedArtifact}
                    />
                </Grid>
            )}
        </>
    );
};

export default memo(EvaluatedArtifacts);

