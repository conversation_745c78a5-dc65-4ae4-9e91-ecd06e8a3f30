import db from "../modules/db";
import { IQueryFilter } from "../interfaces/Common";
import { processSearchQuery } from "../utils/functions";
import { generateOpenAIEmbedding } from "../modules/openai";

class ArtifactService {
    async searchArtifactsIds({
        searchQuery,
        filterQuery,
        skip,
        limit,
    }: {
        searchQuery: string;
        filterQuery: IQueryFilter;
        skip: number;
        limit: number;
    }): Promise<{ searchIds: string[] }> {
        const processedSearchQuery = searchQuery ? processSearchQuery(searchQuery) : undefined;
        if (!processedSearchQuery) return { searchIds: [] };

        const fetchN = skip + limit;
        const searchQueryEmbedding = await generateOpenAIEmbedding({ input: processedSearchQuery });

        // console.log("query", JSON.stringify(filterQuery, null, 2));
        // const { must, mustNot } = matchToSearchFilter(filterQuery);

        // const searchCompound = {
        //     must: [
        //         {
        //             text: {
        //                 query: processedSearchQuery,
        //                 path: { wildcard: "*" }, // all indexed string fields
        //                 fuzzy: {
        //                     maxEdits: 2, // 1 = small typos, 2 = more forgiving
        //                     prefixLength: 1, // first N chars must match exactly
        //                     maxExpansions: 50, // how many variants to explore
        //                 },
        //             },
        //         },
        //     ],
        //     mustNot,
        //     filter: must,
        // };

        // const totalCount =
        // (
        //     await db.searchArtifacts
        //         .collection("analyzed")
        //         .aggregate([
        //             {
        //                 $vectorSearch: {
        //                     index: "artifacts_vector_index",
        //                     path: "embedding",
        //                     queryVector: searchQueryEmbedding,
        //                     numCandidates: fetchN * 5,
        //                     limit: fetchN,
        //                     filter: filterQuery,
        //                 },
        //             },
        //             { $count: "totalCount" },
        //         ])
        //         .toArray()
        // )[0]?.totalCount ?? 0;

        // if (!totalCount) return { totalCount: 0, searchIds: [] };

        const pipeline = [
            {
                // $search: {
                //     index: "artifacts_search_index",
                //     text: {
                //         query: processedSearchQuery,
                //         path: { wildcard: "*" }, // all indexed string fields
                //         fuzzy: {
                //             maxEdits: 2, // 1 = small typos, 2 = more forgiving
                //             prefixLength: 1, // first N chars must match exactly
                //             maxExpansions: 50, // how many variants to explore
                //         },
                //     },
                // },

                //     $rankFusion: {
                //         input: {
                //             pipelines: {
                //                 vectorPipeline: [
                //                     {
                //                         $vectorSearch: {
                //                             index: "artifacts_vector_index",
                //                             path: "embedding",
                //                             queryVector: searchQueryEmbedding,
                //                             numCandidates: fetchN * 5,
                //                             limit: fetchN,
                //                             filter: filterQuery,
                //                         },
                //                     },
                //                 ],
                //                 fullTextPipeline: [
                //                     {
                //                         $search: {
                //                             index: "artifacts_search_index",
                //                             compound: searchCompound,
                //                         },
                //                     },
                //                     { $limit: fetchN },
                //                 ],
                //             },
                //         },
                //         combination: {
                //             weights: {
                //                 vectorPipeline: 0.5,
                //                 fullTextPipeline: 0.5,
                //             },
                //         },
                //         scoreDetails: true,
                //     },
                // },

                $vectorSearch: {
                    index: "artifacts_vector_index",
                    path: "embedding",
                    queryVector: searchQueryEmbedding,
                    numCandidates: fetchN * 5,
                    limit: fetchN,
                    filter: filterQuery,
                },
            },
            {
                $project: {
                    _id: 1,
                    // scoreDetails: { $meta: "scoreDetails" },
                    score: { $meta: "score" },
                },
            },
            { $match: { score: { $gte: 0.7 } } },
            { $sort: { score: -1 } },
            { $skip: skip },
            { $limit: limit },
        ];

        // console.log("pipeline", JSON.stringify(pipeline, null, 2));

        const searchIds = await db.searchArtifacts.collection("analyzed").aggregate(pipeline).toArray();
        // console.log("searchIds", JSON.stringify(searchIds, null, 2));
        console.log(
            `[searchIds] q: ${processedSearchQuery}; highest textScore: ${JSON.stringify(searchIds[0])}, lowest textScore: ${JSON.stringify(searchIds[searchIds.length - 1])}`,
        );

        return { searchIds: searchIds.map((id) => id._id) };
    }
}

export default new ArtifactService();
