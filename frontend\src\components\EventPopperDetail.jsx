import { ClickAwayListener, Grid, Paper, Popper, Tooltip, Typography, alpha, Box, IconButton, CircularProgress } from "@mui/material";
import React, { useEffect, useState, useMemo } from "react";
import FlagIcon from "@mui/icons-material/Flag";
import OutlinedFlagIcon from "@mui/icons-material/OutlinedFlag";
import aisVesselFlagController from "../controllers/AisVesselFlag.controller";

const EventPopperDetail = ({ open, anchorEl, placement, offset, setPanelOpen, details, fieldWidth, containerRef, title }) => {
    const [arrowRef, setArrowRef] = useState(null);
    const [isFlagged, setIsFlagged] = useState(false);
    const [isFlagging, setIsFlagging] = useState(false);

    const mmsi = useMemo(() => {
        if (title === "AIS " && details) {
            const mmsiDetail = details.find(d => d.label === "MMSI");
            return mmsiDetail?.value;
        }
        return null;
    }, [title, details]);

    useEffect(() => {
        const loadFlaggedStatus = async () => {
            if (mmsi) {
                try {
                    const flagged = aisVesselFlagController.isAisVesselFlaggedByUser(mmsi);
                    setIsFlagged(flagged);
                } catch (error) {
                    console.error("Error loading flagged status:", error);
                }
            }
        };
        loadFlaggedStatus();
    }, [mmsi, aisVesselFlagController.version]);

    const handleFlagClick = async (e) => {
        e.stopPropagation();
        if (!mmsi) return;
        setIsFlagging(true);
        try {
            if (isFlagged) {
                await aisVesselFlagController.unflagAisVessel(mmsi);
                setIsFlagged(false);
            } else {
                await aisVesselFlagController.flagAisVessel(mmsi);
                setIsFlagged(true);
            }
        } catch (error) {
            console.error("Error flagging AIS vessel:", error);
        } finally {
            setIsFlagging(false);
        }
    };

    return (
        <Popper
            open={open && Boolean(anchorEl)}
            anchorEl={anchorEl}
            placement={placement}
            disablePortal
            strategy="fixed" // stays with the dot if container scrolls
            style={{ zIndex: 1500, }}
            modifiers={[
                { name: "flip", enabled: true },
                {
                    name: "preventOverflow",
                    enabled: true,
                    options: {
                        boundary: containerRef.current || "clippingParents",
                        altBoundary: true,
                        tether: true,
                        padding: 8,
                    },
                },
                { name: "offset", options: { offset: offset } },
                {
                    name: "arrow",
                    enabled: true,
                    options: {
                        element: arrowRef,
                    },
                },
            ]}
        >
            <Box sx={{ position: "relative" }}>
                <Box
                    ref={setArrowRef}
                    sx={{
                        position: "absolute",
                        width: 0,
                        height: 0,
                        borderStyle: "solid",
                        zIndex: -1,
                        '&[data-popper-placement*="bottom"]': {
                            top: 0,
                            left: 0,
                            marginTop: "-8px",
                            borderWidth: "0 8px 8px 8px",
                            borderColor: (theme) => `transparent transparent ${alpha(theme.palette.primary.light, 0.5)} transparent`,
                        },
                        '&[data-popper-placement*="top"]': {
                            bottom: 0,
                            left: 0,
                            marginBottom: "-8px",
                            borderWidth: "8px 8px 0 8px",
                            borderColor: (theme) => `${alpha(theme.palette.primary.light, 0.5)} transparent transparent transparent`,
                        },
                        '&[data-popper-placement*="right"]': {
                            left: 0,
                            marginLeft: "-8px",
                            borderWidth: "8px 8px 8px 0",
                            borderColor: (theme) => `transparent ${alpha(theme.palette.primary.light, 0.5)} transparent transparent`,
                        },
                        '&[data-popper-placement*="left"]': {
                            right: 0,
                            marginRight: "-8px",
                            borderWidth: "8px 0 8px 8px",
                            borderColor: (theme) => `transparent transparent transparent ${alpha(theme.palette.primary.light, 0.5)}`,
                        },
                    }}
                />
                <ClickAwayListener onClickAway={() => setPanelOpen(false)}>
                    <Paper elevation={4} sx={{
                        p: 0.5,
                        backgroundColor: (theme) => alpha(theme.palette.primary.light, 0.5),

                        maxHeight: "300px", maxWidth: "270px", overflowY: "auto"
                    }}>
                        <Grid>
                            <Grid container justifyContent="space-between" alignItems="center" paddingX={1}>
                                <Typography sx={{ color: "white" }} variant="subtitle2" fontWeight={600}>
                                    {title} Details
                                </Typography>
                                {title === "AIS " && mmsi && (
                                    <Tooltip
                                        title={!isFlagging ? (isFlagged ? "Remove AIS flag" : "Flag AIS") : null}
                                        arrow
                                        placement="left"
                                    >
                                        <IconButton
                                            size="small"
                                            sx={{
                                                height: 24,
                                                width: 24,
                                                padding: 0,
                                                color: "#fff",
                                                backgroundColor: isFlagged ? "#E60000CC" : "rgba(0,0,0,0.5)",
                                                borderRadius: "50%",
                                                "&:hover": {
                                                    backgroundColor: isFlagged ? "#E60000" : "rgba(0,0,0,0.7)",
                                                },
                                            }}
                                            onClick={handleFlagClick}
                                            disabled={isFlagging}
                                        >
                                            {isFlagging ? (
                                                <CircularProgress sx={{ color: "white" }} size={14} />
                                            ) : isFlagged ? (
                                                <FlagIcon sx={{ height: 14 }} />
                                            ) : (
                                                <OutlinedFlagIcon sx={{ height: 14 }} />
                                            )}
                                        </IconButton>
                                    </Tooltip>
                                )}
                            </Grid>
                            <Grid container>
                                {details.map(({ label, value, show }, index) => (
                                    <React.Fragment key={index} >
                                        <Grid
                                            display={!show ? "flex" : "flex"}
                                            alignItems={{
                                                xs: "flex-start",
                                                sm: "flex-start",
                                            }}
                                            paddingX={1}
                                            flexDirection="column"
                                            size={fieldWidth.includes(label) ? 12 : 5.9}
                                        >
                                            <Typography
                                                fontSize="12px"
                                                fontWeight={"bold"}
                                                color={"#FFFFFF"}

                                                sx={{ display: "flex", alignItems: "center", gap: "6px" }}
                                            >
                                                {label}
                                                {label === "Bearing Angle" && (
                                                    <Tooltip title="Angle measured clockwise between the True North and the Target as observed from own vessel">
                                                        <img src="/icons/info_icon.svg" />
                                                    </Tooltip>
                                                )}
                                            </Typography>
                                            <Typography
                                                fontSize="12px"
                                                fontWeight={400}
                                                color={"#FFFFFF"}
                                            >
                                                {value ?? "--"}
                                            </Typography>
                                        </Grid>
                                    </React.Fragment>
                                ))}
                            </Grid>
                        </Grid>
                    </Paper>
                </ClickAwayListener>
            </Box>
        </Popper>

    )
}

export default EventPopperDetail;