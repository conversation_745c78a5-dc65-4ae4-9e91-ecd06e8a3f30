import { Grid, Typography, Tooltip } from "@mui/material";
import { memo, useMemo, useEffect, useState } from "react";
import dayjs from "dayjs";
import { displayCoordinates, helperIconModes, userValues } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";

const EvaluatedCard = ({ card, onFlaggedByClick }) => {
    const [src, setSrc] = useState(null);
    const { user } = useUser();
    const [thumbnail, setThumbnail] = useState(null);
    const { vesselInfo } = useVesselInfo();
    const isVideo = Boolean(card?.artifact?.video_path);

    const roundedCoordinates = useMemo(
        () => displayCoordinates(card?.artifact?.location?.coordinates, !!user?.use_MGRS),
        [card?.artifact?.location?.coordinates, user?.use_MGRS],
    );

    const vessel = vesselInfo.find((v) => v.vessel_id === card?.artifact?.onboard_vessel_id);
    const vesselName = vessel?.name || "Unknown Vessel";

    useEffect(() => {
        if (card?.artifact) {
            const thumbnailUrl = card?.artifact?.thumbnail_url;
            const imageUrl = card?.artifact?.image_url;
            const videoUrl = card?.artifact?.video_url;

            if (isVideo) {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(videoUrl || null);
            } else {
                setThumbnail(thumbnailUrl || imageUrl || null);
                setSrc(imageUrl || null);
            }
        }
    }, [card?.artifact, isVideo]);

    const handleThumbnailClick = (e) => {
        e.stopPropagation();
        onFlaggedByClick(card?.artifact);
    };

    const evaluationResultLabels = {
        both_matched: "Both Matched",
        detection_not_matched: "Detection Not Matched",
        ais_not_matched: "AIS Not Matched",
        both_not_matched: "Both Not Matched",
    };

    const evaluationResult = card?.artifact?.portal?.ais_discrepancy;
    const evaluationResultLabel = evaluationResultLabels[evaluationResult] || evaluationResult;
    const evaluatedBy = card?.artifact?.portal?.ais_discrepancy_reporter_name || "Unknown";
    const evaluatedAt = card?.artifact?.portal?.ais_discrepancy_timestamp;

    if (!card?.artifact || !vesselInfo) return null;

    return (
        <Grid
            container
            paddingTop={"0 !important"}
            height={"100%"}
            maxHeight={"350px"}
            sx={{ cursor: "pointer" }}
            onClick={() => onFlaggedByClick(card.artifact)}
        >
            <Grid container backgroundColor={"primary.main"} borderRadius={2} padding={1} gap={1}>
                <Grid size={12} maxHeight={"200px"}>
                    <PreviewMedia
                        thumbnailLink={thumbnail}
                        originalLink={src}
                        cardId={card.artifact._id}
                        isImage={!isVideo}
                        style={{ borderRadius: 8 }}
                        showVideoThumbnail={isVideo}
                        showArchiveButton={!card.artifact?.portal?.is_archived}
                        isArchived={card.artifact?.portal?.is_archived}
                        vesselId={card.artifact.onboard_vessel_id}
                        buttonsToShow={[helperIconModes.ARCHIVE]}
                        showFlagButton={false}
                        onThumbnailClick={handleThumbnailClick}
                    />
                </Grid>
                <Grid container size={12}>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Tooltip title={vesselName.length > 12 ? vesselName : ""}>
                            <Typography fontSize={"14px"} fontWeight={500}>
                                {vesselName && (vesselName.length > 12 ? vesselName.slice(0, 12) + "..." : vesselName)}
                            </Typography>
                        </Tooltip>
                        <Typography fontSize={"14px"} fontWeight={500}>
                            {dayjs(card.artifact.timestamp)
                                .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Location
                        </Typography>
                        <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Category
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"}>
                            {roundedCoordinates}
                        </Typography>
                        <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"} textAlign={"right"}>
                            {card.artifact.super_category
                                ? card.artifact.super_category.length > 12
                                    ? card.artifact.super_category.slice(0, 12) + "..."
                                    : card.artifact.super_category
                                : "Unspecified category"}
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography
                            fontSize={"14px"}
                            fontWeight={500}
                            maxWidth={"50%"}
                            color={theme.palette.custom.mainBlue}
                        >
                            Evaluated By
                        </Typography>
                        <Typography
                            fontSize={"14px"}
                            fontWeight={500}
                            color={theme.palette.custom.mainBlue}
                        >
                            Evaluated Result
                        </Typography>
                    </Grid>
                    <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                        <Typography
                            fontSize={"14px"}
                            fontWeight={500}
                            maxWidth={"50%"}
                        >
                            {evaluatedBy}
                        </Typography>
                        <Typography
                            fontSize={"14px"}
                            fontWeight={500}
                            maxWidth={"50%"}
                            textAlign={"right"}
                        >
                            {evaluationResultLabel}
                        </Typography>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default memo(EvaluatedCard);

