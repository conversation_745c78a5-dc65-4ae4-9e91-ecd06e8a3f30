import { Grid, Modal, Typography, IconButton, Box, alpha, CircularProgress, Skeleton, Tooltip, Tabs, Tab } from "@mui/material";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import { ChevronLeft, ChevronRight } from "@mui/icons-material";
import ModalContainer from "../../../components/ModalContainer";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
import { useNavigate } from "react-router-dom";
import { getSocket } from "../../../socket";
import FlagIcon from "@mui/icons-material/Flag";
import OutlinedFlagIcon from "@mui/icons-material/OutlinedFlag";
import aisVesselFlagController from "../../../controllers/AisVesselFlag.controller";

export default function GroupedDetailModal({ showDetailModal, setShowDetailModal, selectedCard, setSelectedCard, id }) {
    const { screenSize } = useApp();
    const { user } = useUser();
    const { vesselInfo } = useVesselInfo();
    const navigate = useNavigate();

    const [src, setSrc] = useState(null);
    const [activeTab, setActiveTab] = useState(0);
    const [currentGroupIndex, setCurrentGroupIndex] = useState(selectedCard?.currentGroupIndex || 0);
    const [currentUnifiedIndex, setCurrentUnifiedIndex] = useState(0);
    const [isImageLoading, setIsImageLoading] = useState(true);
    const [imageError, setImageError] = useState(false);
    const [preloadedCache, setPreloadedCache] = useState(new Set());
    const [isInitialLoadComplete, setIsInitialLoadComplete] = useState(false);
    const [preloadController, setPreloadController] = useState(null);
    const [isFlagged, setIsFlagged] = useState(false);
    const [isFlagging, setIsFlagging] = useState(false);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);
    const hasFlagMismatchAIS = user?.hasPermissions([permissions.flagMismatchAIS])

    // Handle grouped artifacts
    const isGrouped = selectedCard?.isGroup && selectedCard?.groupArtifacts?.length > 1;
    const totalCount = isGrouped ? selectedCard?.groupArtifacts?.length : 1;

    // Base artifact considering grouping
    const baseArtifact = useMemo(() => {
        if (isGrouped) {
            return selectedCard?.groupArtifacts?.[currentGroupIndex] || selectedCard;
        }
        return selectedCard;
    }, [selectedCard, currentGroupIndex, isGrouped]);

    // Unified navigation based on duplications of the base artifact
    const isUnified = Array.isArray(baseArtifact?.duplications) && baseArtifact?.duplications?.length >= 1;
    const totalUnified = isUnified ? baseArtifact?.duplications?.length + 1 : 0;

    // Current artifact considering unified navigation (index 0 = base, others from duplications)
    const currentArtifact = useMemo(() => {
        if (!isUnified || currentUnifiedIndex === 0) {
            return baseArtifact;
        }
        const duplicationIndex = currentUnifiedIndex - 1;
        const duplication = baseArtifact?.duplications?.[duplicationIndex];
        return duplication || baseArtifact;
    }, [baseArtifact, isUnified, currentUnifiedIndex]);

    const vessel = useMemo(
        () => vesselInfo.find((v) => v.vessel_id === currentArtifact?.onboard_vessel_id),
        [vesselInfo, currentArtifact?.onboard_vessel_id],
    );

    const key = currentArtifact?.location?.coordinates && displayCoordinates(currentArtifact?.location?.coordinates, !!user?.use_MGRS);
    const [isShowAISDot, setIsShownAISDot] = useState(false);
    useEffect(() => {
        setIsShownAISDot(Boolean(currentArtifact && !currentArtifact?.video_path && !screenSize.md));

    }, [currentArtifact, screenSize.md]);
    // Navigation handlers
    const handlePrevious = useCallback(() => {
        if (currentGroupIndex > 0) {
            setImageError(false);
            setIsInitialLoadComplete(false);
            setCurrentGroupIndex(currentGroupIndex - 1);
            setCurrentUnifiedIndex(0);
        }
    }, [currentGroupIndex]);

    const handleNext = useCallback(() => {
        if (currentGroupIndex < totalCount - 1) {
            setImageError(false);
            setIsInitialLoadComplete(false);
            setCurrentGroupIndex(currentGroupIndex + 1);
            setCurrentUnifiedIndex(0);
        }
    }, [currentGroupIndex, totalCount]);

    const handleClose = () => {
        if (preloadController) {
            preloadController.abort();
            setPreloadController(null);
        }
        setSelectedCard(null);
        setShowDetailModal(false);
        setCurrentGroupIndex(0);
        setImageError(false);
        setIsInitialLoadComplete(false);
        setPreloadedCache(new Set());
        setActiveTab(0);
    };

    const handleUnifiedPrevious = useCallback(() => {
        if (currentUnifiedIndex > 0) {
            setCurrentUnifiedIndex(currentUnifiedIndex - 1);
        }
    }, [currentUnifiedIndex]);

    const handleUnifiedNext = useCallback(() => {
        if (currentUnifiedIndex < totalUnified - 1) {
            setCurrentUnifiedIndex(currentUnifiedIndex + 1);
        }
    }, [currentUnifiedIndex, totalUnified]);

    const handleUnifiedIndexChange = useCallback((index) => {
        if (index >= 0 && index < totalUnified) {
            setCurrentUnifiedIndex(index);
        }
    }, [totalUnified]);

    const preloadImageSequentially = useCallback(async (artifacts, controller) => {
        if (!artifacts) return;

        for (let i = 0; i < artifacts.length; i++) {
            if (controller.signal.aborted) {
                return;
            }

            const artifact = artifacts[i];
            if (!artifact || preloadedCache.has(artifact?._id)) continue;

            if (artifact?.video_path) continue;

            const imageUrl = artifact?.image_url;
            if (!imageUrl) continue;

            try {
                await new Promise((resolve, reject) => {
                    const img = new Image();

                    const cleanup = () => {
                        img.onload = null;
                        img.onerror = null;
                    };

                    img.onload = () => {
                        cleanup();
                        setPreloadedCache(prev => new Set(prev).add(artifact._id));
                        resolve();
                    };

                    img.onerror = () => {
                        cleanup();
                        reject(new Error('Failed to preload image'));
                    };

                    if (controller.signal.aborted) {
                        cleanup();
                        reject(new Error('Preloading cancelled'));
                        return;
                    }

                    img.src = imageUrl;
                });
            } catch (error) {
                console.debug('Preload failed for artifact:', artifact._id, error.message);
            }
        }
    }, [preloadedCache]);

    useEffect(() => {
        if (activeTab != 0) {
            setActiveTab(0)
        }
    }, [currentGroupIndex, currentUnifiedIndex])

    useEffect(() => {
        if (!isInitialLoadComplete || !showDetailModal || !isGrouped) return;

        if (preloadController) {
            preloadController.abort();
        }

        const controller = new AbortController();
        setPreloadController(controller);

        const remainingArtifacts = selectedCard?.groupArtifacts?.filter((artifact, index) =>
            index !== currentGroupIndex && !artifact?.video_path
        ) || [];

        if (remainingArtifacts.length > 0) {
            preloadImageSequentially(remainingArtifacts, controller);
        }

        return () => {
            controller.abort();
        };
    }, [isInitialLoadComplete, showDetailModal, isGrouped, selectedCard?.groupArtifacts, currentGroupIndex, preloadImageSequentially]);

    const handleNavigation = () => {
        if (currentArtifact?.location?.coordinates) {
            navigate(`/dashboard/map?artifact=${currentArtifact?._id}`);
            handleClose();
        }
    };


    // Image loading effect (stable across unified navigation; only base changes reload)
    useEffect(() => {
        if (!baseArtifact) return;
        const mediaOwner = isUnified ? baseArtifact : currentArtifact;
        if (!mediaOwner) return;
        setImageError(false);
        setIsInitialLoadComplete(false);

        const isVideo = Boolean(mediaOwner?.video_path);
        const imageUrl = mediaOwner?.image_url;
        const videoUrl = mediaOwner?.video_url;

        if (isVideo) {
            // Videos are not unified in this flow; still load from base
            setSrc(videoUrl);
            setIsImageLoading(false);
            setIsInitialLoadComplete(true);
            return;
        }

        // If URL unchanged, avoid reloading
        if (src === imageUrl) {
            setIsImageLoading(false);
            setIsInitialLoadComplete(true);
            return;
        }

        const img = new Image();

        const handleLoad = () => {
            setSrc(imageUrl);
            setIsImageLoading(false);
            setImageError(false);
        };

        const handleError = () => {
            setIsImageLoading(false);
            setImageError(true);
        };

        // Set up event handlers first
        img.onload = handleLoad;
        img.onerror = handleError;

        setIsImageLoading(true);

        img.src = imageUrl;

        // Check if image is already loaded in browser cache
        if (img.complete || img.naturalWidth > 0) {
            handleLoad();
        }

        return () => {
            img.onload = null;
            img.onerror = null;
        };
    }, [baseArtifact]);

    // Reset group index when modal opens
    useEffect(() => {
        if (selectedCard?.currentGroupIndex !== undefined) {
            setCurrentGroupIndex(selectedCard.currentGroupIndex);
            setCurrentUnifiedIndex(0);
        }
    }, [selectedCard]);
    // Reset unified index when base artifact changes (e.g., navigating grouped items)
    useEffect(() => {
        setCurrentUnifiedIndex(0);
    }, [baseArtifact?._id]);

    useEffect(() => {
        const socket = getSocket();
        const handleArtifactChanged = (data) => {
            const updatedArtifact = data?.artifact;
            if (!updatedArtifact?.portal?.is_archived) return;

            // Handle regular groups
            if (selectedCard?.groupArtifacts) {
                const artifactInGroup = selectedCard?.groupArtifacts?.find(a => a?._id === updatedArtifact?._id);
                if (artifactInGroup) {
                    const nextGroup = selectedCard?.groupArtifacts?.filter(a => a?._id !== updatedArtifact?._id) || [];
                    setSelectedCard(prev => ({ ...prev, groupArtifacts: nextGroup }));

                    if (nextGroup.length === 0) {
                        handleClose();
                    } else if (currentGroupIndex >= nextGroup.length) {
                        setCurrentGroupIndex(Math.max(0, nextGroup.length - 1));
                    }
                    return;
                }
            }

            // Handle unified groups (duplications)
            if (selectedCard?.duplications) {
                const artifactInUnified = selectedCard?.duplications?.find(a => a?._id === updatedArtifact?._id) ||
                    selectedCard?._id === updatedArtifact?._id;
                if (artifactInUnified) {
                    const nextDuplications = selectedCard?.duplications?.filter(a => a?._id !== updatedArtifact?._id) || [];
                    setSelectedCard(prev => ({ ...prev, duplications: nextDuplications }));

                    if (nextDuplications.length === 0 && selectedCard?._id === updatedArtifact?._id) {
                        handleClose();
                    }
                }
            }
        };

        socket.on("artifact/changed", handleArtifactChanged);
        return () => socket.off("artifact/changed", handleArtifactChanged);
    }, [selectedCard, currentGroupIndex]);

    const details = [
        { label: "Location", value: key },
        { label: "Sub Category", value: currentArtifact?.category },
        { label: "Category", value: currentArtifact?.super_category || "Unspecified category" },
        { label: "Confidence", value: currentArtifact?.det_conf * 100 ? `${(currentArtifact?.det_conf * 100).toFixed(2)}%` : undefined },
        { label: "Size", value: currentArtifact?.size },
        { label: "Color", value: currentArtifact?.color },
        { label: "Imo Number", value: currentArtifact?.imo_number },
        { label: "Flag", value: currentArtifact?.country_flag },
        { label: "Detected Country", value: currentArtifact?.home_country },
        { label: "Weapons", value: currentArtifact?.weapons },
        { label: "Bearing Angle", value: currentArtifact?.true_bearing ? `${Number(currentArtifact?.true_bearing).toFixed(2)}\u00B0` : undefined },
        { label: "Orientation", value: currentArtifact?.vessel_orientation },
        {
            label: "Text Detected",
            value:
                Array.isArray(currentArtifact?.text_extraction) && currentArtifact?.text_extraction?.length > 0
                    ? currentArtifact?.text_extraction
                        .map((e) => e?.text)
                        .slice(0, 5)
                        .join(", ")
                    : null,
        },
        { label: "Features", value: currentArtifact?.vessel_features },
        { label: "Description", value: currentArtifact?.others },
    ];

    const fieldWithFullWidth = ["Text Detected", "Description", "Features"];
    console.log("Ais info:", currentArtifact?.portal);
    const aisInfo = currentArtifact?.portal?.ais_info?.data;

    const aisDetails = [
        // From AIS tooltip mapping
        { label: "AIS Confidence", value: currentArtifact?.portal?.ais_info?.proximity_confidence ? `${(currentArtifact.portal.ais_info.proximity_confidence * 100).toFixed(2)}%` : undefined },
        { label: "MMSI", value: aisInfo?.mmsi },
        { label: "Name", value: aisInfo?.name },
        { label: "Ship Beam", value: aisInfo?.design_beam },
        { label: "Ship Length", value: aisInfo?.design_length },
        { label: "AIS Ship Message Class", value: aisInfo?.sensor_ais_class },
        { label: "AIS Ship State", value: aisInfo?.nav_state },
        { label: "AIS Ship Type", value: aisInfo?.design_ais_ship_type_name },
        { label: "Ship Length Type", value: aisInfo?.design_length_type },
        { label: "Location", value: aisInfo?.nav_longitude && aisInfo?.nav_latitude ? displayCoordinates([aisInfo.nav_longitude, aisInfo.nav_latitude], !!user?.use_MGRS) : undefined },
        { label: "Speed over ground", value: aisInfo?.nav_speed_over_ground },
        { label: "VHF Call Sign", value: aisInfo?.comm_callsign_vhf },
        { label: "Registry Country", value: aisInfo?.portal_registry_country?.country_name },
        { label: "AIS Bearing Angle", value: aisInfo?.portal_true_bearing_deg ? `${Number(aisInfo.portal_true_bearing_deg).toFixed(2)}\u00B0` : undefined },
        { label: "Timestamp", value: aisInfo?.timestamp ? dayjs(aisInfo?.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true })) : undefined },
    ];
    const AISFieldWithFullWidth = [];

    useEffect(() => {
        const loadFlaggedStatus = async () => {
            if (aisInfo?.mmsi) {
                try {
                    const flagged = aisVesselFlagController.isAisVesselFlaggedByUser(aisInfo?.mmsi);
                    setIsFlagged(flagged);
                } catch (error) {
                    console.error("Error loading flagged status:", error);
                }
            }
        };
        loadFlaggedStatus();
    }, [aisInfo?.mmsi, aisVesselFlagController.version]);

    const handleFlagClick = async (e) => {
        e.stopPropagation();
        if (!aisInfo?.mmsi) return;
        setIsFlagging(true);
        try {
            if (isFlagged) {
                await aisVesselFlagController.unflagAisVessel(aisInfo?.mmsi);
                setIsFlagged(false);
            } else {
                await aisVesselFlagController.flagAisVessel(aisInfo?.mmsi);
                setIsFlagged(true);
            }
        } catch (error) {
            console.error("Error flagging AIS vessel:", error);
        } finally {
            setIsFlagging(false);
        }
    };


    if (!currentArtifact) return null;

    return (
        <Modal open={Boolean(showDetailModal)} onClose={handleClose} >
            <ModalContainer title={`Event Details ${isUnified ? "(Multiple Vessels)" : ""}`} onClose={handleClose} showDivider>
                <Grid container gap={1} minWidth={{ xs: 300, sm: 500 }} maxWidth={"80vw"} maxHeight={"80vh"} width={"80vw"}  >
                    {/* maxHeight={{sm:700, xs:700 }} */}
                    <Grid size={12} position="relative" minHeight="300px">
                        {/* Loading placeholder */}
                        {isImageLoading && !src && (
                            <>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        position: "absolute",
                                        top: 0,
                                        left: 0,
                                        width: "100%",
                                        height: "300px",
                                        zIndex: 1,
                                        borderRadius: 2,
                                    }}
                                >
                                    <CircularProgress />
                                </Box>
                                <Skeleton variant="rectangular" width="100%" height="300px" sx={{ borderRadius: 2 }} />
                            </>
                        )}

                        {isImageLoading && src && (
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    position: "absolute",
                                    top: "50%",
                                    left: "50%",
                                    transform: "translate(-50%, -50%)",
                                    zIndex: 3,
                                    backgroundColor: "rgba(0, 0, 0, 0.7)",
                                    borderRadius: "50%",
                                    padding: "12px",
                                }}
                            >
                                <CircularProgress size={24} sx={{ color: "white" }} />
                            </Box>
                        )}

                        {/* Error placeholder */}
                        {imageError && !isImageLoading && (
                            <Box
                                height="300px"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                backgroundColor="rgba(0,0,0,0.2)"
                                borderRadius={2}
                            >
                                <Typography color="text.secondary" variant="body1">
                                    Failed to load media
                                </Typography>
                            </Box>
                        )}

                        {/* Actual media */}
                        {src && !imageError && currentArtifact && (
                            <Box height={{ xs: "300px", sm: "300px", lg: isShowAISDot ? "70vh" : "40vh" }} width={isShowAISDot ? "80vw" : "80vw"} >
                                <PreviewMedia
                                    thumbnailLink={src}
                                    originalLink={src}
                                    cardId={id || baseArtifact._id}
                                    isImage={!currentArtifact.video_path}
                                    style={{ borderRadius: 8, height: "100%", objectFit: "contain", backgroundColor: "#000" }}
                                    skeletonStyle={{ height: "100%", width: "100%", borderRadius: 2 }}
                                    showFullscreenIconForMap={!currentArtifact.video_path}
                                    showArchiveButton={hasManageArtifacts}
                                    isGrouped={isGrouped}
                                    groupArtifacts={selectedCard?.groupArtifacts}
                                    isBounding={isUnified}
                                    det_nbbox={currentArtifact?.det_nbbox}
                                    isUnified={isUnified}
                                    unifiedArtifacts={isUnified ? [baseArtifact, ...(baseArtifact?.duplications || [])] : []}
                                    canArchiveAllArtifacts={isUnified}
                                    showAISFlagButton={currentArtifact?.portal?.ais_info?.data && hasFlagMismatchAIS}
                                    flaggedAISArtifact={currentArtifact}
                                    isAISDot={true}
                                    totalUnifiedAISDots={isUnified ? [baseArtifact, ...(baseArtifact?.duplications || [])] : [baseArtifact]}
                                    aisDetails={aisInfo ? aisDetails : []}
                                    artifactDetails={details}
                                    isShowAISDot={isShowAISDot}
                                    isImageLoading={isImageLoading}
                                    {...(isUnified && { onUnifiedDotClick: handleUnifiedIndexChange })}
                                />
                            </Box>
                        )}
                    </Grid>
                    {/* Unified Navigation  */}
                    {isUnified && (
                        <Grid size={12} container justifyContent="center" display={{ lg: currentArtifact?.video_path ? "flex" : "none" }}>
                            <Box sx={{ display: "flex", alignItems: "center", padding: "4px 8px", gap: 1 }} onClick={(e) => e.stopPropagation()}>
                                <IconButton
                                    size="small"
                                    onClick={handleUnifiedPrevious}
                                    disabled={currentUnifiedIndex === 0}
                                    sx={{
                                        color: "white",
                                        padding: "4px",
                                        background: alpha(theme.palette.custom.borderColor, 0.8) + " !important",
                                        "&:disabled": { color: "rgba(255,255,255,0.3)" },
                                    }}
                                >
                                    <ChevronLeft fontSize="small" />
                                </IconButton>

                                <Typography
                                    variant="caption"
                                    sx={{
                                        color: "white",
                                        fontWeight: 500,
                                        minWidth: "40px",
                                        textAlign: "center",
                                        padding: "5px 17px",
                                        borderRadius: "100px",
                                        background: alpha(theme.palette.primary.main, 0.8),
                                    }}
                                >
                                    Vessel Detections {String(currentUnifiedIndex + 1).padStart(2, "0")}/{String(totalUnified).padStart(2, "0")}
                                </Typography>

                                <IconButton
                                    size="small"
                                    onClick={handleUnifiedNext}
                                    disabled={currentUnifiedIndex === totalUnified - 1}
                                    sx={{
                                        color: "white",
                                        padding: "4px",
                                        background: alpha(theme.palette.custom.borderColor, 0.8) + " !important",
                                        "&:disabled": { color: "rgba(255,255,255,0.3)" },
                                    }}
                                >
                                    <ChevronRight fontSize="small" />
                                </IconButton>
                            </Box>
                        </Grid>
                    )}
                    {
                        !isImageLoading && <Grid
                            position={"absolute"}
                            bottom={isGrouped ? 70 : 45}
                            left={20}
                            display={{ xs: "none", lg: isShowAISDot ? "flex" : "none" }}
                        >
                            <Grid
                                container
                                sx={{

                                    backgroundColor: (theme) => alpha(theme.palette.primary.light, 0.5),
                                    borderRadius: "20px",
                                    width: "auto",
                                    padding: "20px 20px",
                                    minWidth: "300px",
                                }}
                            >
                                <Grid
                                    container
                                    width={"100%"}
                                    flexDirection={"column"}
                                    gap={1}

                                >
                                    <Grid>
                                        <Typography fontSize={"14px"} >
                                            Details
                                        </Typography>
                                    </Grid>
                                    <Grid>
                                        <Grid sx={{ display: "flex", flexDirection: "row", alignItems: "center", gap: 1, justifyContent: "space-between" }}>
                                            <Typography sx={{ fontSize: "12px" }}>
                                                {vessel?.name || vessel?.vessel_id || "Unknown Vessel"}
                                            </Typography>
                                            <Typography sx={{ fontSize: "12px", textAlign: "right" }}>
                                                {dayjs(currentArtifact?.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                                            </Typography>
                                        </Grid>
                                        <Grid sx={{ display: "flex", flexDirection: "row", alignItems: "center", gap: 1, justifyContent: "space-between" }}>
                                            <Typography sx={{ fontSize: "12px" }}>
                                                Location
                                            </Typography>
                                            <Typography sx={{ fontSize: "12px", textAlign: "right" }}>
                                                {key}
                                            </Typography>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </Grid>
                        </Grid>
                    }
                    <Grid
                        display={{ xs: "flex", sm: "flex", lg: !isShowAISDot ? "flex" : "none" }}
                        justifyContent={"space-between"}
                        alignItems={"center"}
                        paddingX={1}
                        flexDirection={"row"}
                        size={12}
                    >
                        <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                            {vessel?.name || vessel?.vessel_id || "Unknown Vessel"}
                        </Typography>
                        <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                            {dayjs(currentArtifact?.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>

                    <Grid width={"100%"} display={{ lg: !isShowAISDot ? "block" : "none" }}>
                        {aisInfo && <Tabs value={activeTab} onChange={(_, v) => setActiveTab(v)}
                            variant="fullWidth"
                            indicatorColor="none"
                            sx={{
                                width: aisInfo ? "100%" : "50%",
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: "50%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },

                            }}>
                            <Tab label="Detection Details" />
                            {
                                aisInfo && <Tooltip title={!aisInfo && "AIS information is not available"}>
                                    <Tab label="AIS Information" sx={{
                                        color: "white",
                                        "&.Mui-disabled": {
                                            color: theme.palette.custom.mediumGrey,
                                            opacity: 1,
                                            cursor: "not-allowed",
                                            pointerEvents: "fill",
                                        },
                                    }}
                                        disabled={!aisInfo} />
                                </Tooltip>
                            }
                        </Tabs>}
                    </Grid>
                    <Grid
                        container
                        sx={{
                            maxHeight: { xs: "200px", md: "280px", lg: "19vh" },
                            overflow: "auto",
                        }}
                        display={{ lg: !isShowAISDot ? "block" : "none" }}
                    >
                        {activeTab === 0 && (
                            <Grid container height={{ xs: "200px", md: "350px", lg: "350px" }} gap={1}>
                                {details.map(({ label, value }, index) => (
                                    <React.Fragment key={index}>
                                        <Grid
                                            display="flex"
                                            alignItems={{
                                                xs: "flex-start",
                                                sm: index % 2 == 0 || fieldWithFullWidth.includes(label) ? "flex-start" : "flex-center",
                                                md: "center"
                                            }}
                                            paddingX={1}
                                            flexDirection="column"
                                            size={{ xs: 12, sm: fieldWithFullWidth.includes(label) ? 12 : 3.9, md: 2.9 }}
                                        >
                                            <Typography
                                                fontSize="16px"
                                                fontWeight={500}
                                                color={theme.palette.custom.mainBlue}
                                                sx={{ display: "flex", alignItems: "center", gap: "6px", textAlign: { xs: "left", sm: "left", md: "center" } }}
                                            >
                                                {label}
                                                {label === "Bearing Angle" && (
                                                    <Tooltip title="Angle measured clockwise between the True North and the Target as observed from own vessel">
                                                        <img src="/icons/info_icon.svg" />
                                                    </Tooltip>
                                                )}
                                            </Typography>
                                            <Typography
                                                fontSize="16px"
                                                fontWeight={500}
                                                onClick={() => (label === "Location" ? handleNavigation() : null)}
                                                sx={{
                                                    cursor: label === "Location" ? "pointer" : "default",
                                                    color: label === "Location" ? "#007bff" : "inherit",
                                                    textDecoration: label === "Location" ? "underline" : "none",
                                                    // userSelect: "none",
                                                    "&:hover":
                                                        label === "Location"
                                                            ? {
                                                                color: "#0056b3",
                                                                textDecoration: "underline",
                                                            }
                                                            : {},
                                                    textAlign: { xs: "left", sm: "left", md: "center" }
                                                }}
                                                title={label === "Location" ? "Click to view on map" : ""}
                                            >
                                                {value ?? "--"}
                                            </Typography>
                                        </Grid>
                                    </React.Fragment>
                                ))}
                            </Grid>
                        )}
                        {activeTab === 1 && (
                            <Grid
                                container
                                height={{ xs: "200px", md: "280px", lg: "19vh" }}
                                gap={1}
                                display={{ lg: !isShowAISDot ? "" : "none" }}
                            >
                                {/* Flag Button */}
                                <Grid
                                    size={12}
                                    display="flex"
                                    justifyContent="flex-end"
                                    alignItems="center"
                                    paddingX={1}
                                    paddingBottom={1}
                                >
                                    <Tooltip
                                        title={!isFlagging ? (isFlagged ? "Remove AIS flag" : "Flag AIS") : null}
                                        arrow
                                        placement="left"
                                    >
                                        <IconButton
                                            size="small"
                                            sx={{
                                                height: 32,
                                                width: 32,
                                                padding: 0,
                                                color: "#fff",
                                                backgroundColor: isFlagged ? "#E60000CC" : "rgba(0,0,0,0.5)",
                                                borderRadius: "50%",
                                                "&:hover": {
                                                    backgroundColor: isFlagged ? "#E60000" : "rgba(0,0,0,0.7)",
                                                },
                                            }}
                                            onClick={handleFlagClick}
                                            disabled={isFlagging}
                                        >
                                            {isFlagging ? (
                                                <CircularProgress sx={{ color: "white" }} size={18} />
                                            ) : isFlagged ? (
                                                <FlagIcon sx={{ height: 18 }} />
                                            ) : (
                                                <OutlinedFlagIcon sx={{ height: 18 }} />
                                            )}
                                        </IconButton>
                                    </Tooltip>
                                </Grid>
                                {aisDetails.map(({ label, value }, index) => (
                                    <React.Fragment key={index}>
                                        <Grid
                                            display="flex"
                                            alignItems={{
                                                xs: "flex-start",
                                                // sm: index % 2 == 0 ? "flex-start" : "flex-end",
                                                sx: "flex-start",
                                                md: "center"
                                            }}
                                            paddingX={1}
                                            flexDirection="column"
                                            size={{ xs: 12, sm: AISFieldWithFullWidth.includes(label) ? 12 : 3.9, md: 2.9 }}
                                        >
                                            <Typography
                                                fontSize="16px"
                                                fontWeight={500}
                                                color={theme.palette.custom.mainBlue}
                                                sx={{ display: "flex", alignItems: "center", gap: "6px", textAlign: { xs: "left", sm: "left", md: "center" } }}
                                            >
                                                {label}
                                            </Typography>
                                            <Typography
                                                fontSize="16px"
                                                fontWeight={500}
                                                sx={{
                                                    cursor: "default",
                                                    color: "inherit",
                                                    // userSelect: "none",
                                                    textAlign: { xs: "left", sm: "left", md: "center" }

                                                }}
                                                title={label === "Location" ? "Click to view on map" : ""}
                                            // textAlign={index % 2 == 0 || AISFieldWithFullWidth.includes(label) ? "left" : "right"}
                                            >
                                                {value ?? "--"}
                                            </Typography>
                                        </Grid>
                                    </React.Fragment>
                                ))}
                            </Grid>
                        )}
                    </Grid>
                    {/* Navigation panel for grouped artifacts */}
                    {isGrouped && (
                        <Grid size={12} container justifyContent="center">
                            <Box sx={{ display: "flex", alignItems: "center", padding: "4px 8px", gap: 1 }} onClick={(e) => e.stopPropagation()}>
                                <IconButton
                                    size="small"
                                    onClick={handlePrevious}
                                    disabled={currentGroupIndex === 0}
                                    sx={{
                                        color: "white",
                                        padding: "4px",
                                        background: alpha(theme.palette.custom.borderColor, 0.8) + " !important",
                                        "&:disabled": { color: "rgba(255,255,255,0.3)" },
                                    }}
                                >
                                    <ChevronLeft fontSize="small" />
                                </IconButton>

                                <Typography
                                    variant="caption"
                                    sx={{
                                        color: "white",
                                        fontWeight: 500,
                                        minWidth: "40px",
                                        textAlign: "center",
                                        padding: "5px 17px",
                                        borderRadius: "100px",
                                        background: alpha(theme.palette.primary.main, 0.8),
                                    }}
                                >
                                    {String(currentGroupIndex + 1).padStart(2, "0")}/{String(totalCount).padStart(2, "0")}
                                </Typography>

                                <IconButton
                                    size="small"
                                    onClick={handleNext}
                                    disabled={currentGroupIndex === totalCount - 1}
                                    sx={{
                                        color: "white",
                                        padding: "4px",
                                        background: alpha(theme.palette.custom.borderColor, 0.8) + " !important",
                                        "&:disabled": { color: "rgba(255,255,255,0.3)" },
                                    }}
                                >
                                    <ChevronRight fontSize="small" />
                                </IconButton>
                            </Box>
                        </Grid>
                    )}
                </Grid>
            </ModalContainer>
        </Modal>
    );
}
