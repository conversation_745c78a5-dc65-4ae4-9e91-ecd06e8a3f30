import axiosInstance from "../axios";

class AisVesselFlagController {
    constructor() {
        this.userFlaggedMmsis = new Set();
        this._version = 0;
    }

    get version() {
        return this._version;
    }

    _incrementVersion() {
        this._version += 1;
    }

    async flagAisVessel(mmsi) {
        try {
            const response = await axiosInstance.post(`/vesselAis/flag`, { mmsi });
            this.userFlaggedMmsis.add(mmsi);
            this._incrementVersion();
            return response.data;
        } catch (error) {
            console.error("Error flagging AIS vessel:", error);
            throw error;
        }
    }

    async getFlaggedAisVessels(page, pageSize) {
        try {
            const response = await axiosInstance.get(`/vesselAis/flagged`, {
                params: { page, pageSize },
            });
            return response.data || { vessels: [], totalCount: 0 };
        } catch (error) {
            console.error("Error fetching flagged AIS vessels:", error);
            throw error;
        }
    }

    async unflagAisVessel(mmsi) {
        try {
            const response = await axiosInstance.post(`/vesselAis/unflag`, { mmsi });
            this.userFlaggedMmsis.delete(mmsi);
            this._incrementVersion();
            return response.data;
        } catch (error) {
            console.error("Error unflagging AIS vessel:", error);
            throw error;
        }
    }

    isAisVesselFlaggedByUser(mmsi) {
        return this.userFlaggedMmsis.has(mmsi);
    }

    async getUserFlaggedAisVesselMmsis() {
        try {
            const response = await axiosInstance.get(`/vesselAis/flagged/user`);
            const flaggedMmsis = response.data.flaggedMmsis || [];
            this.userFlaggedMmsis = new Set(flaggedMmsis);
            this._incrementVersion();
            return flaggedMmsis;
        } catch (error) {
            console.error("Error fetching user flagged AIS vessel MMSIs:", error);
            throw error;
        }
    }

    async removeAllFlagsFromAisVessel(mmsi) {
        try {
            const response = await axiosInstance.delete(`/vesselAis/flagged/${mmsi}`);
            this.userFlaggedMmsis.delete(mmsi);
            this._incrementVersion();
            return response.data;
        } catch (error) {
            console.error("Error removing all flags from AIS vessel:", error);
            throw error;
        }
    }
}

const aisVesselFlagController = new AisVesselFlagController();
export default aisVesselFlagController;

