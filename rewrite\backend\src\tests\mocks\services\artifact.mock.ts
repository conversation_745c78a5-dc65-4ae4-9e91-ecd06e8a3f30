import { jest } from '@jest/globals';

export const defaultProjection = {
    _id: 1,
    unit_id: 1,
    bucket_name: 1,
    image_path: 1,
    video_path: 1,
    location: 1,
    depth_estimation: 1,
    category: 1,
    super_category: 1,
    size: 1,
    color: 1,
    weapons: 1,
    others: 1,
    timestamp: 1,
    onboard_vessel_name: 1,
    onboard_vessel_id: 1,
    portal: 1,
    country_flag: 1,
    aws_region: 1,
    text_extraction: 1,
    imo_number: 1,
    thumbnail_image_path: 1,
    vessel_features: 1,
    home_country: 1,
    vessel_orientation: 1,
    true_bearing: 1,
    det_nbbox: 1,
    det_conf: 1,
};

const artifactServiceMock = {
    searchArtifactsIds: jest.fn(),
    buildArtifactsQuery: jest.fn(),
    fetchArtifacts: jest.fn(),
    unifyArtifacts: jest.fn(),
    groupArtifacts: jest.fn(),
};

export default artifactServiceMock;