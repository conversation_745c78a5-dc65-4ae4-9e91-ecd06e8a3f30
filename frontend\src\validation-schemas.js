import * as yup from "yup";

const inviteUserSchema = yup.object({
    email: yup.string("Enter email").required("Email is required").email("Enter a valid email"),
    role_id: yup.number("Select role").strict(true).required("Role is required"),
    allowed_vessels: yup.array().of(yup.string()).required("Vessel Id is required"),
});

const signupUserSchema = yup.object({
    firstName: yup
        .string("Enter first name")
        .trim()
        .required("First Name is required")
        .min(1, "First Name cannot be empty"),
    lastName: yup
        .string("Enter last name")
        .trim()
        .required("Last Name is required")
        .min(1, "Last Name cannot be empty"),
    username: yup
        .string("Enter username")
        .trim()
        .required("Username is required")
        .min(1, "Username cannot be empty"),
    password: yup.string("Enter password").min(8, "Password should be of minimum 8 characters length").required("Password is required"),
    confirmPassword: yup
        .string("Enter confirm password")
        .oneOf([yup.ref("password"), null], "Passwords mismatch")
        .required("Confirm password is required"),
});

const createRegionGroupSchema = yup.object({
    name: yup.string("Enter region name").required("Region name is required"),
    timezone: yup.string("Select timezone").required("Timezone is required"),
    // vessel_ids: yup.array().of(yup.string()).required("Vessel Id is required"),
});

export { inviteUserSchema, signupUserSchema, createRegionGroupSchema };
