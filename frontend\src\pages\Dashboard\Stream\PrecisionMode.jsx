import React, { useRef, useState, useEffect, useCallback, useLayoutEffect } from "react";

const HorizontalScrollCards = ({
    totalSeconds = 60 * 2,
    pixelPerSecond = 10,
    onCenterSecondChange,
    baseOffsetSeconds = 0,
    startSecond = 10,
    finalizeIdleMs = 200, // debounce for "scroll stopped"
    autoFocus = false, // focus for immediate keyboard control
    handlePause,
}) => {
    const scrollRef = useRef(null);

    // Interaction + drag state
    const isPointerDown = useRef(false);
    const startXRef = useRef(0);
    const scrollStartRef = useRef(0);
    const [drag, setDrag] = useState(false);
    // Track if the current scroll is user-initiated
    const userInteractingRef = useRef(false);

    // Track programmatic scrolls (so we don’t emit on them)
    const programmaticScrollRef = useRef(false);

    // Debounce timer for idle-finalize
    const scrollIdleTimerRef = useRef(null);

    // Last emitted absolute second (to avoid duplicate emits)
    const lastSentSecRef = useRef(null);

    const [playheadTime, setPlayheadTime] = useState("0:00");

    // ---------- helpers ----------
    const formatTime = (seconds) => {
        const h = Math.floor(seconds / 3600);
        const m = Math.floor((seconds % 3600) / 60);
        const s = seconds % 60;
        if (h > 0 && h < 24) return `${h}:${m.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`;
        if (h >= 24) return `${Math.floor(h / 24)}d ${h % 24}:${m.toString().padStart(2, "0")}:${s.toString().padStart(2, "0")}`;
        return `${m}:${s.toString().padStart(2, "0")}`;
    };

    const getCenterAbsoluteSec = useCallback(() => {
        const el = scrollRef.current;
        if (!el) return baseOffsetSeconds;
        const leftPadding = el.clientWidth * 0.5; // 50% padding
        const centerSec = (el.scrollLeft + leftPadding) / pixelPerSecond;
        const clamped = Math.max(0, Math.min(totalSeconds, Math.round(centerSec)));
        return baseOffsetSeconds + clamped;
    }, [baseOffsetSeconds, pixelPerSecond, totalSeconds]);

    // UI update only (no API)
    const updatePlayheadTime = useCallback(() => {
        const el = scrollRef.current;
        if (!el) return;
        const leftPadding = el.clientWidth * 0.5; // 50% padding
        const centerSec = (el.scrollLeft + leftPadding) / pixelPerSecond;
        const clamped = Math.max(0, Math.min(totalSeconds, Math.round(centerSec)));
        setPlayheadTime(formatTime(clamped));
    }, [pixelPerSecond, totalSeconds]);

    // Emit only when an interaction ends
    const finalizeScrub = useCallback(() => {
        if (!userInteractingRef.current) return; // ignore non-user scrolls
        const absoluteSec = getCenterAbsoluteSec();
        if (absoluteSec !== lastSentSecRef.current) {
            lastSentSecRef.current = absoluteSec;
            onCenterSecondChange(absoluteSec); // <-- API callback
        }
        userInteractingRef.current = false; // end interaction session
    }, [getCenterAbsoluteSec, onCenterSecondChange]);

    const scheduleIdleFinalize = useCallback(() => {
        if (scrollIdleTimerRef.current) clearTimeout(scrollIdleTimerRef.current);
        scrollIdleTimerRef.current = setTimeout(() => {
            // When scroll momentum stops, finalize if this was user-driven
            finalizeScrub();
        }, finalizeIdleMs);
    }, [finalizeScrub, finalizeIdleMs]);

    // ---------- initial centering (programmatic; no emit) ----------
    useLayoutEffect(() => {
        const el = scrollRef.current;
        if (!el) return;
        // Don't update scroll position if user is currently dragging
        if (drag) return;
        const s = Math.max(0, Math.min(totalSeconds - 1, startSecond));
        programmaticScrollRef.current = true;
        // Position startSecond under the playhead (center of viewport)
        // When startSecond = 10, we want the 10-second mark under the playhead
        const leftPadding = el.clientWidth * 0.5;
        el.scrollLeft = Math.max(0, s * pixelPerSecond - leftPadding);
        updatePlayheadTime();
        // clear programmatic flag on next frame
        requestAnimationFrame(() => {
            programmaticScrollRef.current = false;
        });
    }, [startSecond, pixelPerSecond, totalSeconds, updatePlayheadTime, drag]);

    useEffect(() => {
        if (autoFocus) scrollRef.current?.focus();
    }, [autoFocus]);

    // ---------- pointer (mouse + touch) ----------
    const onPointerDown = (e) => {
        const el = scrollRef.current;
        if (!el) return;
        isPointerDown.current = true;
        setDrag(true);
        handlePause();
        userInteractingRef.current = true; // start a user interaction
        startXRef.current = e.clientX;
        scrollStartRef.current = el.scrollLeft;
        el.setPointerCapture?.(e.pointerId);
        if (scrollIdleTimerRef.current) clearTimeout(scrollIdleTimerRef.current);
    };

    const onPointerMove = (e) => {
        if (!isPointerDown.current) return;
        const el = scrollRef.current;
        if (!el) return;
        const deltaX = e.clientX - startXRef.current;
        const newScrollLeft = scrollStartRef.current - deltaX;
        const leftPadding = el.clientWidth * 0.5;
        const maxScrollLeft = Math.max(0, (totalSeconds - 1) * pixelPerSecond - leftPadding);
        el.scrollLeft = Math.max(0, Math.min(maxScrollLeft, newScrollLeft));
        updatePlayheadTime(); // live UI
    };

    const endPointer = () => {
        if (!isPointerDown.current) return;
        isPointerDown.current = false;
        setDrag(false);
        finalizeScrub(); // emit once on drop
    };

    const onPointerUp = () => endPointer();
    const onPointerCancel = () => endPointer();

    // ---------- keyboard ----------
    const KEY_SCROLL_KEYS = new Set(["ArrowLeft", "ArrowRight", "PageUp", "PageDown", "Home", "End"]);

    const onKeyDown = (e) => {
        const el = scrollRef.current;
        if (!el) return;
        let handled = true;

        const secPerArrow = 1; // 1s per arrow key press
        const step = secPerArrow * pixelPerSecond;
        const page = Math.max(step, el.clientWidth * 0.9);

        // Calculate maximum scroll position (last second should be centerable)
        const leftPadding = el.clientWidth * 0.5;
        const maxScrollLeft = Math.max(0, (totalSeconds - 1) * pixelPerSecond - leftPadding);

        switch (e.key) {
            case "ArrowLeft":
                el.scrollLeft = Math.max(0, el.scrollLeft - step);
                break;
            case "ArrowRight":
                el.scrollLeft = Math.min(maxScrollLeft, el.scrollLeft + step);
                break;
            case "PageUp":
                el.scrollLeft = Math.max(0, el.scrollLeft - page);
                break;
            case "PageDown":
                el.scrollLeft = Math.min(maxScrollLeft, el.scrollLeft + page);
                break;
            case "Home":
                el.scrollLeft = 0;
                break;
            case "End":
                el.scrollLeft = maxScrollLeft;
                break;
            default:
                handled = false;
        }

        if (handled) {
            userInteractingRef.current = true; // keyboard is a user interaction
            e.preventDefault();
            updatePlayheadTime();
            scheduleIdleFinalize(); // also handles key-repeat
        }
    };

    const onKeyUp = (e) => {
        if (KEY_SCROLL_KEYS.has(e.key)) finalizeScrub(); // emit on release
    };

    // ---------- wheel/trackpad + scrollbar drags ----------
    const onWheel = () => {
        userInteractingRef.current = true; // wheel is user interaction
    };

    useEffect(() => {
        const el = scrollRef.current;
        if (!el) return;

        const onScroll = () => {
            updatePlayheadTime();
            // If not programmatic and not currently dragging, treat as user interaction (e.g., scrollbar drag)
            if (!programmaticScrollRef.current && !drag) {
                userInteractingRef.current = true;
                scheduleIdleFinalize();
            }
        };

        el.addEventListener("scroll", onScroll, { passive: true });
        return () => el.removeEventListener("scroll", onScroll);
    }, [updatePlayheadTime, scheduleIdleFinalize, drag]);

    // ---------- ticks (virtualized) ----------
    const getVisibleTicks = () => {
        const el = scrollRef.current;
        if (!el) return [];
        const bufferPx = 0;
        const startPx = Math.max(0, el.scrollLeft - bufferPx);
        const endPx = el.scrollLeft + el.clientWidth + bufferPx;
        const startSec = Math.floor(startPx / pixelPerSecond);
        const endSec = Math.min(totalSeconds, Math.ceil(endPx / pixelPerSecond));

        const ticks = [];
        for (let sec = startSec; sec <= endSec; sec++) {
            let lineHeight = 13;
            let label = null;
            if (sec % 10 === 0) {
                lineHeight = 27;
                label = formatTime(sec);
            } else if (sec % 5 === 0) {
                lineHeight = 22;
            }
            ticks.push({ sec, lineHeight, label });
        }
        return ticks;
    };

    const ticks = getVisibleTicks();
    const totalWidth = totalSeconds * pixelPerSecond;
    const leftPadding = scrollRef.current ? scrollRef.current.clientWidth * 0.5 : 0; // 50% padding

    return (
        <div
            style={{
                position: "relative",
                backgroundColor: "#1E242C",
                width: "97vw",
            }}
        >
            {/* Playhead time display */}
            <div
                style={{
                    position: "absolute",
                    top: "-80px",
                    left: "50%",
                    transform: "translateX(-50%)",
                    color: "white",
                    background: "rgba(0,0,0,0.6)",
                    padding: "4px 8px",
                    borderRadius: "4px",
                    fontWeight: "bold",
                    zIndex: 10,
                }}
            >
                {playheadTime}
            </div>

            {/* Scrollable timeline */}
            <div
                ref={scrollRef}
                tabIndex={0}
                role="slider"
                aria-label="Timeline scrubber"
                aria-valuemin={baseOffsetSeconds}
                aria-valuemax={baseOffsetSeconds + totalSeconds}
                aria-valuenow={getCenterAbsoluteSec()}
                aria-valuetext={playheadTime}
                onPointerDown={onPointerDown}
                onPointerMove={onPointerMove}
                onPointerUp={onPointerUp}
                onPointerCancel={onPointerCancel}
                onKeyDown={onKeyDown}
                onKeyUp={onKeyUp}
                onWheel={onWheel}
                style={{
                    overflowX: "scroll",
                    position: "relative",
                    scrollbarWidth: "none",
                    msOverflowStyle: "none",
                    WebkitOverflowScrolling: "touch",
                    cursor: "grab",
                    border: "1px solid #444",
                    // borderRadius: "6px",
                    background: "#1E242C",
                    width: "100%",
                    height: "75px",
                    touchAction: "none",
                    userSelect: "none",
                    outline: "none",
                    //   backgroundColor:"green"
                }}
            >
                {/* Spacer so scroll works */}
                <div
                    style={{
                        position: "relative",
                        width: totalWidth,
                        height: "100%",
                        paddingRight: leftPadding,
                        // backgroundColor: "pink"
                    }}
                >
                    {/* Visible ticks */}
                    <div
                        style={{
                            position: "absolute",
                            top: 5,
                            left: 0,
                            display: "flex",
                            alignItems: "flex-start",
                        }}
                    >
                        {ticks.map(({ sec, lineHeight, label }) => (
                            <div
                                key={sec}
                                style={{
                                    position: "absolute",
                                    left: sec * pixelPerSecond,
                                    width: pixelPerSecond,
                                    textAlign: "center",
                                    //   backgroundColor:"gray"
                                }}
                            >
                                <div
                                    style={{
                                        width: "2px",
                                        height: lineHeight,
                                        background: "white",
                                        margin: "0 auto",
                                    }}
                                />
                                {label && <div style={{ color: "white", fontSize: "11px", marginTop: 4 }}>{label}</div>}
                            </div>
                        ))}
                    </div>
                </div>
            </div>

            {/* Fixed playhead */}
            <div
                style={{
                    position: "absolute",
                    top: "0px",
                    left: "50%",
                    transform: "translateX(-50%)",
                    width: "4px",
                    height: "75px",
                    background: "#3873E4",
                    zIndex: 20,
                }}
            />
        </div>
    );
};

export default HorizontalScrollCards;
