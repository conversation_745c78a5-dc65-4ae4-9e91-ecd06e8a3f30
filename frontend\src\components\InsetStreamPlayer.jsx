import { Grid, Typography, CircularProgress } from "@mui/material";
import _ from "lodash";
import React, { useEffect, useState, useRef } from "react";
import VideoPlayer from "../pages/Dashboard/Stream/VideoPlayer";
import axiosInstance from "../axios";
import { logEvent } from "../utils";
import theme from "../theme";

const InsetStreamPlayer = ({
    vessel,
    streamMode,
    startTimestamp
}) => {
    const [isLoading, setIsLoading] = useState(false);
    const [streamUrl, setStreamUrl] = useState("");
    const [hlsUrlError, setHlsUrlError] = useState("");
    const playBack = {
        offset: 0,
        currentVideoPlayTime: 0,
        latency: 0,
    };
    const referenceTime = useRef(0);

    const loadStreamUrl = async ({ unit_id, region }) => {
        setIsLoading(true);
        setHlsUrlError("");
        try {
            // note: have to fetch new url everytime. cannot restream with same url
            const { url } = await axiosInstance
                .get(
                    `/v2/kinesis/dashStreamingSessionURL?${new URLSearchParams({
                        region,
                        streamName: unit_id,
                        streamMode,
                        startTimestamp: streamMode === "ON_DEMAND" ? startTimestamp : new Date().toISOString(),
                        totalDuration: 60
                    }).toString()}`,
                    { meta: { showSnackbar: false } },
                )
                .then((res) => res.data.data);
            setStreamUrl(url);
            logEvent("StreamUrlLoaded", {
                streamName: unit_id,
                streamMode: "LIVE",
                url
            });
        } catch (err) {
            console.log("error in loadStreamUrl", err);
            setStreamUrl("");
            const data = err?.response?.data;
            setHlsUrlError(data?.message || `An unexpected error occurred. Please try again later. ${err?.message}`);
            logEvent("StreamUrlLoadFailed", {
                streamName: unit_id,
                streamMode: "LIVE",
                error: err?.message
            });
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        loadStreamUrl(vessel);
    }, [vessel, startTimestamp]);

    let componentState = "error";
    if (isLoading) {
        componentState = "loading";
    } else if (!hlsUrlError) {
        componentState = "ready";
    }

    const innerComponentMap = {
        error: <Typography align="center">
                   {streamMode === "LIVE"
                       ? "This vessel is not streaming"
                       : "No replay found at this location"}
               </Typography>,
        loading: <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={50} />,
        ready: <VideoPlayer
                   setStreamUrl={setStreamUrl}
                   setPlayBack={() => {}}
                   playBack={playBack}
                   streamUrl={streamUrl}
                   streamMode={streamMode}
                   totalDuration={60}
                   setScrubBarSlotInterval={() => {}}
                   selectedStream={{ IsLive: true }}
                   referenceTime={referenceTime}
                   handleReplay={() => {}}
                   hlsUrlError={hlsUrlError}
                   marks={[]}
                   setTimestamp={() => {}}
                   replayCoordinate={null}
                   hideDetails
                   isInsetPlayer
        />,
    };

    return (
        <Grid
            container
            alignItems="center"
            justifyContent="center"
            width="100%"
        >
            {innerComponentMap[componentState]}
        </Grid>
    );
}

export default InsetStreamPlayer;
