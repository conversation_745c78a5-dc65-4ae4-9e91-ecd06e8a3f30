/** @type {import("jest").Config} **/
module.exports = {
    // Test environment
    testEnvironment: "node",

    // Setup files
    setupFilesAfterEnv: ["<rootDir>/src/tests/setup.ts"],

    // Test matching
    testMatch: ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"],
    testPathIgnorePatterns: ["<rootDir>/frontend/", "<rootDir>/node_modules/", "<rootDir>/dist/", "<rootDir>/coverage/"],

    // TypeScript transformation
    preset: "ts-jest",
    transform: {
        "^.+\\.tsx?$": [
            "ts-jest",
            {
                // Optimize ts-jest performance
                useESM: false,
                tsconfig: {
                    // Minimal TypeScript config for tests
                    target: "ES2020",
                    module: "CommonJS",
                    moduleResolution: "node",
                    allowJs: true,
                    skipLibCheck: true,
                    forceConsistentCasingInFileNames: false,
                    isolatedModules: true,
                },
            },
        ],
    },

    // Module resolution
    moduleNameMapper: {
        "^src/(.*)$": "<rootDir>/src/$1",
    },
    moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json"],

    // Coverage configuration - optimized for performance
    collectCoverageFrom: ["src/**/*.ts", "!src/**/*.d.ts", "!src/tests/**", "!src/types/**", "!src/interfaces/**", "!src/scripts/**"],
    coveragePathIgnorePatterns: ["/node_modules/", "/coverage/", "/dist/", "/build/", "/logs/", "/credentials/", "/main/", "/frontend/"],
    coverageReporters: ["text", "lcov", "html", "json-summary"],
    coverageDirectory: "coverage",

    // Performance optimizations
    maxWorkers: 1, // Use single worker to reduce memory usage
    cache: true,
    cacheDirectory: "<rootDir>/node_modules/.cache/jest",

    // Memory and timeout settings
    workerIdleMemoryLimit: "512MB",
    detectOpenHandles: true,
    forceExit: true, // Force exit to prevent hanging

    // Reduce verbose output during coverage collection
    verbose: false,
    silent: false,

    // Clear mocks between tests
    clearMocks: true,
    restoreMocks: true,
};
