import { jest } from '@jest/globals';

const generateOpenAIEmbedding = jest.fn().mockResolvedValue([0.1, 0.2, 0.3, 0.4, 0.5] as never);

// Mock for OpenAI constructor (used in route files)
const mockChatCompletionsCreate = jest.fn();
const mockEmbeddingsCreate = jest.fn();

const MockOpenAI = jest.fn().mockImplementation(() => ({
    embeddings: {
        create: mockEmbeddingsCreate,
    },
    chat: {
        completions: {
            create: mockChatCompletionsCreate,
        },
    },
}));

// Add helper methods for test control
(MockOpenAI as any).__setMode = jest.fn();
(MockOpenAI as any).__getMockChatCompletionsCreate = () => mockChatCompletionsCreate;
(MockOpenAI as any).__getMockEmbeddingsCreate = () => mockEmbeddingsCreate;

export { generateOpenAIEmbedding };
export default MockOpenAI;
