import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { GoogleMap } from "@react-google-maps/api";
import { Skeleton } from "@mui/material";
import dayjs from "dayjs";
import "video.js/dist/video-js.css";
import { useLocation } from "react-router-dom";
import { useUser } from "../../../hooks/UserHook";
import axiosInstance from "../../../axios";
import { useToaster } from "../../../hooks/ToasterHook";
import { useApp } from "../../../hooks/AppHook";
import { defaultValues } from "../../../utils";
import { drawArtifacts, drawAisData, drawAudioData, filterArtifactsNearHomePorts, filterArtifactsByAis, drawPolylines, drawDataPoints, drawPortsData } from "./Map";
import useVesselInfo from "../../../hooks/VesselInfoHook";
import { getSocket } from "../../../socket";
import artifactController from "../../../controllers/Aritfact.controller";
import StreamInfoWindow from "./StreamInfoWindow";

const MainMap = ({
    vessels = [],
    loadingVessels,
    setLoadingVessels,
    // setErrorsomeVessels,
    errorsomeVessels,
    setAvailableVessels,
    setEmptyVessels,
    emptyVessels,
    focusedVessel,
    datapointsDistance = defaultValues.datapointsDistance,
    journeyStart = defaultValues.journeyStart,
    journeyEnd = defaultValues.journeyEnd,
    initialZoom = defaultValues.zoom,
    interval = defaultValues.interval,
    setArtifactsCategories,
    selectedArtifactsCategory,
    timeSlider,
    showDatapoints,
    showArtifacts,
    showAisData,
    showAudioData,
    homePortsArtifactsMode,
    selectedNumberOfArtifacts,
    artifactsType,
    showFilteredCoordinates,
    // fromDate,
    // toDate,
    // showCoordinatesPoints,
    setSelectedArtifactsCategory,
    targetArtifactId,
    setTargetArtifactId,
    aisArtifactsFilter,
    clearUrlParameter,
    showEEZLayers,
    showPortsLayers,
    setLoadingEEZLayers,
    setTotalArtifactsCount,
}) => {
    const defaultCenter = { lat: 0, lng: 0 };
    const { google, timezone, devMode } = useApp();
    const { vesselInfo } = useVesselInfo();
    const toaster = useToaster();
    const { pathname } = useLocation();
    const [map, setMap] = useState(null);
    const zoom = useRef(initialZoom);
    const [loadingCoordinates, setLoadingCoordinates] = useState(true);
    const [coordinates, setCoordinates] = useState({});
    const [filteredCoordinates, setFilteredCoordinates] = useState({});
    const [dataSetLayers, setDataSetLayers] = useState({});
    // const [unfilteredDataLayer, setUnfilteredDataLayer] = useState({});
    const [flightPaths, setFlightPaths] = useState({});

    // AIS data state
    const [aisData, setAisData] = useState({});
    const [filteredAisData, setFilteredAisData] = useState({});

    // const [loadingAisData, setLoadingAisData] = useState(true);
    const [aisDataLayers, setAisDataLayers] = useState({});

    // Audio data state
    const [audioData, setAudioData] = useState({});
    const [filteredAudioData, setFilteredAudioData] = useState({});
    // eslint-disable-next-line no-unused-vars
    const [loadingAudioData, setLoadingAudioData] = useState(true);
    const [audioDataMarkers, setAudioDataMarkers] = useState({});

    const clustererRef = useRef(null);
    const audioClustererRef = useRef(null);
    const portsClustererRef = useRef(null);
    const portsUpdateTimeoutRef = useRef(null);
    const loadingVesselsRef = useRef([]);

    // eslint-disable-next-line no-unused-vars
    const [loadingArtifacts, setLoadingArtifacts] = useState(true);
    const [artifacts, setArtifacts] = useState({});
    const artifactsMarkers = useRef({});
    const [filteredArtifacts, setFilteredArtifacts] = useState({});
    const [center, setCenter] = useState(defaultCenter);
    const eezLayerRef = useRef(null);
    const eezGeoJsonCacheRef = useRef(null);
    const portsGeoJsonCacheRef = useRef(null);
    const portsMarkersRef = useRef([]);

    const infoWindow = useMemo(() => new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    const artifactInfowWindow = useMemo(() => new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    const audioInfoWindow = useMemo(() => new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    let currentAudioClusterInfoWindow = useMemo(() => new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    let currentClusterInfoWindow = useMemo(() => new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    const streamInfoWindow = useMemo(() => new google.maps.InfoWindow({ disableAutoPan: true }), [google]);
    const [streamInfoWindowContent, setStreamInfoWindowContent] = useState(null);

    const prevFilteredCoordinates = useRef({});
    const prevFilteredArtifacts = useRef({});
    const prevFilteredAisData = useRef({});
    const prevFilteredAudioData = useRef({});

    const cachedSrc = useRef({});
    const { user } = useUser();

    const isPlayingVideo = useRef(false);
    const scrubberMarker = useRef(new google.maps.Marker({
        map: null,
    }));

    // fetch a specific artifact and add it to the artifacts state when navigating from events page modal
    const fetchAndAddArtifact = async (artifactId) => {
        try {
            const artifact = await artifactController.getArtifactDetail(artifactId);
            if (artifact && artifact.onboard_vessel_id) {
                const vesselId = artifact.onboard_vessel_id;
                // Add the artifact to the artifacts state
                setArtifacts((prev) => {
                    const newArtifacts = { ...prev };
                    if (!newArtifacts[vesselId]) {
                        newArtifacts[vesselId] = [];
                    }
                    const existingIndex = newArtifacts[vesselId].findIndex((a) => a._id === artifact._id);
                    if (existingIndex === -1) {
                        newArtifacts[vesselId] = [
                            ...newArtifacts[vesselId],
                            {
                                _id: artifact._id,
                                location: artifact.location,
                                onboard_vessel_id: artifact.onboard_vessel_id,
                                super_category: artifact.super_category,
                                timestamp: artifact.timestamp,
                                video_exists: artifact.video_path ? true : false,
                            },
                        ];
                    }
                    return newArtifacts;
                });
                return artifact;
            } else {
                return null;
            }
        } catch (error) {
            console.error("Error fetching artifact:", error);
            return null;
        }
    };

    useEffect(() => {
        console.count("MainMap rerendered");
    });

    useEffect(() => {
        const centerVessel = filteredCoordinates[focusedVessel];
        if (!centerVessel && center) return;
        if (!centerVessel && !center) return setCenter(defaultCenter);
        const lastCoord = centerVessel?.[centerVessel.length - 1];
        if (!lastCoord) return;
        setCenter({ lat: lastCoord[2], lng: lastCoord[3] });
    }, [focusedVessel]);

    const storePrevFilteredArtifacts = useCallback(
        (filteredArtifacts) => {
            if (!map) return;
            // prevFilteredArtifacts.current = { ...filteredArtifacts };
            prevFilteredArtifacts.current = Object.keys(filteredArtifacts).reduce(
                (acc, key) => ({ ...acc, [key]: filteredArtifacts[key].length }),
                {},
            );
        },
        [map],
    );

    const storePrevFilteredAisData = useCallback(
        (filteredAisData) => {
            if (!map) return;
            prevFilteredAisData.current = { ...filteredAisData };
        },
        [map],
    );

    const storePrevFilteredAudioData = useCallback(
        (filteredAudioData) => {
            if (!map) return;
            // prevFilteredAudioData.current = { ...filteredAudioData };
            prevFilteredAudioData.current = Object.keys(filteredAudioData).reduce(
                (acc, key) => ({ ...acc, [key]: filteredAudioData[key].length }),
                {},
            );
        },
        [map],
    );

    //artifact range setting and loading
    useEffect(() => {
        storePrevFilteredArtifacts(filteredArtifacts);
        const applyFilters = async () => {
            const computedArtifacts = { ...artifacts };
            Object.keys(computedArtifacts).forEach((key) => {
                computedArtifacts[key] = artifacts[key]
                    .filter(
                        (a) =>
                            dayjs(a.timestamp).valueOf() >= journeyStart.valueOf() &&
                            dayjs(a.timestamp).valueOf() <= (journeyEnd === "now" ? dayjs().valueOf() : journeyEnd.valueOf()),
                    ) // filter for time range
                    .filter((a) => {
                        if (!selectedArtifactsCategory || selectedArtifactsCategory.length === 0) {
                            return false;
                        }
                        return selectedArtifactsCategory.includes(a.super_category);
                    }) // filter for category
                    .filter((a) => {
                        if (artifactsType === "video") {
                            return a.video_exists;
                        } else if (artifactsType === "image") {
                            return !a.video_exists;
                        }
                        return true;
                    }); //filter for artifact type
                if (computedArtifacts[key].length >= selectedNumberOfArtifacts && selectedNumberOfArtifacts !== "all") {
                    computedArtifacts[key] = computedArtifacts[key].slice(0, selectedNumberOfArtifacts - 1);
                    console.warn(
                        "Category",
                        selectedArtifactsCategory,
                        "has a lot of artifacts. Only showing",
                        selectedNumberOfArtifacts,
                        "to conserve resources for type",
                        artifactsType,
                    );
                }
            });

            const homePortsFilteredArtifacts = await filterArtifactsNearHomePorts(computedArtifacts, homePortsArtifactsMode, vesselInfo);
            const aisFilteredArtifacts = filterArtifactsByAis(homePortsFilteredArtifacts, aisArtifactsFilter);

            setFilteredArtifacts(aisFilteredArtifacts);
        };

        applyFilters();
    }, [artifacts, selectedArtifactsCategory, selectedNumberOfArtifacts, artifactsType, journeyStart, journeyEnd, homePortsArtifactsMode, aisArtifactsFilter]);

    useEffect(() => {
        if (artifacts && artifacts != {}) {
            const totalCount = Object.values(filteredArtifacts).reduce((sum, artifactsArray) => sum + (artifactsArray?.length || 0), 0);
            setTotalArtifactsCount(totalCount);
        }
    }, [filteredArtifacts]);

    const storePrevCoordinates = useCallback(
        (filteredCoordinates) => {
            if (!map) return;
            prevFilteredCoordinates.current = Object.keys(filteredCoordinates).reduce(
                (acc, key) => ({ ...acc, [key]: filteredCoordinates[key].length }),
                {},
            );
        },
        [map],
    );

    // useEffect(() => {
    //     const ts = Date.now();
    //     console.log("[computing coordinates] executing...");
    //     storePrevCoordinates(filteredCoordinates);
    //     const computedCoordinates = { ...coordinates };
    //     console.log(
    //         "[computing coorindates] length",
    //         Object.keys(coordinates).reduce((acc, key) => acc + coordinates[key].length, 0),
    //     );
    //     const journeyStartValue = dayjs(journeyStart).valueOf();
    //     const journeyEndValue = journeyEnd === "now" ? dayjs().valueOf() : dayjs(journeyEnd).valueOf();
    //     Object.keys(computedCoordinates).forEach((key) => {
    //         computedCoordinates[key] = coordinates[key]
    //             .filter((c) => {
    //                 const currTimestamp = new Date(c.timestamp).getTime();
    //                 return currTimestamp >= journeyStartValue && currTimestamp <= journeyEndValue;
    //             }) // filter for time range
    //             // .filter((_, i, self) => i % interval === 0 || i === self.length - 1) // filter for interval
    //             .map((c) => ({ ...c, lat: c.lat, lng: c.lng })); // map for location
    //         // .filter(
    //         //     (value, index, self) => index === self.length - 1 || self.findIndex((v) => v.lat === value.lat && v.lng === value.lng) === index,
    //         // ); // remove unnecessary duplicated coordinates

    //         // Compute rotation
    //         const headingTs = Date.now();
    //         for (let i = 0; i < computedCoordinates[key].length; i++) {
    //             if (i < computedCoordinates[key].length - 1) {
    //                 computedCoordinates[key][i].rotation = google.maps.geometry.spherical.computeHeading(
    //                     new google.maps.LatLng(computedCoordinates[key][i].lat, computedCoordinates[key][i].lng),
    //                     new google.maps.LatLng(computedCoordinates[key][i + 1].lat, computedCoordinates[key][i + 1].lng)
    //                 );
    //             } else {
    //                 computedCoordinates[key][i].rotation = 0; // Last point
    //             }
    //         }
    //         console.log(
    //             `[computing coordinates] computed rotation for ${key} in`,
    //             Date.now() - headingTs,
    //             "ms"
    //         );
    //     });
    //     setFilteredCoordinates(computedCoordinates);
    //     console.log("[computing coordinates] done in", Date.now() - ts, "ms");
    // }, [coordinates, journeyStart, journeyEnd]);

    useEffect(() => {
        const ts = Date.now();
        console.log("[computing coordinates] executing...");
        storePrevCoordinates(filteredCoordinates);
        const computedCoordinates = {};
        // console.log(
        //     "[computing coorindates] length",
        //     Object.keys(coordinates).reduce((acc, key) => acc + coordinates[key].length, 0),
        // );
        const journeyStartValue = dayjs(journeyStart).valueOf();
        const journeyEndValue = journeyEnd === "now" ? dayjs().valueOf() : dayjs(journeyEnd).valueOf();

        Object.keys(coordinates).forEach((key) => {
            const filteredList = [];
            const originalList = coordinates[key];

            let prev = null;

            for (const point of originalList) {
                const currTimestamp = new Date(point[1]).getTime();
                if (currTimestamp < journeyStartValue || currTimestamp > journeyEndValue) continue;
                // Precision logic removed - using original coordinate values
                // const current = { ...point, lat, lng };
                if (prev) {
                    prev[5] = google.maps.geometry.spherical.computeHeading(
                        new google.maps.LatLng(prev[2], prev[3]),
                        new google.maps.LatLng(point[2], point[3]),
                    );
                    filteredList.push(prev);
                }
                // Set current as next previous
                prev = point;
            }
            // Handle last point (no rotation)
            if (prev) {
                prev[5] = 0;
                filteredList.push(prev);
            }
            computedCoordinates[key] = filteredList;
            console.log(`[computing coordinates] processed ${filteredList.length} points for ${key}`);
        });
        // console.log("[computing coordinates] computedCoordinates", computedCoordinates);
        setFilteredCoordinates(computedCoordinates);
        console.log("[computing coordinates] done in", Date.now() - ts, "ms");
    }, [coordinates, journeyStart, journeyEnd]);

    // AIS data filtering effect
    useEffect(() => {
        storePrevFilteredAisData(filteredAisData);
        const ts = Date.now();
        console.log("[computing AIS data] executing...");
        // const computedAisData = {};
        // console.log(
        //     "[computing AIS data] length",
        //     Object.keys(aisData).reduce((acc, key) => acc + aisData[key].length, 0),
        // );
        // deduplicate aisMessages: keep the latest message for each mmsi
        const deduplicatedAisMessages = Object.values(aisData)
            .flat()
            .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
            .reduce(
                (acc, ais) => {
                    if (acc.processed.includes(ais.metadata.mmsi)) return acc;
                    if (!acc.messages[ais.onboard_vessel_id]) acc.messages[ais.onboard_vessel_id] = [];
                    acc.messages[ais.onboard_vessel_id].push(ais);
                    acc.processed.push(ais.metadata.mmsi);
                    return acc;
                },
                { messages: {}, processed: [] },
            ).messages;
        // .filter((ais, index, self) => index === self.findIndex((t) => t.mmsi === ais.mmsi));
        // const journeyStartValue = dayjs(journeyStart).valueOf();
        // const journeyEndValue = journeyEnd === "now" ? dayjs().valueOf() : dayjs(journeyEnd).valueOf();

        // Object.keys(aisData).forEach((key) => {
        //     const filteredList = [];
        //     const originalList = aisData[key];

        //     for (const point of originalList) {
        //         const currTimestamp = new Date(point.timestamp).getTime();
        //         if (currTimestamp < journeyStartValue || currTimestamp > journeyEndValue) continue;
        //         filteredList.push(point);
        //     }
        //     computedAisData[key] = filteredList;
        //     console.log(`[computing AIS data] processed ${filteredList.length} points for ${key}`);
        // });
        setFilteredAisData(deduplicatedAisMessages);
        console.log("[computing AIS data] done in", Date.now() - ts, "ms");
    }, [aisData, storePrevFilteredAisData]);

    // Audio data filtering effect
    useEffect(() => {
        storePrevFilteredAudioData(filteredAudioData);
        const ts = Date.now();
        console.log("[computing audio data] executing...");
        const computedAudioData = {};
        // console.log(
        //     "[computing audio data] length",
        //     Object.keys(audioData).reduce((acc, key) => acc + audioData[key].length, 0),
        // );
        const journeyStartValue = dayjs(journeyStart).valueOf();
        const journeyEndValue = journeyEnd === "now" ? dayjs().valueOf() : dayjs(journeyEnd).valueOf();

        Object.keys(audioData).forEach((key) => {
            const filteredList = [];
            const originalList = audioData[key];
            // console.log("AudioData", audioData);
            for (const point of originalList) {
                const currTimestamp = new Date(point.timestamp).getTime();
                if (currTimestamp < journeyStartValue || currTimestamp > journeyEndValue) continue;
                filteredList.push(point);
            }
            computedAudioData[key] = filteredList;
            console.log(`[computing audio data] processed ${filteredList.length} points for ${key}`);
        });
        setFilteredAudioData(computedAudioData);
        console.log("[computing audio data] done in", Date.now() - ts, "ms");
    }, [audioData, journeyStart, journeyEnd, storePrevFilteredAudioData]);

    const vesselsRef = useRef(vessels);
    useEffect(() => {
        const skipVesselsCoordinates = vessels
            .filter((v) => vesselsRef.current.find((_v) => _v.id === v.id) && Object.keys(coordinates).includes(v.id))
            .map((v) => v.id);
        const skipVesselsArtifacts = vessels
            .filter((v) => vesselsRef.current.find((_v) => _v.id === v.id) && Object.keys(artifacts).includes(v.id))
            .map((v) => v.id);
        const skipVesselsAis = vessels
            .filter((v) => vesselsRef.current.find((_v) => _v.id === v.id) && Object.keys(aisData).includes(v.id))
            .map((v) => v.id);
        const skipVesselsAudio = vessels
            .filter((v) => vesselsRef.current.find((_v) => _v.id === v.id) && Object.keys(audioData).includes(v.id))
            .map((v) => v.id);

        if (vessels.length === 0) {
            setCoordinates({});
            setArtifacts({});
            setAisData({});
            setAudioData({});
            return;
        }

        if (Object.keys(coordinates).some((vesselName) => !vessels.find((v) => v.id === vesselName))) {
            setCoordinates((v) => {
                const newCoordinates = {};
                Object.keys(coordinates)
                    .filter((vesselName) => vessels.find((v) => v.id === vesselName))
                    .forEach((vesselName) => {
                        newCoordinates[vesselName] = v[vesselName];
                    });
                return newCoordinates;
            });
        }
        if (Object.keys(artifacts).some((vesselName) => !vessels.find((v) => v.id === vesselName))) {
            setArtifacts((v) => {
                const newArtifacts = {};
                Object.keys(artifacts)
                    .filter((vesselName) => vessels.find((v) => v.id === vesselName))
                    .forEach((vesselName) => {
                        newArtifacts[vesselName] = v[vesselName];
                    });
                return newArtifacts;
            });
        }
        if (Object.keys(aisData).some((vesselName) => !vessels.find((v) => v.id === vesselName))) {
            setAisData((v) => {
                const newAisData = {};
                Object.keys(aisData)
                    .filter((vesselName) => vessels.find((v) => v.id === vesselName))
                    .forEach((vesselName) => {
                        newAisData[vesselName] = v[vesselName];
                    });
                return newAisData;
            });
        }
        if (Object.keys(audioData).some((vesselName) => !vessels.find((v) => v.id === vesselName))) {
            setAudioData((v) => {
                const newAudioData = {};
                Object.keys(audioData)
                    .filter((vesselName) => vessels.find((v) => v.id === vesselName))
                    .forEach((vesselName) => {
                        newAudioData[vesselName] = v[vesselName];
                    });
                return newAudioData;
            });
        }

        vesselsRef.current = vessels;

        // console.log("vesselsRef.current", vesselsRef.current);

        fetchCoordinates({ skipVessels: skipVesselsCoordinates });
        fetchArtifacts({ skipVessels: skipVesselsArtifacts });
        fetchAisData({ skipVessels: skipVesselsAis });
        fetchAudioData({ skipVessels: skipVesselsAudio });
    }, [vessels]);

    useEffect(() => {
        fetchCoordinates();
        fetchArtifacts();
        // fetchAisData();
        fetchAudioData();
    }, [timeSlider]);

    const autoRefreshIntervalRef = useRef(null);
    // auto refreh every 5m
    useEffect(() => {
        if (journeyEnd === "now") {
            if (autoRefreshIntervalRef.current) {
                clearInterval(autoRefreshIntervalRef.current);
            }
            autoRefreshIntervalRef.current = setInterval(
                () => {
                    console.info("refreshing map data");
                    fetchCoordinates();
                    fetchArtifacts();
                    fetchAisData();
                    fetchAudioData();
                },
                5 * 60 * 1000,
            );
        } else {
            clearInterval(autoRefreshIntervalRef.current);
        }
        return () => clearInterval(autoRefreshIntervalRef.current);
    }, [timeSlider, journeyEnd, coordinates]);

    // useEffect(() => {
    //     let aisInterval = null;
    //     fetchAisData();

    //     aisInterval = setInterval(
    //         () => {
    //             console.info("refreshing AIS data");
    //             fetchAisData();
    //         },
    //         5 * 60 * 1000,
    //     );
    //     return () => clearInterval(aisInterval);
    // }, []);

    useEffect(() => {
        const fetchFilters = async () => {
            try {
                const filterItems = await axiosInstance
                    .get("/artifacts/filters")
                    .then((res) => res.data)
                    .catch((err) => {
                        console.error(`Error fetching filters in Events`, err);
                    });
                if (Object.keys(filterItems).length !== 0) {
                    const categories = filterItems["superCategories"] || [];
                    setArtifactsCategories(categories);
                    setSelectedArtifactsCategory(categories);
                }
            } catch (err) {
                console.error(`Error fetching filters in Events`, err);
            }
        };
        fetchFilters();

        const socket = getSocket();
        const handleArtifactChanged = (data) => {
            const updatedArtifact = data?.artifact;
            if (!updatedArtifact) return;
            updatedArtifact.lat = updatedArtifact.location.coordinates[1];
            updatedArtifact.lng = updatedArtifact.location.coordinates[0];
            setArtifacts((prev) => {
                if (!updatedArtifact.onboard_vessel_id) return prev;
                const newArtifacts = { ...prev };
                const vesselId = updatedArtifact.onboard_vessel_id;
                const vesselArtifacts = newArtifacts[vesselId] ? [...newArtifacts[vesselId]] : [];

                if (updatedArtifact?.portal?.is_archived) {
                    newArtifacts[vesselId] = vesselArtifacts.filter((a) => a._id !== updatedArtifact._id);
                } else {
                    const idx = vesselArtifacts.findIndex((a) => a._id === updatedArtifact._id);
                    if (idx !== -1) {
                        vesselArtifacts[idx] = updatedArtifact;
                    } else {
                        vesselArtifacts.push(updatedArtifact);
                    }
                    newArtifacts[vesselId] = vesselArtifacts;
                }

                return newArtifacts;
            });
        };
        socket.on("artifact/changed", handleArtifactChanged);
        return () => socket.off("artifact/changed", handleArtifactChanged);
    }, []);

    /** This has been removed because this leads to unnecessary rerender */
    // useEffect(() => {
    //     const vesselIds = Object.keys(coordinates);

    //     const addCoordinate = (data) => {
    //         console.log('[addCoordinate] data', data);
    //         setCoordinates((v) => {
    //             if (!v[data.vesselName]) {
    //                 return v;
    //             }
    //             const new_coordinates = { ...v };
    //             new_coordinates[data.vesselName] = new_coordinates[data.vesselName].concat({ ...data, lat: data.latitude, lng: data.longitude });
    //             return new_coordinates;
    //         });
    //     };

    //     vesselIds.forEach((id) => {
    //         gps_socket.on(`${id}_location/insert`, addCoordinate);
    //     });

    //     return () => {
    //         vesselIds.forEach((id) => {
    //             gps_socket.off(`${id}_location/insert`, addCoordinate);
    //         });
    //     };
    // }, [coordinates]);

    useEffect(() => {
        prevFilteredCoordinates.current = {};
    }, [datapointsDistance, showDatapoints, showFilteredCoordinates]);

    // Draw polylines (stable unless time range or raw coordinates change)
    useEffect(() => {
        if (!map) return;
        console.log("calling drawPolylines");
        drawPolylines({
            filteredCoordinates,
            prevFilteredCoordinates,
            flightPaths,
            dataSetLayers,
            map,
            journeyStart,
            journeyEnd,
            showFilteredCoordinates,
            coordinates,
            google,
            setFlightPaths,
            artifactInfowWindow,
            currentClusterInfoWindow,
            streamInfoWindow,
            setStreamInfoWindowContent,
            zoomLevelRef: zoom,
            isPlayingVideoRef: isPlayingVideo,
            scrubberMarkerRef: scrubberMarker,
        });
    }, [map, coordinates, filteredCoordinates, google, showFilteredCoordinates, journeyStart, journeyEnd]);

    // Draw data points (sensitive to datapointsDistance, interval, showDatapoints)
    useEffect(() => {
        if (!map) return;
        console.log("calling drawDataPoints");
        drawDataPoints({
            user,
            filteredCoordinates,
            prevFilteredCoordinates,
            dataSetLayers,
            setDataSetLayers,
            map,
            infoWindow,
            datapointsDistance,
            showDatapoints,
            google,
            timezone,
            interval,
            vesselInfo,
            vessels,
            artifactInfowWindow,
            currentClusterInfoWindow,
            streamInfoWindow,
            setStreamInfoWindowContent,
            scrubberMarkerRef: scrubberMarker
        });
    }, [map, filteredCoordinates, datapointsDistance, showDatapoints, google, timezone, interval]);

    useEffect(() => {
        if (!map) return;
        console.log("calling drawArtifacts");
        drawArtifacts(
            showArtifacts,
            artifactsMarkers,
            google,
            clustererRef,
            map,
            filteredArtifacts,
            prevFilteredArtifacts,
            artifactInfowWindow,
            currentClusterInfoWindow,
            currentAudioClusterInfoWindow,
            audioInfoWindow,
            streamInfoWindowClose,
            user,
            timezone,
            vesselInfo,
            cachedSrc,
        );
    }, [map, filteredArtifacts, showArtifacts]);

    useEffect(() => {
        if (!map) return;
        // if (aisData.length < 0) return;
        drawAisData({
            user,
            filteredAisData,
            prevFilteredAisData,
            aisDataLayers,
            setAisDataLayers,
            vessels,
            map,
            infoWindow,
            google,
            timezone,
            vesselInfo,
            showAisData,
        });
        // filteredAisData
    }, [map, showAisData, google, vesselInfo, filteredAisData]);

    // Clear AIS data layers when showAisData is toggled off
    // useEffect(() => {
    //     if (!map || showAisData) return;

    // Clear all AIS data layers when showAisData is false
    // Object.keys(aisDataLayers).forEach((vesselName) => {
    //     const dataLayer = aisDataLayers[vesselName];
    //     if (dataLayer) {
    //         dataLayer.forEach((feature) => dataLayer.remove(feature));
    //         dataLayer.setMap(null);
    //     }
    // });
    // setAisDataLayers({});
    //     if (aisDataLayers) {
    //         aisDataLayers.setMap(null);
    //     }
    //     setAisDataLayers(null)
    // }, [showAisData, map, aisDataLayers]);

    useEffect(() => {
        if (!map) return;
        console.log("calling drawAudioData");
        drawAudioData({
            user,
            filteredAudioData,
            prevFilteredAudioData,
            audioDataMarkers,
            setAudioDataMarkers,
            vessels,
            map,
            google,
            timezone,
            vesselInfo,
            showAudioData,
            audioClustererRef,
            audioInfoWindow,
            currentAudioClusterInfoWindow,
            artifactInfowWindow,
            currentClusterInfoWindow,
            infoWindow,
            streamInfoWindowClose,
        });
    }, [map, filteredAudioData, showAudioData, google, vesselInfo]);

    useEffect(() => {
        setAvailableVessels(vessels.filter((v) => filteredCoordinates[v.id] && filteredCoordinates[v.id].length > 0).map((v) => v.id));
        setEmptyVessels(vessels.filter((v) => filteredCoordinates[v.id] && filteredCoordinates[v.id].length === 0).map((v) => v.id));
        if (loadingCoordinates) return;
        if (vessels.length === 0 && pathname.includes("/map")) {
            toaster("No vessel selected", { variant: "warning" });
        }
    }, [vessels, filteredCoordinates]);

    useEffect(() => {
        const errorEmptyVessels = [...errorsomeVessels, ...emptyVessels];
        const isErrorOrEmpty = errorEmptyVessels.some((vessel) => vessel.id !== focusedVessel);
        if (loadingCoordinates) return;
        if (
            loadingVessels.length == 0 &&
            isErrorOrEmpty &&
            Object.values(filteredCoordinates).every((v) => v.length === 0) &&
            pathname.includes("/map")
        ) {
            toaster("No coordinates found for the selected vessels", { variant: "warning" });
        }
    }, [loadingVessels, errorsomeVessels, emptyVessels]);

    useEffect(() => {
        Object.keys(dataSetLayers).forEach((vesselName) => {
            const dataLayer = dataSetLayers[vesselName];
            if (!dataLayer) return;

            let lastFeature = null;
            dataLayer.forEach((feature) => {
                lastFeature = feature;
            });

            if (!lastFeature) return;

            const color = focusedVessel === vesselName ? "#ff0000" : defaultValues.polylineColors[vesselName] || "#0000FF";

            dataLayer.overrideStyle(lastFeature, {
                icons: [
                    {
                        icon: {
                            path: defaultValues.icons.location,
                            strokeColor: "#000000",
                            strokeWeight: 0.5,
                            scale: 1.2,
                            fillColor: color,
                            fillOpacity: 1,
                            anchor: new google.maps.Point(12, 21),
                        },
                    },
                ],
            });
        });
    }, [focusedVessel, dataSetLayers]);

    // const coordinatesCurrentlyLoading = useRef(new Map());
    // const artifactsCurrentlyLoading = useRef(new Map());
    // const aisDataCurrentlyLoading = useRef(new Map());
    // const audioDataCurrentlyLoading = useRef(new Map());
    const fetchCoordinates = ({ skipVessels = [] } = {}) => {
        const vesselIds = vesselsRef.current.filter((v) => !skipVessels.includes(v.id)).map((v) => v.id);
        console.log("[fetching-map-data] fetching coordinates for vessels", vesselIds.length);
        if (vesselIds.length === 0) return;
        // const ts = Date.now();
        // const store = vessel.id + "_location";
        // const filter = (item) => dayjs(item.timestamp).isBetween(timeSlider[0], timeSlider[1], null, "[]");
        // idb.getItems(store, filter)
        //     .then((storedCoordinates) => {
        //     })
        //     .catch((err) => {
        //         console.error(err);
        //         reject({
        //             vessel,
        //             err,
        //         });
        //     });
        // if (skipVessels.includes(vessel.id)) {
        //     return resolve({
        //         vessel,
        //         coordinates: coordinates[vessel.id],
        //     });
        // }
        setLoadingVessels((v) => {
            loadingVesselsRef.current = v.concat(vesselIds);
            return loadingVesselsRef.current;
        });
        // const filteredStoredCoordinates = storedCoordinates.filter((item) =>
        //     dayjs(item.timestamp).isBetween(timeSlider[0], timeSlider[1], null, "[]"),
        // );
        // console.log(vessel.id, 'time taken to get stored coordinates', Date.now() - ts, 'ms');
        const payload = {
            vesselIds: vesselIds.join(","),
            startTimestampISO: dayjs(timeSlider[0]).toISOString(),
            endTimestampISO: journeyEnd === "now" ? dayjs().toISOString() : dayjs(timeSlider[1]).toISOString(),
            // excludeIds: filteredStoredCoordinates.map((item) => item._id),
        };
        // coordinatesCurrentlyLoading.current.set(vessel.id, {
        //     vessel_id: vessel.vessel_id,
        //     payload,
        // });
        axiosInstance
            .get(`/vesselLocations/bulk`, { params: payload }, { meta: { showSnackbar: false } })
            .then((res) => {
                const vesselCoordinates = res.data;
                // if (res.data.length > 0) storeCoordinates(vessel.id, res.data);
                // const coordinates = res.data
                //     // .concat(storedCoordinates)
                //     .map((c) => ({
                //         ...c,
                //         lat: c.latitude,
                //         lng: c.longitude,
                //     }))
                //     .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
                // Update vessel availability immediately
                Object.entries(vesselCoordinates).forEach(([vesselId, coordinates]) => {
                    if (coordinates.length > 0) {
                        setAvailableVessels((prev) => {
                            if (prev.some((id) => id === vesselId)) return prev;
                            return [...prev, vesselId];
                        });
                        setEmptyVessels((prev) => prev.filter((id) => id !== vesselId));
                    } else {
                        setEmptyVessels((prev) => {
                            if (prev.some((id) => id === vesselId)) return prev;
                            return [...prev, vesselId];
                        });
                        setAvailableVessels((prev) => prev.filter((id) => id !== vesselId));
                    }
                });
                // Set coordinates only if vessel is still in the ref list
                setCoordinates((prev) => ({
                    ...prev,
                    ...Object.keys(vesselCoordinates).reduce((acc, vesselId) => {
                        if (vesselsRef.current.find((v) => v.id === vesselId)) {
                            acc[vesselId] = vesselCoordinates[vesselId];
                        }
                        return acc;
                    }, {}),
                }));
                // if (coordinates.length > 0) {
                //     setAvailableVessels((prev) => {
                //         if (prev.some((v) => v.id === vessel.id)) return prev;
                //         return [...prev, vessel];
                //     });
                //     setEmptyVessels((prev) => prev.filter((v) => v.id !== vessel.id));
                // } else {
                //     setEmptyVessels((prev) => {
                //         if (prev.some((v) => v.id === vessel.id)) return prev;
                //         return [...prev, vessel];
                //     });
                //     setAvailableVessels((prev) => prev.filter((v) => v.id !== vessel.id));
                // }
                // Set coordinates only if vessel is still in the ref list
                // if (vesselsRef.current.find((v) => v.id === vessel.id)) {
                //     setCoordinates((prev) => ({ ...prev, [vessel.id]: coordinates }));
                // }
                // resolve();
            })
            .catch((err) => {
                console.error("Error fetching coordinates:", err);
                // Track error vessels here (replacing outer .then)
                // setErrorsomeVessels((prev) => {
                //     if (prev.some((v) => v.id === vessel.id)) return prev;
                //     return [...prev, vessel];
                // });
                // reject(err);
            })
            .finally(() => {
                // coordinatesCurrentlyLoading.current.delete(vessel.id);
                setLoadingVessels((v) => {
                    loadingVesselsRef.current = v.filter((id) => !vesselIds.includes(id));
                    return loadingVesselsRef.current;
                });
                setLoadingCoordinates(false);
            });
        //                 }),
        //         ),
        // )
        //     .catch((err) => {
        //         console.error("Unexpected error in fetchCoordinates:", err);
        //     })
        //     .finally(() => setLoadingCoordinates(false));
    };

    const fetchArtifacts = ({ skipVessels = [] } = {}) => {
        const vesselIds = vesselsRef.current.filter((v) => !skipVessels.includes(v.id)).map((v) => v.id);
        console.log("[fetching-map-data] fetching artifacts for vessels", vesselIds.length);
        if (vesselIds.length === 0) return;
        // skipVessels = []
        // clearTimeout(artifactsTimeout.current);
        // artifactsTimeout.current = setTimeout(() => {
        // Promise.all(
        //     vesselsRef.current
        //         .filter((v) => !skipVessels.includes(v.id) && !artifactsCurrentlyLoading.current.has(v.id))
        //         .map(
        //             (vessel) =>
        //                 new Promise((resolve, reject) => {
        // const store = vessel.id + "_artifact";
        // idb.getItems(store)
        //     .then((storedArtifacts) => {
        //         const filteredStoredArtifacts = storedArtifacts.filter((item) =>
        //             dayjs(item.timestamp).isBetween(timeSlider[0], timeSlider[1], null, "[]"),
        //         );
        //         const payload = {
        //             startTimestamp: timeSlider[0],
        //             endTimestamp: journeyEnd === "now" ? dayjs().valueOf() : timeSlider[1],
        //             excludeIds: filteredStoredArtifacts.map((item) => item._id),
        //             favourites: 1,
        //         };
        //         artifactsCurrentlyLoading.current.set(vessel.id, {
        //             vessel_id: vessel.vessel_id,
        //             payload,
        //         });
        const payload = {
            vesselIds: vesselIds.join(","),
            startTimestampISO: dayjs(timeSlider[0]).toISOString(),
            endTimestampISO: journeyEnd === "now" ? dayjs().toISOString() : dayjs(timeSlider[1]).toISOString(),
            // excludeIds: filteredStoredArtifacts.map((item) => item._id),
        };
        axiosInstance
            .get("/artifacts/bulk", { params: payload }, { meta: { showSnackbar: false } })
            .then((res) => {
                // console.log("artifact data in map", res.data, 'favourites', res.data.favouritesArtifacts)
                // if (res.data.artifacts.length > 0) {
                //     // storeArtifacts(vessel.id, res.data.artifacts);
                //     setFavouriteArtifacts(res.data.favouritesArtifacts);
                //     // console.log("coming here to update the ref", res.data.favouriteArtifacts, "favourtieArtifacts", favouriteArtifacts)
                //     favouriteArtifactsRef.current = res.data.favouritesArtifacts;
                // }
                // const artifacts = res.data.artifacts.map((a) => ({
                //     ...a,
                //     lat: a.location.coordinates[1],
                //     lng: a.location.coordinates[0],
                // }));
                // resolve({
                //     vessel,
                //     artifacts,
                // });
                // setArtifacts((prev) => {
                //     const newArtifacts = {};
                //     ress.filter((res) => vesselsRef.current.some((v) => v.id === res.vessel.id)).forEach((res) => {
                //         newArtifacts[res.vessel.id] = res.artifacts;
                //     });
                //     return { ...prev, ...newArtifacts };
                // });

                setArtifacts((prev) => ({
                    ...prev,
                    ...Object.keys(res.data.artifacts).reduce((acc, vesselId) => {
                        if (vesselsRef.current.find((v) => v.id === vesselId)) {
                            acc[vesselId] = res.data.artifacts[vesselId];
                        }
                        return acc;
                    }, {}),
                }));
            })
            .catch((err) => {
                console.error(err);
                // reject({
                //     vessel,
                //     err,
                // });
            });
        // .finally(() => {
        //     artifactsCurrentlyLoading.current.delete(vessel.id);
        // });
        //     })
        //             .catch ((err) => {
        //     console.error(err);
        //     reject({
        //         vessel,
        //         err,
        //     });
        // });
        //     }),
        //                 ),
        //         )
        //             .then((ress) => {
        //     if (ress.length === 0) return;
        //     setArtifacts((prev) => {
        //         const newArtifacts = {};
        //         ress.filter((res) => vesselsRef.current.some((v) => v.id === res.vessel.id)).forEach((res) => {
        //             newArtifacts[res.vessel.id] = res.artifacts;
        //         });
        //         return { ...prev, ...newArtifacts };
        //     });
        // })
        //     .catch(console.error)
        //     .finally(() => setLoadingArtifacts(false));
        //         // }, 500);
    };

    // const fetchAisData = ({ skipVessels = [] } = {}) => {
    //     Promise.allSettled(
    //         vesselsRef.current
    //             .filter((v) => !skipVessels.includes(v.id) && !aisDataCurrentlyLoading.current.has(v.id))
    //             .map(
    //                 (vessel) =>
    //                     new Promise((resolve, reject) => {
    //                         setLoadingVessels((v) => {
    //                             loadingVesselsRef.current = v.concat(vessel.id);
    //                             return loadingVesselsRef.current;
    //                         });
    //                         const payload = {
    //                             startTimestamp: timeSlider[0],
    //                             endTimestamp: journeyEnd === "now" ? dayjs().valueOf() : timeSlider[1],
    //                         };
    //                         aisDataCurrentlyLoading.current.set(vessel.id, {
    //                             vessel_id: vessel.vessel_id,
    //                             payload,
    //                         });
    //                         axiosInstance
    //                             .post(`/vesselAis/${vessel.vessel_id}`, payload, { meta: { showSnackbar: false } })
    //                             .then((res) => {
    //                                 // Set AIS data only if vessel is still in the ref list
    //                                 if (vesselsRef.current.find((v) => v.id === vessel.id)) {
    //                                     setAisData((prev) => ({ ...prev, [vessel.id]: res.data }));
    //                                     console.log("AISData : set ais data for each vessel");
    //                                 }
    //                                 resolve();
    //                             })
    //                             .catch((err) => {
    //                                 console.error("Error fetching AIS data:", err);
    //                                 // Track error vessels here
    //                                 setErrorsomeVessels((prev) => {
    //                                     if (prev.some((v) => v.id === vessel.id)) return prev;
    //                                     return [...prev, vessel];
    //                                 });
    //                                 reject(err);
    //                             })
    //                             .finally(() => {
    //                                 aisDataCurrentlyLoading.current.delete(vessel.id);
    //                                 setLoadingVessels((v) => {
    //                                     loadingVesselsRef.current = v.filter((id) => id !== vessel.id);
    //                                     return loadingVesselsRef.current;
    //                                 });
    //                             });
    //                     }),
    //             ),
    //     )
    //         .catch((err) => {
    //             console.error("Unexpected error in fetchAisData:", err);
    //         })
    //         .finally(() => setLoadingAisData(false));
    // };

    const fetchAisData = ({ skipVessels = [] } = {}) => {
        try {
            // TODO: prevent duplicate fetch on initial render
            const vesselIds = vesselsRef.current.filter((v) => !skipVessels.includes(v.id)).map((v) => v.id);
            console.log("[fetching-map-data] fetching AIS data for vessels", vesselIds.length);
            if (vesselIds.length === 0) return;

            const startTime = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
            axiosInstance
                .get(`/vesselAis/latest`, {
                    params: {
                        vesselIds: vesselIds.join(","),
                        startTimestampISO: startTime,
                    },
                    meta: { showSnackbar: false },
                })
                .then((res) => {
                    // console.log("AIS Data", res.data);
                    setAisData((prev) => ({
                        ...prev,
                        ...Object.keys(res.data).reduce((acc, vesselId) => {
                            if (vesselsRef.current.find((v) => v.id === vesselId)) {
                                acc[vesselId] = res.data[vesselId];
                            }
                            return acc;
                        }, {}),
                    }));
                })
                .catch((err) => {
                    console.error("Error fetching AIS data:", err);
                });
        } catch (err) {
            console.error("Error in fetching AIS latest data", err);
        }
    };

    const fetchAudioData = ({ skipVessels = [] } = {}) => {
        const vesselIds = vesselsRef.current.filter((v) => !skipVessels.includes(v.id)).map((v) => v.id);
        console.log("[fetching-map-data] fetching audio data for vessels", vesselIds.length);
        if (vesselIds.length === 0) return;
        // Promise.allSettled(
        //     vesselsRef.current
        //         .filter((v) => !skipVessels.includes(v.id) && !audioDataCurrentlyLoading.current.has(v.id))
        //         .map(
        //             (vessel) =>
        //                 new Promise((resolve, reject) => {
        // setLoadingVessels((v) => {
        //     loadingVesselsRef.current = v.concat(vessel.id);
        //     return loadingVesselsRef.current;
        // });
        const payload = {
            vesselIds: vesselIds.join(","),
            startTimestamp: timeSlider[0],
            endTimestamp: journeyEnd === "now" ? dayjs().valueOf() : timeSlider[1],
        };
        // audioDataCurrentlyLoading.current.set(vessel.id, {
        //     vessel_id: vessel.vessel_id,
        //     payload,
        // });
        axiosInstance
            .get(`/audios/bulk`, { params: payload }, { meta: { showSnackbar: false } })
            .then((res) => {
                // Set audio data only if vessel is still in the ref list
                // if (vesselsRef.current.find((v) => v.id === vessel.id)) {
                //     setAudioData((prev) => ({ ...prev, [vessel.id]: res.data }));
                //     console.log("AudioData: set audio data for each vessel");
                // }
                setAudioData((prev) => ({
                    ...prev,
                    ...Object.keys(res.data).reduce((acc, vesselId) => {
                        if (vesselsRef.current.find((v) => v.id === vesselId)) {
                            acc[vesselId] = res.data[vesselId];
                        }
                        return acc;
                    }, {}),
                }));
                // resolve();
            })
            .catch((err) => {
                console.error("Error fetching audio data:", err);
                // Track error vessels here
                // setErrorsomeVessels((prev) => {
                //     if (prev.some((v) => v.id === vessel.id)) return prev;
                //     return [...prev, vessel];
            });
        // reject(err);
        // })
        //                         .finally(() => {
        //                             audioDataCurrentlyLoading.current.delete(vessel.id);
        //                             setLoadingVessels((v) => {
        //                                 loadingVesselsRef.current = v.filter((id) => id !== vessel.id);
        //                                 return loadingVesselsRef.current;
        //                             });
        //                         });
        //                 }),
        //         ),
        // )
        //     .catch((err) => {
        //         console.error("Unexpected error in fetchAudioData:", err);
        //     })
        //     .finally(() => setLoadingAudioData(false));
    };

    const onLoad = (mapInstance) => {
        setMap(mapInstance);
    };

    const onUnmount = () => {
        setMap(null);
        prevFilteredCoordinates.current = {};
    };

    useEffect(() => {
        if (!pathname.includes("/map") && currentClusterInfoWindow) {
            currentClusterInfoWindow.close();
        }
        if (!pathname.includes("/map") && artifactInfowWindow) {
            artifactInfowWindow.close();
        }
        if (!pathname.includes("/map") && audioInfoWindow) {
            audioInfoWindow.close();
        }
        if (!pathname.includes("/map") && currentAudioClusterInfoWindow) {
            currentAudioClusterInfoWindow.close();
        }
        if (!pathname.includes("/map") && streamInfoWindow) {
            streamInfoWindowClose();
        }
    }, [pathname]);

    // Handle target artifact navigation when user clicks on the location coordinates in events modal
    useEffect(() => {
        if (targetArtifactId && map) {
            const handleTargetArtifact = async () => {
                let targetArtifact = null;
                let targetVesselId = null;
                // Check from filtered artifacts first filteredArtifacts
                for (const [vesselId, artifactsList] of Object.entries(filteredArtifacts)) {
                    const artifact = artifactsList.find((a) => a._id === targetArtifactId);
                    if (artifact) {
                        targetArtifact = artifact;
                        targetVesselId = vesselId;
                        break;
                    }
                }
                // If not found in filtered artifacts, try raw artifacts
                if (!targetArtifact) {
                    for (const [vesselId, artifactsList] of Object.entries(artifacts)) {
                        const artifact = artifactsList.find((a) => a._id === targetArtifactId);
                        if (artifact) {
                            targetArtifact = artifact;
                            targetVesselId = vesselId;
                            break;
                        }
                    }
                }
                // If still not found, fetch from database
                if (!targetArtifact) {
                    targetArtifact = await fetchAndAddArtifact(targetArtifactId);
                    if (targetArtifact) {
                        targetVesselId = targetArtifact.onboard_vessel_id;
                    }
                }
                if (targetArtifact && targetArtifact.location?.coordinates) {
                    const [lng, lat] = targetArtifact.location.coordinates;
                    const position = new google.maps.LatLng(lat, lng);
                    // Pan to the location and zoom in
                    map.panTo(position);
                    map.setZoom(Math.max(map.getZoom(), 15));

                    // This is for open the artifact info window after navigating from events page modal
                    setTimeout(() => {
                        const markers = artifactsMarkers.current[targetVesselId];
                        if (markers) {
                            const marker = markers.find((m) => m.artifactId === targetArtifactId);
                            if (marker) {
                                // console.log('Navigation: Found marker for artifact, triggering click');
                                // Trigger the marker click event to open info window
                                // console.log("Navigation: Triggering click event on marker for artifact", targetArtifactId, marker);
                                google.maps.event.trigger(marker, "click");
                            }
                        }
                    }, 2000); // Increased delay to ensure map has panned and markers are ready

                    // Clear the target artifact ID and URL parameter
                    setTargetArtifactId(null);
                    if (clearUrlParameter) {
                        clearUrlParameter();
                    }
                } else {
                    setTargetArtifactId(null);
                    if (clearUrlParameter) {
                        clearUrlParameter();
                    }
                }
            };

            handleTargetArtifact();
        }
    }, [targetArtifactId, map, google, filteredArtifacts, artifacts, clearUrlParameter]);

    const streamInfoWindowClose = useCallback(() => {
        if (streamInfoWindow) {
            if (scrubberMarker.current) {
                scrubberMarker.current.setMap(null);
            }
            isPlayingVideo.current = false;
            setStreamInfoWindowContent(null);
            streamInfoWindow.close();
        }
    }, [streamInfoWindow]);

    // This is for showing the EEZ layers on the map
    useEffect(() => {
        if (!map || !google) return;

        // If EEZ layers are disabled, clean up and return
        if (!showEEZLayers) {
            if (eezLayerRef.current) {
                eezLayerRef.current.setMap(null);
                eezLayerRef.current = null;
            }
            // Set EEZ loading to false when disabling layers
            if (setLoadingEEZLayers) {
                setLoadingEEZLayers(false);
            }
            return;
        }

        // If layer already exists and is attached to the map, do nothing
        if (eezLayerRef.current && eezLayerRef.current.getMap() === map) {
            // Ensure EEZ loading is false if layer is already loaded
            if (setLoadingEEZLayers) {
                setLoadingEEZLayers(false);
            }
            return;
        }

        const loadEezLayers = async () => {
            try {
                // Set loading state to true when starting to load
                if (setLoadingEEZLayers) {
                    setLoadingEEZLayers(true);
                }
                
                // Use cached GeoJSON if available, otherwise fetch
                let geoJsonData = eezGeoJsonCacheRef.current;

                if (!geoJsonData) {
                    console.log('Loading EEZ GeoJSON data...');
                    const response = await fetch('/optimized.geojson');
                    geoJsonData = await response.json();
                    // Cache the GeoJSON data to avoid re-fetching
                    eezGeoJsonCacheRef.current = geoJsonData;
                }

                // Reuse existing layer if available, otherwise create new one
                let eezDataLayer = eezLayerRef.current;

                if (!eezDataLayer) {
                    // Create a new Data layer for EEZ
                    eezDataLayer = new google.maps.Data();

                    // Add all GeoJSON features
                    eezDataLayer.addGeoJson(geoJsonData);

                    // Set style with optimized rendering
                    eezDataLayer.setStyle((feature) => {
                        return {
                            fillColor: '#4A90E2', // Blue color for EEZ areas
                            fillOpacity: 0.3, // Semi-transparent
                            strokeColor: '#2E5C8A', // Darker blue border
                            strokeWeight: 2,
                            strokeOpacity: 0.8,
                            zIndex: 1, // Behind other map elements
                            optimized: true, // Enable optimization for better performance
                        };
                    });
                    eezLayerRef.current = eezDataLayer;
                }

                // Attach layer to map (or reattach if it was detached)
                eezDataLayer.setMap(map);

                console.log('EEZ layers loaded successfully');
            } catch (error) {
                console.error('Error loading EEZ layers:', error);
            } finally {
                if (setLoadingEEZLayers) {
                    setLoadingEEZLayers(false);
                }
            }
        };

        loadEezLayers();
    }, [map, google, showEEZLayers, setLoadingEEZLayers]);

    useEffect(() => {
        if (!map || !google) return;

        const cleanup = drawPortsData({
            map,
            google,
            showPortsLayers,
            infoWindow,
            portsGeoJsonCacheRef,
            portsMarkersRef,
            portsClustererRef,
            portsUpdateTimeoutRef,
            user,
        });

        if (!cleanup) return;

        return cleanup;
    }, [map, google, showPortsLayers, infoWindow, user]);

    const onZoom = useCallback(() => {
        if (map) {
            zoom.current = map.getZoom();
        }
    }, [map]);

    return (
        <div style={{ color: "#FFFFFF", width: "100%", height: "100%" }}>
            {loadingCoordinates ? <Skeleton animation="wave" variant="rectangular" height={"100%"} /> : <></>}
            <div style={{ display: loadingCoordinates ? "none" : "flex", width: "100%", height: "100%" }}>
                <GoogleMap
                    mapContainerStyle={{
                        width: "100%",
                        height: "100%",
                    }}
                    center={center}
                    zoom={initialZoom}
                    onZoomChanged={onZoom}
                    onLoad={onLoad}
                    onUnmount={onUnmount}
                    options={{
                        // gestureHandling: "greedy",
                        mapId: "c94897bab52d6290",
                        zoomControl: true,
                        streetViewControl: false,
                        ...(devMode ? {
                            colorScheme: "DARK",
                        } : {
                            mapTypeControl: false,
                            fullscreenControl: false,
                        }),
                        // styles: [
                        //     {
                        //         featureType: "administrative.country",
                        //         elementType: "labels",
                        //         stylers: [{ visibility: "on" }] // keep country names
                        //     },
                        //     {
                        //         featureType: "administrative.province",
                        //         elementType: "labels",
                        //         stylers: [{ visibility: "on" }] // keep state/province names
                        //     },
                        //     {
                        //         featureType: "administrative.locality",
                        //         elementType: "labels",
                        //         stylers: [{ visibility: "off" }] // hide city names
                        //     },
                        //     {
                        //         featureType: "landscape",
                        //         stylers: [{ visibility: "off" }],
                        //     },
                        //     {
                        //         featureType: "poi",
                        //         stylers: [{ visibility: "off" }],
                        //     },
                        //     {
                        //         featureType: "road",
                        //         stylers: [{ visibility: "off" }],
                        //     },
                        //     {
                        //         featureType: "transit",
                        //         stylers: [{ visibility: "off" }],
                        //     },
                        //     {
                        //         featureType: "water",
                        //         stylers: [{ visibility: "simplified" }],
                        //     },
                        // ],
                    }}
                />
            </div>
            {streamInfoWindowContent && createPortal(
                <StreamInfoWindow
                    vessel={
                        vesselInfo.find((v) =>
                            v.vessel_id === streamInfoWindow.get("vesselId")
                        )
                    }
                    streamMode={streamInfoWindow.get("streamMode")}
                    startTimestamp={streamInfoWindow.get("startTimestamp")}
                    onClose={streamInfoWindowClose} />,
                streamInfoWindowContent,
            )}
        </div>
    );
};

export default MainMap;
