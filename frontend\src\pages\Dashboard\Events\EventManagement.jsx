import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, Badge, Ty<PERSON>graphy, CircularProgress, TextField, MenuItem } from "@mui/material";
import { ExpandMore, ExpandLess } from "@mui/icons-material";
import { useEffect, useMemo, useState, memo, useRef, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { useApp } from "../../../hooks/AppHook";
import { useUser } from "../../../hooks/UserHook";
import Events from "./Events";
import theme from "../../../theme";
import axiosInstance from "../../../axios.js";
import ListManagement from "./Lists/ListManagement.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
import { isEnvironment, permissions } from "../../../utils.js";
import ArchivedArtifacts from "./ArchivedArtifacts";
import EvaluatedArtifacts from "./EvaluatedArtifacts";
import FlaggedAIS from "./FlaggedAIS";
import ArtifactAutoSuggest from "./ArtifactAutoSuggest";
import environment from "../../../../environment.js";
import SetSaveListModal from "./Lists/SetSaveListModal";
import listsController from "../../../controllers/Lists.controller";
import { Star } from "@mui/icons-material";
import { getSocket } from "../../../socket";
import FlaggedArtifacts from "./FlaggedArtifacts";

const EventsManagement = () => {
    const location = useLocation();
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const [tab, setTab] = useState("");
    const [filters, setFilters] = useState({ host_vessel: false });
    const [hasVisitedEvents, setHasVisitedEvents] = useState(true);
    const [searchQuery, setSearchQuery] = useState("");

    const [showFilterModal, setShowFilterModal] = useState(false);
    const [vessels, setVessels] = useState([]);
    const [showingFor, setShowingFor] = useState(null);
    const [originalInput, setOriginalInput] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [showSetSaveListModal, setShowSetSaveListModal] = useState(false);
    const [lists, setLists] = useState([]);
    const [isLoadingLists, setIsLoadingLists] = useState(false);
    const [tabChildrenViews, setTabChildrenViews] = useState({}); // Track selected child for each tab with children
    const [showEvaluatedFilterModal, setShowEvaluatedFilterModal] = useState(false);

    const { isMobile, devMode } = useApp();
    const onCompletion = useRef(null);
    const abortControllerRef = useRef();

    useEffect(() => {
        const isEventPath = location.pathname === "/dashboard/events" || location.pathname.startsWith("/dashboard/events");

        if (isEventPath) {
            setHasVisitedEvents(true);
        }
    }, [location]);

    const tabs = useMemo(
        () => [
            {
                value: "events",
                label: "Events",
                component: vessels.length === 0 ? (
                    <Grid
                        container
                        display={"flex"}
                        justifyContent={"center"}
                        alignItems={"center"}
                        height={{ xs: "90%", sm: "90%" }}
                        overflow={"auto"}
                        marginBottom={2}
                        size="grow"
                    >
                        <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
                    </Grid>
                ) : (
                    <Events
                        searchQuery={searchQuery}
                        showFilterModal={showFilterModal}
                        setShowFilterModal={setShowFilterModal}
                        filters={filters}
                        setFilters={setFilters}
                        vessels={vessels}
                        tab={tab}
                        onCompletion={onCompletion}
                        onLoadingChange={setIsLoading}
                    />
                ),
                display: true,
            },
            {
                value: "lists",
                label: "Lists",
                component: vessels.length === 0 ? (
                    <Grid
                        container
                        display={"flex"}
                        justifyContent={"center"}
                        alignItems={"center"}
                        height={{ xs: "90%", sm: "90%" }}
                        overflow={"auto"}
                        marginBottom={2}
                        size="grow"
                    >
                        <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
                    </Grid>
                ) : <ListManagement vessels={vessels} />,
                display: true,
            },
            {
                value: "archived",
                label: "Archived",
                component: <ArchivedArtifacts vessels={vessels} />,
                display: user?.hasPermissions([permissions.manageArtifacts]),
            },
            {
                value: "flagged",
                label: "Flagged",
                children: {
                    artifacts: {
                        value: "flagged-artifacts",
                        label: "Artifacts - Flagged",
                        component: <FlaggedArtifacts />,
                    },
                    evaluated: {
                        value: "evaluated-artifacts",
                        label: "Artifacts - Evaluated",
                        component: <EvaluatedArtifacts showFilterModal={showEvaluatedFilterModal} setShowFilterModal={setShowEvaluatedFilterModal} />,
                    },
                    ais: {
                        value: "flagged-ais",
                        label: "AIS - Flagged",
                        component: <FlaggedAIS />,
                    }
                },
                display: user?.hasPermissions([permissions.manageArtifacts]),
            },
        ],
        [user, showFilterModal, filters, vessels, tab, searchQuery, showEvaluatedFilterModal],
    );

    // Generic function to get component for a specific tab
    const getTabComponent = useCallback(
        (tabValue) => {
            const targetTab = tabs.find((t) => t.value === tabValue);
            if (!targetTab) return null;

            // If tab has children, get the selected child component
            if (targetTab.children) {
                const selectedChildValue = tabChildrenViews[tabValue];
                if (selectedChildValue) {
                    const child = Object.values(targetTab.children).find((c) => c.value === selectedChildValue);
                    if (child?.component) {
                        return child.component;
                    }
                }
                // If no selection, return first child component
                const firstChild = Object.values(targetTab.children)[0];
                if (firstChild?.component) {
                    return firstChild.component;
                }
                return null;
            }

            // If no children, return the tab's component directly
            return targetTab.component || null;
        },
        [tabs, tabChildrenViews],
    );

    const filterVessels = (vessels) => {
        if (!vessels || !Array.isArray(vessels)) return [];

        return vessels.filter((vessel) => {
            if (vessel.is_active === false && !devMode) return false;
            return true;
        });
    };

    const fetchVessels = async () => {
        try {
            if (vesselInfo) {
                const filteredVessels = filterVessels(vesselInfo);
                setVessels(filteredVessels);
            } else {
                fetchVesselsInfo();
            }
        } catch (err) {
            console.error("An error occurred while fetching vessels on the events Page:", err);
        }
    };

    useEffect(() => {
        fetchVessels();
    }, [vesselInfo]);

    const fetchLists = async () => {
        setIsLoadingLists(true);
        try {
            const data = await listsController.fetchLists();
            const safe = Array.isArray(data) ? data : [];
            setLists(safe);
        } catch (err) {
            console.error("Error fetching lists", err);
            setLists([]);
        } finally {
            setIsLoadingLists(false);
        }
    };

    useEffect(() => {
        if (tab === "events" || tab === "lists") {
            fetchLists();
            const socket = getSocket();
            const handleListChanged = () => {
                fetchLists();
            };
            socket.on("list/changed", handleListChanged);
            return () => {
                socket.off("list/changed", handleListChanged);
            };
        }
    }, [tab]);

    useEffect(() => {
        const handleShowManageLists = () => {
            setTab("lists");
        };

        window.addEventListener("showManageLists", handleShowManageLists);
        return () => {
            window.removeEventListener("showManageLists", handleShowManageLists);
        };
    }, []);

    useEffect(() => {
        if (!tab) {
            setTab(tabs.find((t) => t.display)?.value || "");
        }
    }, [tabs]);

    // Initialize or reset tab children views when switching tabs
    useEffect(() => {
        const currentTab = tabs.find((t) => t.value === tab);
        if (currentTab?.children) {
            // Initialize with first child if not set for this tab
            if (!tabChildrenViews[tab]) {
                const firstChild = Object.values(currentTab.children)[0];
                if (firstChild) {
                    setTabChildrenViews((prev) => ({
                        ...prev,
                        [tab]: firstChild.value,
                    }));
                }
            }
        }
    }, [tab, tabs]);

    const handleAutoSuggestSelect = async (suggestion, input) => {
        // If Enter is pressed on empty string, clear filters
        if (suggestion === "") {
            setFilters({ host_vessel: false });
            setOriginalInput("");
            setShowingFor(null);
            return;
        }
        if (input && suggestion && suggestion !== input) {
            setOriginalInput(input);
            setShowingFor(suggestion);
        } else {
            setOriginalInput("");
            setShowingFor(null);
        }
        try {
            onCompletion.current?.(true);
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
            abortControllerRef.current = new AbortController();
            const res = await axiosInstance.post(
                "/completions",
                { text: suggestion },
                { signal: abortControllerRef.current.signal, meta: { showSnackbar: false } },
            );
            abortControllerRef.current = null;
            if (res.data && typeof res.data === "object") {
                setFilters(res.data);
            }
        } catch (err) {
            onCompletion.current?.(false);
            console.error("Error fetching completions for suggestion", err);
        }
    };

    const handleTabChange = (event, newValue) => {
        setTab(newValue);
    };

    if (!hasVisitedEvents) {
        return null;
    }

    const activeFilterCount = Object.entries(filters).filter(([key, v]) => {
        if (!v || (Array.isArray(v) && v.length === 0) || (!Array.isArray(v) && v === "")) return false;
        if (key === "type" && v === "both") return false;
        if (key === "end_time" && v) return false;
        return true;
    }).length;

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                color={"#FFFFFF"}
                flexDirection={"column"}
                width={"100%"}
                height={"100%"}
                overflow={"auto"}
                sx={{ backgroundColor: theme.palette.custom.darkBlue }}
            >
                <Grid
                    container
                    padding={2}
                    display={"flex"}
                    columnGap={{ xs: 2, lg: 0 }}
                    rowGap={2}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    flexWrap={"wrap"}
                >
                    <Grid
                        size={{
                            xs: "grow",
                            lg: 4.5,
                        }}
                    >
                        <Grid
                            container
                            gap={0.5}
                            width="100%"
                            sx={{
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t) => {
                                    const isSelected = tab === t.value;
                                    const tabWidth = 100 / tabs.filter((tabItem) => tabItem.display).length + "%";

                                    // If tab has children, render as dropdown
                                    if (t.children) {
                                        const selectedChildValue = tabChildrenViews[t.value] || Object.values(t.children)[0]?.value;

                                        return (
                                            <Grid 
                                                key={t.value} 
                                                size="grow" 
                                                sx={{ 
                                                    flex: `0 0 calc(${tabWidth} - 4px)`,
                                                    minWidth: 0,
                                                    maxWidth: `calc(${tabWidth} - 4px)`,
                                                    overflow: "hidden",
                                                }}
                                            >
                                                <TextField
                                                    value={selectedChildValue || ""}
                                                    onChange={(e) => {
                                                        const newValue = e.target.value;
                                                        // Only update if value actually changed
                                                        if (newValue !== selectedChildValue) {
                                                            setTab(t.value);
                                                            setTabChildrenViews((prev) => ({
                                                                ...prev,
                                                                [t.value]: newValue,
                                                            }));
                                                        }
                                                    }}
                                                    onMouseDown={(e) => {
                                                        // Switch to this tab when clicking on the dropdown (before it opens)
                                                        if (tab !== t.value) {
                                                            // Use setTimeout to ensure tab switch happens before dropdown opens
                                                            setTab(t.value);
                                                        }
                                                    }}
                                                    select
                                                    SelectProps={{
                                                        onOpen: () => {
                                                            // Always ensure tab is switched when dropdown opens
                                                            if (tab !== t.value) {
                                                                setTab(t.value);
                                                            }
                                                        },
                                                        renderValue: (value) => {
                                                            const selectedChild = Object.values(t.children).find((c) => c.value === value);
                                                            return (
                                                                <Typography
                                                                    sx={{
                                                                        overflow: "hidden",
                                                                        textOverflow: "ellipsis",
                                                                        whiteSpace: "nowrap",
                                                                        maxWidth: "100%",
                                                                    }}
                                                                >
                                                                    {selectedChild?.label || ""}
                                                                </Typography>
                                                            );
                                                        },
                                                        MenuProps: {
                                                            PaperProps: {
                                                                sx: {
                                                                    backgroundColor: theme.palette.custom.darkBlue,
                                                                    border: `1px solid ${theme.palette.custom.borderColor}`,
                                                                    "& .MuiMenuItem-root": {
                                                                        display: "flex",
                                                                        alignItems: "center",
                                                                        color: "#FFFFFF",
                                                                        "&.Mui-selected": {
                                                                            backgroundColor: theme.palette.custom.mainBlue + " !important",
                                                                        },
                                                                        "&:hover": {
                                                                            backgroundColor: "rgba(0, 102, 255, 0.2)",
                                                                        },
                                                                    },
                                                                    "& .MuiList-root": {
                                                                        padding: 0,
                                                                    },
                                                                },
                                                            },
                                                        },
                                                        IconComponent: (props) => (
                                                            <ExpandMore {...props} sx={{ right: "8px !important", color: "#FFFFFF", flexShrink: 0, position: "absolute" }} />
                                                        ),
                                                    }}
                                                    sx={{
                                                        width: "100%",
                                                        height: "100%",
                                                        minWidth: 0,
                                                        maxWidth: "100%",
                                                        "& .MuiInputBase-root": {
                                                            height: "100%",
                                                            minWidth: 0,
                                                            maxWidth: "100%",
                                                            backgroundColor: isSelected ? theme.palette.custom.mainBlue : "transparent",
                                                            borderRadius: "8px",
                                                            border: "none",
                                                            padding: 0,
                                                            paddingLeft: "12px",
                                                            color: "#FFFFFF",
                                                            fontWeight: "bold",
                                                            transition: "all 0.2s ease-in-out",
                                                            position: "relative",
                                                            "&::before, &::after": {
                                                                display: "none",
                                                            },
                                                        },
                                                        "& .MuiSelect-select": {
                                                            padding: 0,
                                                            display: "flex",
                                                            alignItems: "center",
                                                            height: "100%",
                                                            minWidth: 0,
                                                            maxWidth: "calc(100% - 24px)",
                                                            overflow: "hidden",
                                                            textOverflow: "ellipsis",
                                                            whiteSpace: "nowrap",
                                                        },
                                                        "& .MuiOutlinedInput-notchedOutline": {
                                                            display: "none",
                                                        }
                                                    }}
                                                >
                                                    {Object.values(t.children).map((child) => (
                                                        <MenuItem key={child.value} value={child.value}>
                                                            {child.label}
                                                        </MenuItem>
                                                    ))}
                                                </TextField>
                                            </Grid>
                                        );
                                    }

                                    // Regular tab without children
                                    return (
                                        <Grid key={t.value} size="grow" sx={{ flex: `0 0 calc(${tabWidth} - 4px)` }}>
                                            <Tab
                                                label={t.label}
                                                value={t.value}
                                                onClick={() => handleTabChange(null, t.value)}
                                                sx={{
                                                    maxWidth: "none",
                                                    width: "100%",
                                                    height: "100%",
                                                    backgroundColor: isSelected ? theme.palette.custom.mainBlue : "transparent",
                                                    borderRadius: "8px",
                                                    border: "none",
                                                    color: "#FFFFFF",
                                                    fontWeight: "bold",
                                                    "&.Mui-selected": {
                                                        backgroundColor: theme.palette.custom.mainBlue,
                                                        color: "#FFFFFF",
                                                    },
                                                    "&.MuiButtonBase-root": {
                                                        opacity: 1,
                                                    },
                                                }}
                                            />
                                        </Grid>
                                    );
                                })}
                        </Grid>
                    </Grid>
                    {tab === "events" && (
                        <Grid
                            container
                            columnGap={2}
                            justifyContent={"space-between"}
                            size={{
                                xs: 12,
                                lg: 7.4,
                            }}
                        >
                            <Grid
                                size={{
                                    xs: "grow",
                                    lg: 5.8,
                                }}
                            >
                                {!isEnvironment(environment.stagingAndProduction) && (
                                    <ArtifactAutoSuggest
                                        setSearchQuery={setSearchQuery}
                                        // onSelect={handleAutoSuggestSelect}
                                        isLoading={isLoading}
                                    />
                                )}
                            </Grid>
                            <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size="auto">
                                <Button
                                    variant="outlined"
                                    startIcon={<Star fontSize="small" sx={{ fontSize: "16px", color: "#FFFFFF" }} />}
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            borderColor: theme.palette.custom.borderColor,
                                            height: { xs: isEnvironment(environment.stagingAndProduction) ? "50px" : "100%", lg: "auto" },
                                            color: "#FFFFFF",
                                            padding: { xs: "0", lg: "10px 20px" },
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    onClick={() => setShowSetSaveListModal(true)}
                                >
                                    {!isMobile && "Set save list"}
                                </Button>
                                <Badge
                                    badgeContent={activeFilterCount}
                                    color="primary"
                                    invisible={activeFilterCount === 0}
                                    sx={{
                                        height: isMobile ? "100%" : "auto",
                                        "& .MuiBadge-badge": {
                                            backgroundColor: theme.palette.custom.mainBlue,
                                            color: "#FFFFFF",
                                            borderRadius: "50%",
                                            height: 20,
                                            width: 20,
                                            border: "2px solid #FFFFFF",
                                            top: 3,
                                            right: 3,
                                        },
                                    }}
                                >
                                    <Button
                                        className="events-step-1"
                                        variant="outlined"
                                        startIcon={<img src={"/icons/filter_icon.svg"} width={20} height={20} alt={"Filter"} />}
                                        sx={{
                                            "&.MuiButtonBase-root": {
                                                borderColor:
                                                    activeFilterCount === 0 ? theme.palette.custom.borderColor : theme.palette.custom.mainBlue,
                                                height: { xs: isEnvironment(environment.stagingAndProduction) ? "50px" : "100%", lg: "auto" },
                                                color: "#FFFFFF",
                                                padding: { xs: "0", lg: "10px 20px" },
                                                fontWeight: "bold",
                                            },
                                            "& .MuiButton-icon": {
                                                marginRight: { xs: 0, lg: "10px" },
                                            },
                                        }}
                                        onClick={() => setShowFilterModal(true)}
                                        disabled={isLoading}
                                    >
                                        {!isMobile && "Filter"}
                                    </Button>
                                </Badge>
                            </Grid>
                        </Grid>
                    )}
                    {tabChildrenViews[tab] === "evaluated-artifacts" && (
                        <Button
                            className="events-step-1"
                            variant="outlined"
                            startIcon={<img src={"/icons/filter_icon.svg"} width={20} height={20} alt={"Filter"} />}
                            sx={{
                                "&.MuiButtonBase-root": {
                                    borderColor: theme.palette.custom.borderColor,
                                    height: { xs: isEnvironment(environment.stagingAndProduction) ? "50px" : "100%", lg: "auto" },
                                    color: "#FFFFFF",
                                    padding: { xs: "0", lg: "10px 20px" },
                                    fontWeight: "bold",
                                },
                                "& .MuiButton-icon": {
                                    marginRight: { xs: 0, lg: "10px" },
                                },
                            }}
                            onClick={() => setShowEvaluatedFilterModal(true)}
                            disabled={isLoading}
                        >
                            {!isMobile && "Filter"}
                        </Button>
                    )}
                    {tab === "lists" && (
                        <Grid
                            container
                            columnGap={2}
                            justifyContent={"flex-end"}
                            size={{
                                xs: 12,
                                lg: 7.4,
                            }}
                        >
                            <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size={{ xs: 12 }}>
                                <Button
                                    variant="outlined"
                                    startIcon={<Star fontSize="small" sx={{ fontSize: "16px", color: "#FFFFFF" }} />}
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            borderColor: theme.palette.custom.borderColor,
                                            height: { xs: "50px", lg: "auto" },
                                            color: "#FFFFFF",
                                            padding: { xs: "0", lg: "10px 20px" },
                                            fontWeight: "bold",
                                            width: { xs: "100%", lg: "auto" },
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: "10px" },
                                        },
                                    }}
                                    onClick={() => setShowSetSaveListModal(true)}
                                >
                                    Set save list
                                </Button>
                            </Grid>
                        </Grid>
                    )}
                </Grid>
                {tab === "events" && showingFor && originalInput && (
                    <Grid item xs={12} padding={2} pt={0}>
                        <Typography sx={{ fontWeight: "bold", fontSize: 16, pl: 1 }}>
                            <span style={{ color: theme.palette.custom.mainBlue }}>Showing results for</span> &quot;{showingFor}&quot;{" "}
                            <span style={{ textDecoration: "line-through", color: "#888", marginLeft: 8 }}>{originalInput}</span>
                        </Typography>
                    </Grid>
                )}
                {tabs
                    .filter((t) => t.display)
                    .map((t) => {
                        const component = getTabComponent(t.value);
                        return (
                            <Grid key={t.value} display={tab !== t.value && "none"} paddingX={2} paddingBottom={2} width={"100%"} size="grow">
                                {component}
                            </Grid>
                        );
                    })}
                <SetSaveListModal open={showSetSaveListModal} onClose={() => setShowSetSaveListModal(false)} lists={lists} isLoadingLists={isLoadingLists} />
            </Grid>
        )
    );
};

export default memo(EventsManagement);
