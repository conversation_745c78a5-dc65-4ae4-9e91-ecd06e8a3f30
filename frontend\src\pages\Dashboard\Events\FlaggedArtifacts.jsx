import { useEffect, useState, useRef, useCallback, memo } from "react";
import { Grid, CircularProgress } from "@mui/material";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import VirtualizedCardList from "./VirtualizedCardList";
import FlaggedCard from "./FlaggedCard";
import { getSocket } from "../../../socket";
import DetailModal from "./DetailModal";
import theme from "../../../theme";

const FlaggedArtifacts = () => {
    const [flaggedArtifacts, setFlaggedArtifacts] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedArtifact, setSelectedArtifact] = useState(null);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const containerRef = useRef();
    const listRef = useRef();
    const hasMore = useRef(true);
    const pageRef = useRef(1);
    const loadMoreTimeoutRef = useRef();
    const isLoadingMoreRef = useRef(false);
    const totalLoadedRef = useRef(0);
    const [dataVersion, setDataVersion] = useState(0);

    const fetchFlaggedArtifacts = useCallback(async (isSocketTriggered = false, isLoadMore = false) => {
        if (isLoadMore && isLoadingMoreRef.current) {
            return;
        }

        if (isLoadMore && !hasMore.current) {
            return;
        }

        setLoading(true);

        if (isLoadMore) {
            isLoadingMoreRef.current = true;
        }

        try {
            const currentPage = isLoadMore ? pageRef.current + 1 : 1;
            const pageSize = 30;
            const response = await artifactFlagController.getFlaggedArtifacts(currentPage, pageSize);
            const { artifacts, totalCount, page: responsePage, pageSize: responsePageSize } = response;

            if (artifacts.length === 0) {
                if (isLoadMore) {
                    hasMore.current = false;
                } else {
                    setFlaggedArtifacts([]);
                    pageRef.current = 1;
                    totalLoadedRef.current = 0;
                    hasMore.current = false;
                }
                setLoading(false);
                isLoadingMoreRef.current = false;
                return;
            }
            if (isLoadMore) {
                setFlaggedArtifacts((prev) => {
                    const newList = [...prev, ...artifacts];
                    totalLoadedRef.current = newList.length;
                    return newList;
                });
                pageRef.current = responsePage || currentPage;
            } else {
                setFlaggedArtifacts([...artifacts]);
                pageRef.current = responsePage || 1;
                totalLoadedRef.current = artifacts.length;
                setDataVersion((prev) => prev + 1);

                if (isSocketTriggered) {
                    artifactFlagController.getUserFlaggedArtifactIds().catch(console.error);
                }
            }

            const gotFullPage = artifacts.length >= responsePageSize;
            const currentTotal = isLoadMore ? totalLoadedRef.current : artifacts.length;
            hasMore.current = gotFullPage && currentTotal < totalCount;

            setLoading(false);
            isLoadingMoreRef.current = false;
        } catch (error) {
            console.error("Error fetching flagged artifacts:", error);
            setLoading(false);
            isLoadingMoreRef.current = false;
        }
    }, []);

    const handleLoadMore = useCallback(() => {
        if (loading || !hasMore.current || isLoadingMoreRef.current) {
            return;
        }

        if (loadMoreTimeoutRef.current) {
            clearTimeout(loadMoreTimeoutRef.current);
        }

        loadMoreTimeoutRef.current = setTimeout(() => {
            fetchFlaggedArtifacts(false, true);
        }, 500);
    }, [loading, fetchFlaggedArtifacts]);

    useEffect(() => {
        fetchFlaggedArtifacts();
        const socket = getSocket();

        const handleFlagChanged = () => {
            fetchFlaggedArtifacts(true, false);
        };

        socket.on("artifacts_flagged/changed", handleFlagChanged);
        socket.on("artifact/changed", handleFlagChanged);

        return () => {
            socket.off("artifacts_flagged/changed", handleFlagChanged);
            socket.off("artifact/changed", handleFlagChanged);
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }
        };
    }, [fetchFlaggedArtifacts]);

    const handleFlaggedByClick = useCallback((artifact) => {
        setSelectedArtifact(artifact);
        setShowDetailModal(true);
    }, []);

    return (
        <>
            {loading && flaggedArtifacts.length === 0 ? (
                <Grid
                    container
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                    height={{ xs: "90%", sm: "90%" }}
                    overflow={"auto"}
                    marginBottom={2}
                    size="grow"
                >
                    <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
                </Grid>
            ) : (
                <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
                    <Grid
                        container
                        overflow={"auto"}
                        display={"block"}
                        border={`1px solid ${theme.palette.custom.borderColor}`}
                        borderRadius={"10px"}
                        padding={"10px 24px"}
                        size="grow"
                    >
                        <Grid container height={"100%"} overflow={"hidden"} ref={containerRef}>
                            <VirtualizedCardList
                                key={`flagged-artifacts-${dataVersion}`}
                                ref={listRef}
                                events={flaggedArtifacts}
                                isLoading={loading}
                                onLoadMore={handleLoadMore}
                                hasMore={hasMore.current}
                                containerRef={containerRef}
                                setShowDetailModal={setShowDetailModal}
                                setSelectedCard={setSelectedArtifact}
                                CustomCard={FlaggedCard}
                                onFlaggedByClick={handleFlaggedByClick}
                            />
                        </Grid>
                    </Grid>
                    <DetailModal
                        showDetailModal={showDetailModal}
                        setShowDetailModal={setShowDetailModal}
                        selectedCard={selectedArtifact}
                        setSelectedCard={setSelectedArtifact}
                    />
                </Grid>
            )}
        </>
    );
};

export default memo(FlaggedArtifacts);
