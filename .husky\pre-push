# Try to get the upstream branch
TARGET=$(git rev-parse --abbrev-ref @{u} 2>/dev/null || true)

if [ -n "$TARGET" ]; then
    echo "📡 Comparing against upstream: $TARGET"
    # Check for changes specifically against the upstream
    CHANGES=$(git diff --name-only "$TARGET...HEAD" | grep "^rewrite/backend" || true)

    if [ -z "$CHANGES" ]; then
        echo "✅ No backend changes detected vs $TARGET. Skipping tests."
        exit 0
    fi
else
    # FALLBACK: No upstream branch found
    echo "⚠️  No upstream branch detected (untracked branch). Running backend tests as a safety fallback..."
fi

echo "🔍 Running backend quality checks before push..."

cd rewrite/backend || exit 1

echo "1) Lint (no warnings)..."
npm run -s lint:check || exit 1

echo "2) Prettier format check..."
npm run -s format:check || exit 1

echo "3) Tests with coverage..."
npm test --silent || exit 1

echo "4) Coverage threshold (avg S/B/F/L >= 95%)..."
npm run -s coverage:check || exit 1

echo "🚀 All checks passed. Proceeding with push."
