import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { generateUserToken, authorizedUser, nonAuthorizedUser } from '../data/Auth';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import listService from '../../services/List.service';
import List from '../../models/List';
import ListArtifact from '../../models/ListArtifact';
import Vessel from '../../models/Vessel';
import { qmai } from '../mocks/modules/db.mock';
import { processBatchItem, s3Config } from '../../modules/awsS3';
import { canAccessVessel, validateError } from '../../utils/functions';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { AuthRunTestsFunction } from '../type';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../services/List.service', () => require('../mocks/services/listService.mock'));

jest.mock('../../models/List', () => require('../mocks/models/list.mock'));
jest.mock('../../models/ListArtifact', () => require('../mocks/models/listArtifact.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));

describe('Lists API', () => {
    beforeEach(() => {
        jest.resetAllMocks();
        (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
            res.status(500).json({ message: err.message });
        });
    });

    describe('GET /api/lists', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/lists');
                    expect(res.status).toBe(401);
                });

                it('should return lists for authenticated user or 403 for api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockLists = [{ _id: '507f1f77bcf86cd799439020', name: 'Test List' }];
                    (listService.getVisibleListsForUser as jest.Mock).mockResolvedValue(mockLists);
                    const res = await request(app).get('/api/lists').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockLists);
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.getVisibleListsForUser as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).get('/api/lists').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/lists/user/artifacts', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/lists/user/artifacts');
                    expect(res.status).toBe(401);
                });

                it('should return artifact IDs for authenticated user or 403 for api-key', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifacts = { '507f1f77bcf86cd799439020': ['507f1f77bcf86cd799439030'] };
                    (listService.getUserListsArtifacts as jest.Mock).mockResolvedValue(mockArtifacts);
                    const res = await request(app).get('/api/lists/user/artifacts').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockArtifacts);
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.getUserListsArtifacts as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).get('/api/lists/user/artifacts').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/lists', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/lists').send({ name: 'Test List' });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if name is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/lists').set('Authorization', authToken).send({});
                    expect(res.status).toBe(400);
                });

                it('should return 400 if name is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/lists').set('Authorization', authToken).send({ name: '   ' });
                    expect(res.status).toBe(400);
                });

                it('should create list with name only', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockList = { _id: '507f1f77bcf86cd799439020', name: 'Test List', owner_id: authorizedUser._id };
                    (listService.createList as jest.Mock).mockResolvedValue(mockList);
                    const res = await request(app).post('/api/lists').set('Authorization', authToken).send({ name: 'Test List' });
                    expect(res.status).toBe(201);
                    expect(res.body).toEqual(mockList);
                    expect(listService.createList).toHaveBeenCalledWith({ name: 'Test List', sharedWithOrganization: undefined, user: expect.any(Object) });
                });

                it('should create list with sharedWithOrganization true', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockList = { _id: '507f1f77bcf86cd799439020', name: 'Test List', shared_with_organization: true };
                    (listService.createList as jest.Mock).mockResolvedValue(mockList);
                    const res = await request(app).post('/api/lists').set('Authorization', authToken).send({ name: 'Test List', sharedWithOrganization: true });
                    expect(res.status).toBe(201);
                    expect(res.body).toEqual(mockList);
                    expect(listService.createList).toHaveBeenCalledWith({ name: 'Test List', sharedWithOrganization: true, user: expect.any(Object) });
                });

                it('should return 409 if list name is duplicate', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const error: any = new Error('Duplicate key');
                    error.code = 11000;
                    (listService.createList as jest.Mock).mockRejectedValue(error);
                    const res = await request(app).post('/api/lists').set('Authorization', authToken).send({ name: 'Test List' });
                    expect(res.status).toBe(409);
                    expect(res.body.message).toBe('Name is already taken');
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.createList as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).post('/api/lists').set('Authorization', authToken).send({ name: 'Test List' });
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/lists/:listId', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/lists/507f1f77bcf86cd799439020');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get('/api/lists/invalid-id').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if list not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (List.findById as jest.Mock).mockResolvedValue(null);
                    const res = await request(app).get(`/api/lists/${listId}`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('List not found');
                });

                it('should return 404 if list is deleted', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockList = { _id: listId, is_deleted: true, toObject: jest.fn().mockReturnValue({}) };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    const res = await request(app).get(`/api/lists/${listId}`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('List not found');
                });

                it('should return 403 if user cannot read list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockList = { _id: listId, is_deleted: false, toObject: jest.fn().mockReturnValue({}) };
                    (List.findById as jest.Mock).mockResolvedValue(mockList as never);
                    (listService.canReadList as jest.Mock).mockResolvedValue(false as never);
                    const res = await request(app).get(`/api/lists/${listId}`).set('Authorization', authToken);
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Forbidden');
                });

                it('should return list if user can read', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockListData = { _id: listId, name: 'Test List', is_deleted: false };
                    const mockList = { _id: listId, is_deleted: false, toObject: jest.fn().mockReturnValue(mockListData) };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    const res = await request(app).get(`/api/lists/${listId}`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockListData);
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (List.findById as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).get(`/api/lists/${listId}`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('PATCH /api/lists/:listId', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).patch('/api/lists/507f1f77bcf86cd799439020').send({ name: 'New Name' });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).patch('/api/lists/invalid-id').set('Authorization', authToken).send({ name: 'New Name' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if name is empty string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).patch(`/api/lists/${listId}`).set('Authorization', authToken).send({ name: '   ' });
                    expect(res.status).toBe(400);
                });

                it('should rename list when name is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const updatedList = { _id: listId, name: 'New Name' };
                    (listService.renameList as jest.Mock).mockResolvedValue(updatedList);
                    const res = await request(app).patch(`/api/lists/${listId}`).set('Authorization', authToken).send({ name: 'New Name' });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(updatedList);
                    expect(listService.renameList).toHaveBeenCalledWith({ listId, name: 'New Name', user: expect.any(Object) });
                });

                it('should toggle org share when sharedWithOrganization is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const updatedList = { _id: listId, shared_with_organization: true };
                    (listService.toggleOrgShare as jest.Mock).mockResolvedValue(updatedList);
                    const res = await request(app).patch(`/api/lists/${listId}`).set('Authorization', authToken).send({ sharedWithOrganization: true });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(updatedList);
                    expect(listService.toggleOrgShare).toHaveBeenCalledWith({ listId, sharedWithOrganization: true, user: expect.any(Object) });
                });

                it('should update both name and sharedWithOrganization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const updatedList1 = { _id: listId, name: 'New Name' };
                    const updatedList2 = { _id: listId, name: 'New Name', shared_with_organization: true };
                    (listService.renameList as jest.Mock).mockResolvedValue(updatedList1);
                    (listService.toggleOrgShare as jest.Mock).mockResolvedValue(updatedList2);
                    const res = await request(app).patch(`/api/lists/${listId}`).set('Authorization', authToken).send({ name: 'New Name', sharedWithOrganization: true });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(updatedList2);
                    expect(listService.renameList).toHaveBeenCalledWith({ listId, name: 'New Name', user: expect.any(Object) });
                    expect(listService.toggleOrgShare).toHaveBeenCalledWith({ listId, sharedWithOrganization: true, user: expect.any(Object) });
                });

                it('should return ok when no updates provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).patch(`/api/lists/${listId}`).set('Authorization', authToken).send({});
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual({ ok: true });
                });

                it('should return 409 if list name is duplicate', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const error: any = new Error('Duplicate key');
                    error.code = 11000;
                    (listService.renameList as jest.Mock).mockRejectedValue(error);
                    const res = await request(app).patch(`/api/lists/${listId}`).set('Authorization', authToken).send({ name: 'New Name' });
                    expect(res.status).toBe(409);
                    expect(res.body.message).toBe('Name is already taken');
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (listService.renameList as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).patch(`/api/lists/${listId}`).set('Authorization', authToken).send({ name: 'New Name' });
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/lists/:listId', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete('/api/lists/507f1f77bcf86cd799439020');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).delete('/api/lists/invalid-id').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should delete list successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (listService.deleteList as jest.Mock).mockResolvedValue({ deleted: true });
                    const res = await request(app).delete(`/api/lists/${listId}`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual({ deleted: true });
                    expect(listService.deleteList).toHaveBeenCalledWith({ listId, user: expect.any(Object) });
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (listService.deleteList as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).delete(`/api/lists/${listId}`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/lists/:listId/share/users', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/lists/507f1f77bcf86cd799439020/share/users').send({ userId: '507f1f77bcf86cd799439021' });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/lists/invalid-id/share/users').set('Authorization', authToken).send({ userId: '507f1f77bcf86cd799439021' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if neither userId nor email is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).post(`/api/lists/${listId}/share/users`).set('Authorization', authToken).send({});
                    expect(res.status).toBe(400);
                });

                it('should return 400 if both userId and email are provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).post(`/api/lists/${listId}/share/users`).set('Authorization', authToken).send({ userId: '507f1f77bcf86cd799439021', email: '<EMAIL>' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if userId format is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).post(`/api/lists/${listId}/share/users`).set('Authorization', authToken).send({ userId: 'invalid-id' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if email format is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).post(`/api/lists/${listId}/share/users`).set('Authorization', authToken).send({ email: 'invalid-email' });
                    expect(res.status).toBe(400);
                });

                it('should share list with user by userId', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const targetUserId = '507f1f77bcf86cd799439021';
                    (listService.shareWithUser as jest.Mock).mockResolvedValue({ ok: true });
                    const res = await request(app).post(`/api/lists/${listId}/share/users`).set('Authorization', authToken).send({ userId: targetUserId });
                    expect(res.status).toBe(204);
                    expect(listService.shareWithUser).toHaveBeenCalledWith({ listId, targetUserId, user: expect.any(Object) });
                });

                it('should share list with user by email', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const email = '<EMAIL>';
                    (listService.shareWithEmail as jest.Mock).mockResolvedValue({ ok: true });
                    const res = await request(app).post(`/api/lists/${listId}/share/users`).set('Authorization', authToken).send({ email });
                    expect(res.status).toBe(204);
                    expect(listService.shareWithEmail).toHaveBeenCalledWith({ listId, email: email.trim(), user: expect.any(Object) });
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (listService.shareWithUser as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).post(`/api/lists/${listId}/share/users`).set('Authorization', authToken).send({ userId: '507f1f77bcf86cd799439021' });
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/lists/:listId/share/users/:userId', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete('/api/lists/507f1f77bcf86cd799439020/share/users/507f1f77bcf86cd799439021');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).delete('/api/lists/invalid-id/share/users/507f1f77bcf86cd799439021').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if userId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).delete(`/api/lists/${listId}/share/users/invalid-id`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should unshare list with user', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const targetUserId = '507f1f77bcf86cd799439021';
                    (listService.unshareWithUser as jest.Mock).mockResolvedValue({ ok: true });
                    const res = await request(app).delete(`/api/lists/${listId}/share/users/${targetUserId}`).set('Authorization', authToken);
                    expect(res.status).toBe(204);
                    expect(listService.unshareWithUser).toHaveBeenCalledWith({ listId, targetUserId });
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const targetUserId = '507f1f77bcf86cd799439021';
                    (listService.unshareWithUser as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).delete(`/api/lists/${listId}/share/users/${targetUserId}`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/lists/:listId/artifacts', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/lists/507f1f77bcf86cd799439020/artifacts').send({ artifactId: '507f1f77bcf86cd799439030' });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/lists/invalid-id/artifacts').set('Authorization', authToken).send({ artifactId: '507f1f77bcf86cd799439030' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if artifactId is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).post(`/api/lists/${listId}/artifacts`).set('Authorization', authToken).send({});
                    expect(res.status).toBe(400);
                });

                it('should return 400 if artifactId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).post(`/api/lists/${listId}/artifacts`).set('Authorization', authToken).send({ artifactId: 'invalid-id' });
                    expect(res.status).toBe(400);
                });

                it('should add artifact to list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    (listService.addArtifact as jest.Mock).mockResolvedValue({ ok: true });
                    const res = await request(app).post(`/api/lists/${listId}/artifacts`).set('Authorization', authToken).send({ artifactId });
                    expect(res.status).toBe(200);
                    expect(listService.addArtifact).toHaveBeenCalledWith({ listId, artifactId, user: expect.any(Object) });
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    (listService.addArtifact as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).post(`/api/lists/${listId}/artifacts`).set('Authorization', authToken).send({ artifactId });
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/lists/:listId/artifacts/:artifactId', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete('/api/lists/507f1f77bcf86cd799439020/artifacts/507f1f77bcf86cd799439030');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).delete('/api/lists/invalid-id/artifacts/507f1f77bcf86cd799439030').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if artifactId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).delete(`/api/lists/${listId}/artifacts/invalid-id`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should remove artifact from list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    (listService.removeArtifact as jest.Mock).mockResolvedValue({ ok: true });
                    const res = await request(app).delete(`/api/lists/${listId}/artifacts/${artifactId}`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(listService.removeArtifact).toHaveBeenCalledWith({ listId, artifactId, user: expect.any(Object) });
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    (listService.removeArtifact as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).delete(`/api/lists/${listId}/artifacts/${artifactId}`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/lists/:listId/artifacts', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/lists/507f1f77bcf86cd799439020/artifacts');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get('/api/lists/invalid-id/artifacts').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if page is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).get(`/api/lists/${listId}/artifacts?page=0`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if limit is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).get(`/api/lists/${listId}/artifacts?limit=0`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if limit exceeds maximum', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const res = await request(app).get(`/api/lists/${listId}/artifacts?limit=101`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 if list not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (List.findById as jest.Mock).mockResolvedValue(null);
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('List not found');
                });

                it('should return 404 if list is deleted', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockList = { _id: listId, is_deleted: true };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('List not found');
                });

                it('should return 403 if user cannot read list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(false);
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Forbidden');
                });

                it('should return artifacts with default pagination', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 1, data: [{ artifact_id: artifactId }] }]);
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([{ _id: artifactId, timestamp: new Date() }])
                            })
                        })
                    });
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://example.com/image.jpg' });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('artifacts');
                    expect(res.body).toHaveProperty('total', 1);
                    expect(res.body).toHaveProperty('page', 1);
                    expect(res.body).toHaveProperty('pages', 1);
                });

                it('should return artifacts with custom pagination', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 50, data: [{ artifact_id: artifactId }] }]);
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([{ _id: artifactId, timestamp: new Date() }])
                            })
                        })
                    });
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://example.com/image.jpg' });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts?page=2&limit=10`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('artifacts');
                    expect(res.body).toHaveProperty('total', 50);
                    expect(res.body).toHaveProperty('page', 2);
                    expect(res.body).toHaveProperty('pages', 5);
                });

                it('should handle artifacts with vessel access', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const vesselId = '507f1f77bcf86cd799439040';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 1, data: [{ artifact_id: artifactId }] }]);
                    const mockVessel = { _id: vesselId, is_active: true, region_group_id: '507f1f77bcf86cd799439050' };
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([mockVessel] as never) });
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([{ _id: artifactId, onboard_vessel_id: vesselId, timestamp: new Date() }])
                            })
                        })
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://example.com/image.jpg' });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0].canAccess).toBe(true);
                });

                it('should handle artifacts without vessel access', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const vesselId = '507f1f77bcf86cd799439040';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 1, data: [{ artifact_id: artifactId }] }]);
                    const mockVessel = { _id: vesselId, is_active: true, region_group_id: '507f1f77bcf86cd799439050' };
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([mockVessel] as never) });
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([{ _id: artifactId, onboard_vessel_id: vesselId, timestamp: new Date() }])
                            })
                        })
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(false);
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0].canAccess).toBe(false);
                    expect(res.body.artifacts[0]).toHaveProperty('_id');
                    expect(res.body.artifacts[0]).toHaveProperty('timestamp');
                });

                it('should handle artifacts with no vessel', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 1, data: [{ artifact_id: artifactId }] }]);
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([{ _id: artifactId, timestamp: new Date() }])
                            })
                        })
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(false);
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0].canAccess).toBe(false);
                });

                it('should generate image_url when image_path exists', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const vesselId = '507f1f77bcf86cd799439040';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 1, data: [{ artifact_id: artifactId }] }]);
                    const mockVessel = { _id: vesselId, is_active: true, region_group_id: '507f1f77bcf86cd799439050' };
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([mockVessel] as never) });
                    const artifactData = { _id: artifactId, image_path: 'path/to/image.jpg', bucket_name: 'test-bucket', aws_region: 'us-east-1', timestamp: new Date(), onboard_vessel_id: vesselId };
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([artifactData])
                            })
                        })
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://example.com/image.jpg' });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0].canAccess).toBe(true);
                    expect(res.body.artifacts[0].image_url).toBe('https://example.com/image.jpg');
                    expect(processBatchItem).toHaveBeenCalledWith({ bucketName: 'test-bucket', key: 'path/to/image.jpg', region: 'us-east-1' });
                });

                it('should generate video_url when video_path exists', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const vesselId = '507f1f77bcf86cd799439040';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 1, data: [{ artifact_id: artifactId }] }]);
                    const mockVessel = { _id: vesselId, is_active: true, region_group_id: '507f1f77bcf86cd799439050' };
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([mockVessel] as never) });
                    const artifactData = { _id: artifactId, video_path: 'path/to/video.mp4', bucket_name: 'test-bucket', aws_region: 'us-east-1', timestamp: new Date(), onboard_vessel_id: vesselId };
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([artifactData])
                            })
                        })
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://example.com/video.mp4' });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0].canAccess).toBe(true);
                    expect(res.body.artifacts[0].video_url).toBe('https://example.com/video.mp4');
                    expect(processBatchItem).toHaveBeenCalledWith({ bucketName: 'test-bucket', key: 'path/to/video.mp4', region: 'us-east-1' });
                });

                it('should generate thumbnail_url when thumbnail_image_path exists', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const vesselId = '507f1f77bcf86cd799439040';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 1, data: [{ artifact_id: artifactId }] }]);
                    const mockVessel = { _id: vesselId, is_active: true, region_group_id: '507f1f77bcf86cd799439050' };
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([mockVessel] as never) });
                    const artifactData = { _id: artifactId, thumbnail_image_path: 'path/to/thumb.jpg', aws_region: 'us-east-1', timestamp: new Date(), onboard_vessel_id: vesselId };
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([artifactData])
                            })
                        })
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://example.com/thumb.jpg' });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0].canAccess).toBe(true);
                    expect(res.body.artifacts[0].thumbnail_url).toBe('https://example.com/thumb.jpg');
                    expect(processBatchItem).toHaveBeenCalledWith({ bucketName: s3Config.buckets.compressedItems.name, key: 'path/to/thumb.jpg', region: 'us-east-1' });
                });

                it('should handle empty artifacts list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 0, data: [] }]);
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([])
                            })
                        })
                    });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts).toEqual([]);
                    expect(res.body.total).toBe(0);
                    expect(res.body.pages).toBe(0);
                });

                it('should handle faceted result with missing data property', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 5 }]);
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([])
                            })
                        })
                    });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts).toEqual([]);
                    expect(res.body.total).toBe(5);
                    expect(res.body.pages).toBe(1);
                });

                it('should handle faceted result with null data property', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 3, data: null }]);
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([])
                            })
                        })
                    });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts).toEqual([]);
                    expect(res.body.total).toBe(3);
                    expect(res.body.pages).toBe(1);
                });

                it('should handle artifacts with all URL types', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const artifactId = '507f1f77bcf86cd799439030';
                    const vesselId = '507f1f77bcf86cd799439040';
                    const mockList = { _id: listId, is_deleted: false };
                    (List.findById as jest.Mock).mockResolvedValue(mockList);
                    (listService.canReadList as jest.Mock).mockResolvedValue(true);
                    (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ total: 1, data: [{ artifact_id: artifactId }] }]);
                    const mockVessel = { _id: vesselId, is_active: true, region_group_id: '507f1f77bcf86cd799439050' };
                    (Vessel.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([mockVessel] as never) });
                    const artifactData = {
                        _id: artifactId,
                        image_path: 'path/to/image.jpg',
                        video_path: 'path/to/video.mp4',
                        thumbnail_image_path: 'path/to/thumb.jpg',
                        bucket_name: 'test-bucket',
                        aws_region: 'us-east-1',
                        timestamp: new Date(),
                        onboard_vessel_id: vesselId
                    };
                    (qmai.collection as jest.Mock).mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue([artifactData])
                            })
                        })
                    });
                    (canAccessVessel as jest.Mock).mockReturnValue(true);
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://example.com/file.jpg' });
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0].canAccess).toBe(true);
                    expect(res.body.artifacts[0]).toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).toHaveProperty('thumbnail_url');
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (List.findById as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).get(`/api/lists/${listId}/artifacts`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/lists/:listId/share/users', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/lists/507f1f77bcf86cd799439020/share/users');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get('/api/lists/invalid-id/share/users').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return shared users for list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    const mockUsers = [
                        { _id: '507f1f77bcf86cd799439021', name: 'User 1', email: '<EMAIL>' },
                        { _id: '507f1f77bcf86cd799439022', name: 'User 2', email: '<EMAIL>' }
                    ];
                    (listService.getSharedUsers as jest.Mock).mockResolvedValue(mockUsers);
                    const res = await request(app).get(`/api/lists/${listId}/share/users`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockUsers);
                    expect(listService.getSharedUsers).toHaveBeenCalledWith({ listId, user: expect.any(Object) });
                });

                it('should return empty array when no shared users', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (listService.getSharedUsers as jest.Mock).mockResolvedValue([]);
                    const res = await request(app).get(`/api/lists/${listId}/share/users`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual([]);
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (listService.getSharedUsers as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).get(`/api/lists/${listId}/share/users`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/lists/:listId/download', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/lists/507f1f77bcf86cd799439020/download');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if listId is invalid (not favorite or valid ObjectId)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get('/api/lists/invalid-id/download').set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should download favorites when listId is "favorite"', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.downloadFavorites as jest.Mock).mockResolvedValue({ message: 'Download started' });
                    const res = await request(app).get('/api/lists/favorite/download').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Download started! You will receive an email shortly.');
                    expect(listService.downloadFavorites).toHaveBeenCalledWith({ user: expect.any(Object) });
                    expect(listService.downloadList).not.toHaveBeenCalled();
                });

                it('should download list when listId is valid ObjectId', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (listService.downloadList as jest.Mock).mockResolvedValue({ message: 'Download started' });
                    const res = await request(app).get(`/api/lists/${listId}/download`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Download started! You will receive an email shortly.');
                    expect(listService.downloadList).toHaveBeenCalledWith({ listId, user: expect.any(Object) });
                    expect(listService.downloadFavorites).not.toHaveBeenCalled();
                });

                it('should handle errors from downloadFavorites via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.downloadFavorites as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).get('/api/lists/favorite/download').set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });

                it('should handle errors from downloadList via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const listId = '507f1f77bcf86cd799439020';
                    (listService.downloadList as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app).get(`/api/lists/${listId}/download`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/lists/copy-from', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const sourceListId = '507f1f77bcf86cd799439020';
            const targetListId = '507f1f77bcf86cd799439021';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        if (err.status) {
                            res.status(err.status).json({ message: err.message });
                        } else {
                            res.status(500).json({ message: err.message });
                        }
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(401);
                });

                it('should return 400 if targetType is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if targetType is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'invalid',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if sourceType is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                        });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if sourceType is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'invalid',
                            sourceListId,
                        });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if targetListId is invalid MongoDB ObjectId when targetType is list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId: 'invalid-id',
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if sourceListId is invalid MongoDB ObjectId when sourceType is list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId: 'invalid-id',
                        });
                    expect(res.status).toBe(400);
                });

                it('should copy artifacts from list to list successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = { message: 'Source List has been copied to Target List' };
                    (listService.copyArtifactsFromList as jest.Mock).mockResolvedValue(mockResult);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockResult);
                    expect(listService.copyArtifactsFromList).toHaveBeenCalledWith({
                        targetType: 'list',
                        targetListId,
                        sourceListId,
                        sourceType: 'list',
                        user: expect.objectContaining({ _id: userOrApiKey.authorized._id }),
                    });
                });

                it('should copy artifacts from favorites to list successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = { message: 'Favorites has been copied to Target List' };
                    (listService.copyArtifactsFromList as jest.Mock).mockResolvedValue(mockResult);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'favorites',
                        });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockResult);
                    expect(listService.copyArtifactsFromList).toHaveBeenCalledWith({
                        targetType: 'list',
                        targetListId,
                        sourceType: 'favorites',
                        user: expect.objectContaining({ _id: userOrApiKey.authorized._id }),
                    });
                });

                it('should copy artifacts from list to favorites successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = { message: 'Source List has been copied to Favorites' };
                    (listService.copyArtifactsFromList as jest.Mock).mockResolvedValue(mockResult);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'favorites',
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockResult);
                    expect(listService.copyArtifactsFromList).toHaveBeenCalledWith({
                        targetType: 'favorites',
                        sourceListId,
                        sourceType: 'list',
                        user: expect.objectContaining({ _id: userOrApiKey.authorized._id }),
                    });
                });

                it('should copy artifacts from favorites to favorites successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = { message: 'Favorites has been copied to Favorites' };
                    (listService.copyArtifactsFromList as jest.Mock).mockResolvedValue(mockResult);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'favorites',
                            sourceType: 'favorites',
                        });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockResult);
                    expect(listService.copyArtifactsFromList).toHaveBeenCalledWith({
                        targetType: 'favorites',
                        sourceType: 'favorites',
                        user: expect.objectContaining({ _id: userOrApiKey.authorized._id }),
                    });
                });

                it('should return 404 when source list not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.copyArtifactsFromList as jest.Mock).mockRejectedValue({
                        status: 404,
                        message: 'Source list not found',
                    });
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Source list not found');
                });

                it('should return 404 when target list not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.copyArtifactsFromList as jest.Mock).mockRejectedValue({
                        status: 404,
                        message: 'Target list not found',
                    });
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Target list not found');
                });

                it('should return 403 when user cannot access source list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.copyArtifactsFromList as jest.Mock).mockRejectedValue({
                        status: 403,
                        message: 'Forbidden: Cannot access source list',
                    });
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Forbidden: Cannot access source list');
                });

                it('should return 403 when user cannot access target list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.copyArtifactsFromList as jest.Mock).mockRejectedValue({
                        status: 403,
                        message: 'Forbidden: Cannot access target list',
                    });
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Forbidden: Cannot access target list');
                });

                it('should return 400 when source list is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.copyArtifactsFromList as jest.Mock).mockResolvedValue({
                        message: 'Source list is empty',
                    });
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Source list is empty');
                });

                it('should return message when all artifacts already exist', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.copyArtifactsFromList as jest.Mock).mockResolvedValue({
                        message: 'All artifacts from Source List already exist in Target List',
                    });
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('All artifacts from Source List already exist in Target List');
                });

                it('should handle errors via validateError', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (listService.copyArtifactsFromList as jest.Mock).mockRejectedValue(new Error('Test error'));
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(500);
                    expect(validateError).toHaveBeenCalled();
                });

                it('should not include targetListId in service call when targetType is favorites', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = { message: 'Source List has been copied to Favorites' };
                    (listService.copyArtifactsFromList as jest.Mock).mockResolvedValue(mockResult);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'favorites',
                            sourceType: 'list',
                            sourceListId,
                        });
                    expect(res.status).toBe(200);
                    expect(listService.copyArtifactsFromList).toHaveBeenCalledWith({
                        targetType: 'favorites',
                        sourceListId,
                        sourceType: 'list',
                        user: expect.objectContaining({ _id: userOrApiKey.authorized._id }),
                    });
                    expect(listService.copyArtifactsFromList).not.toHaveBeenCalledWith(
                        expect.objectContaining({ targetListId: expect.anything() })
                    );
                });

                it('should not include sourceListId in service call when sourceType is favorites', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = { message: 'Favorites has been copied to Target List' };
                    (listService.copyArtifactsFromList as jest.Mock).mockResolvedValue(mockResult);
                    const res = await request(app)
                        .post('/api/lists/copy-from')
                        .set('Authorization', authToken)
                        .send({
                            targetType: 'list',
                            targetListId,
                            sourceType: 'favorites',
                        });
                    expect(res.status).toBe(200);
                    expect(listService.copyArtifactsFromList).toHaveBeenCalledWith({
                        targetType: 'list',
                        targetListId,
                        sourceType: 'favorites',
                        user: expect.objectContaining({ _id: userOrApiKey.authorized._id }),
                    });
                    expect(listService.copyArtifactsFromList).not.toHaveBeenCalledWith(
                        expect.objectContaining({ sourceListId: expect.anything() })
                    );
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});

