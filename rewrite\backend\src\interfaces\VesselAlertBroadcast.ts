import mongoose from "mongoose";

export interface IVesselAlertBroadcast {
    _id: mongoose.Types.ObjectId;
    origin_unit_id: string;
    origin_vessel_id: mongoose.Types.ObjectId;
    target_unit_id: string;
    target_vessel_id: mongoose.Types.ObjectId;
    location: {
        type: string;
        coordinates: number[];
    };
    emergency_type: string;
    emergency_description: string;
    reported_at: Date;
    creation_timestamp: Date;
}

// const vesselAlertsBroadcastSchema = new mongoose.Schema({
//     creation_timestamp: {
//         type: Date,
//         required: true,
//         default: () => new Date().toISOString(),
//     },
//     reported_at: {
//         type: Date,
//         required: true,
//     },
//     origin_unit_id: {
//         type: String,
//         required: true,
//     },
//     origin_vessel_id: {
//         type: mongoose.Schema.Types.ObjectId,
//         required: true,
//     },
//     target_unit_id: {
//         type: String,
//         required: true,
//     },
//     target_vessel_id: {
//         type: mongoose.Schema.Types.ObjectId,
//         required: true,
//     },
//     location: {
//         type: {
//             type: String,
//             enum: ["Point"],
//             default: "Point",
//         },
//         coordinates: {
//             type: [Number],
//             required: true,
//         },
//     },
//     emergency_type: {
//         type: String,
//         required: true,
//     },
//     emergency_description: {
//         type: String,
//         required: true,
//     }
// });
