// run this script by running the following command in root directory: node scripts/copyFavoriteArtifacts.js
// make sure to set the fromUser and toUser variables below
// this script will copy all favorites from the fromUser to the toUser

require("dotenv").config();
const db = require("../modules/db");
const mongoose = require("mongoose");

// object ids of the users to copy from and to
const fromUser = "";
const toUser = "";

if (!fromUser || !toUser) {
    console.log("Please set the fromUser and toUser variables in the script");
    process.exit(1);
}

async function copyFavoriteArtifacts() {
    const favorites = await db.qm
        .collection("artifact_favourites")
        .find({ user_id: mongoose.Types.ObjectId(fromUser) })
        .toArray();
    const existingFavorites = await db.qm
        .collection("artifact_favourites")
        .find({ user_id: mongoose.Types.ObjectId(toUser) })
        .toArray();
    const newFavorites = favorites.filter(
        (favorite) => !existingFavorites.find((existing) => existing.artifact_id.toString() === favorite.artifact_id.toString()),
    );
    if (newFavorites.length === 0) {
        console.log("No new favorites to copy");
        return;
    }
    console.log(`Found ${newFavorites.length} new favorites`);
    console.log("Inserting new favorites");
    await db.qm.collection("artifact_favourites").insertMany(
        newFavorites.map((favorite) => ({
            ...favorite,
            _id: new mongoose.Types.ObjectId(),
            user_id: mongoose.Types.ObjectId(toUser),
            createdAt: new Date(),
            updatedAt: new Date(),
        })),
    );
    console.log("Favorite artifacts copied");
}

db.qm.once("open", async () => {
    console.log("Connected to database");
    await copyFavoriteArtifacts();
    console.log("Exiting");
    process.exit(0);
});
