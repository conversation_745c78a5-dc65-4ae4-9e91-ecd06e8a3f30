import mongoose from "mongoose";

export interface IAisVesselFlag {
    _id: mongoose.Types.ObjectId;
    mmsi: string;
    flaggedBy: mongoose.Types.ObjectId;
    flaggedAt: Date;
}

export interface IFlaggedAisVessel {
    _id: mongoose.Types.ObjectId;
    mmsi: string;
    flags: Array<{
        _id: mongoose.Types.ObjectId;
        flaggedBy: mongoose.Types.ObjectId;
        flaggedByUser: {
            _id: mongoose.Types.ObjectId;
            name: string;
            email: string;
        };
        flaggedAt: Date;
        user: {
            _id: mongoose.Types.ObjectId;
            name: string;
            email: string;
        };
        created_at: Date;
    }>;
    flagCount: number;
    latestFlagDate: Date;
    aisData?: {
        name: string | null;
        location: {
            type: string;
            coordinates: number[];
        };
        timestamp: Date;
        metadata: {
            mmsi: string;
            onboard_vessel_id: mongoose.Types.ObjectId | string | null;
            unit_id: string;
        };
        details: {
            message: Record<string, number | string | boolean>;
        };
    };
}
