import { Client, AddressType } from "@googlemaps/google-maps-services-js";
import { alpha2ToAlpha3Code } from "../utils/isoCountryCodes";

const googleMapsClient = new Client();

async function fetchGeolocation(lat: number, lng: number) {
    try {
        const { data, status } = await googleMapsClient.reverseGeocode({
            params: {
                latlng: `${lat},${lng}`,
                key: process.env.GOOGLE_API_KEY as string,
            },
        });

        if (status === 200 && data.results.length > 0) {
            const filterAddress =
                data.results.find((address) => !address.types.includes(AddressType.plus_code))?.formatted_address ||
                data.results[0].formatted_address;
            return filterAddress;
        }

        throw new Error("No valid address found.");
    } catch (err: any) {
        console.error("Google Maps Geocoding Error:", err);
        throw new Error(`Google Maps Geocoding Error: ${err.message || "Unexpected error"}`);
    }
}

async function fetchCountryIsoCode(lat: number, lng: number): Promise<string | null> {
    try {
        const { data, status } = await googleMapsClient.reverseGeocode({
            params: {
                latlng: `${lat},${lng}`,
                key: process.env.GOOGLE_API_KEY as string,
            },
        });

        if (status === 200 && data.results.length > 0) {
            const result = data.results[0];
            const countryComponent = result.address_components?.find((component: any) => component.types.includes("country"));

            if (countryComponent && countryComponent.short_name) {
                const alpha3Code = alpha2ToAlpha3Code(countryComponent.short_name);
                return alpha3Code;
            }
        }

        return null;
    } catch (err: any) {
        console.error("Google Maps Geocoding Error (Country ISO):", err);
        throw new Error(`Google Maps Geocoding Error: ${err.message || "Unexpected error"}`);
    }
}

export { fetchGeolocation, fetchCountryIsoCode, googleMapsClient };
