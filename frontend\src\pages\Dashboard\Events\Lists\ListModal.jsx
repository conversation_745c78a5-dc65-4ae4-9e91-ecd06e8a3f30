import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON><PERSON>, <PERSON><PERSON>, TextField, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useState, useEffect } from "react";
import theme from "../../../../theme";
import listsController from "../../../../controllers/Lists.controller";

const validateListName = (name) => {
    const MAX_LENGTH = 50;

    if (name.length > MAX_LENGTH) {
        return { valid: false, message: `List name must be ${MAX_LENGTH} characters or less` };
    }

    if (/\s{2,}/.test(name)) {
        return { valid: false, message: "List name cannot contain multiple consecutive spaces" };
    }

    if (name !== name.trim()) {
        return { valid: false, message: "List name cannot have leading or trailing spaces" };
    }

    const allowedPattern = /^[a-zA-Z0-9\s\-_'()[\].,!?:;]+$/;
    const emojiPattern = /[\u{1F300}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1FA00}-\u{1FA6F}]|[\u{1FA70}-\u{1FAFF}]|[\u{200D}]|[\u{20D0}-\u{20FF}]|[\u{FE00}-\u{FE0F}]|[\u{FE20}-\u{FE2F}]/gu;

    if (emojiPattern.test(name)) {
        return { valid: false, message: "Emojis are not allowed in list names" };
    }

    if (!allowedPattern.test(name)) {
        return { valid: false, message: "List name contains invalid characters. Only letters, numbers, spaces, and common punctuation are allowed." };
    }

    return { valid: true, message: "" };
};

export default function ListModal({ open, onClose, list = null, onSuccess }) {
    const [listName, setListName] = useState("");
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState("");

    const isEditMode = !!list;

    useEffect(() => {
        if (open) {
            setListName(list?.name || "");
            setError("");
        }
    }, [open, list]);

    const handleSubmit = async () => {
        const normalizedName = listName.trim().replace(/\s+/g, " ");

        if (!normalizedName) {
            setError("List name is required");
            return;
        }

        const validation = validateListName(normalizedName);
        if (!validation.valid) {
            setError(validation.message);
            return;
        }

        if (isEditMode && normalizedName === list?.name) {
            onClose();
            return;
        }
        setLoading(true);
        setError("");
        try {
            let result;
            if (isEditMode) {
                result = await listsController.updateList({ listId: list._id, name: normalizedName });
            } else {
                result = await listsController.createList({ name: normalizedName });
            }
            setListName("");
            onSuccess?.(result);
            onClose();
        } catch (err) {
            setError(err?.message || `Failed to ${isEditMode ? "rename" : "create"} list`);
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setListName("");
        setError("");
        onClose();
    };

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            maxWidth="sm"
            fullWidth
            PaperProps={{
                sx: {
                    backgroundColor: theme.palette.custom.darkBlue,
                    borderRadius: "12px",
                    border: `1px solid ${theme.palette.custom.borderColor}`,
                },
            }}
        >
            <DialogTitle sx={{ color: "#FFFFFF", fontWeight: 600, fontSize: "18px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                {isEditMode ? "Rename list" : "Add list"}
                <IconButton onClick={handleClose} size="small" sx={{ color: "#FFFFFF" }}>
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <TextField
                    fullWidth
                    placeholder="List name"
                    value={listName}
                    inputProps={{ maxLength: 50 }}
                    onChange={(e) => {
                        const value = e.target.value;
                        if (value.length <= 50) {
                            setListName(value);
                            if (error) {
                                setError("");
                            }
                        }
                    }}
                    onBlur={(e) => {
                        const trimmedValue = e.target.value.trim().replace(/\s+/g, " ");
                        if (trimmedValue) {
                            const validation = validateListName(trimmedValue);
                            if (!validation.valid) {
                                setError(validation.message);
                            } else if (trimmedValue !== e.target.value) {
                                setListName(trimmedValue);
                            }
                        }
                    }}
                    error={!!error}
                    helperText={error || "Letters, numbers, spaces, and common punctuation only (e.g., - _ ' ( ) [ ] . , ! ? : ;). Maximum 50 characters."}
                    autoFocus
                    sx={{
                        mt: 1,
                        "& .MuiOutlinedInput-root": {
                            backgroundColor: theme.palette.custom.cardBackground,
                            color: "#FFFFFF",
                            "& fieldset": {
                                borderColor: theme.palette.custom.borderColor,
                            },
                            "&:hover fieldset": {
                                borderColor: theme.palette.custom.borderColor,
                            },
                            "&.Mui-focused fieldset": {
                                borderColor: theme.palette.custom.mainBlue,
                            },
                        },
                        "& .MuiInputBase-input::placeholder": {
                            color: "#9AA4BF",
                        },
                        "& .MuiFormHelperText-root": {
                            color: "grey",
                        },
                        "& .MuiFormHelperText-root.Mui-error": {
                            color: "#E60000 !important",
                        },
                    }}
                />
            </DialogContent>
            <DialogActions sx={{ p: 2, gap: 1 }}>
                <Button
                    onClick={handleClose}
                    variant="outlined"
                    sx={{
                        color: "#FFFFFF",
                        borderColor: theme.palette.custom.borderColor,
                        textTransform: "none",
                        "&:hover": {
                            borderColor: theme.palette.custom.borderColor,
                        },
                    }}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleSubmit}
                    variant="contained"
                    disabled={loading || !listName.trim()}
                    sx={{
                        backgroundColor: loading || !listName.trim() ? theme.palette.custom.borderColor : theme.palette.custom.mainBlue,
                        color: loading || !listName.trim() ? "#9AA4BF" : "#FFFFFF",
                        textTransform: "none",
                        "&:hover": {
                            backgroundColor: loading || !listName.trim() ? theme.palette.custom.borderColor : theme.palette.custom.mainBlue,
                        },
                    }}
                >
                    Submit
                </Button>
            </DialogActions>
        </Dialog>
    );
}

