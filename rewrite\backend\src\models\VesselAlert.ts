import mongoose from "mongoose";
import db from "../modules/db";
import { IVesselAlert } from "src/interfaces/VesselAlert";

// bool emergency_button: boolean
// float64 latitude: number
// float64 longitude: number
// bool gps_valid: boolean
// string[] severe_weather_alerts_type
// string[] severe_weather_alerts_description
// string[] geo_hazard_alerts_type
// string[] geo_hazard_alerts_description
// string[] visual_alerts_type
// string[] visual_alerts_description
// uint8 max_alert_severity
// uint8 emergency_type
// string emergency_description

const vesselAlertSchema = new mongoose.Schema({
    unit_id: {
        type: String,
        required: true,
        unique: true,
    },
    vessel_id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
    },
    emergency_button: {
        type: Boolean,
        required: true,
    },
    location: {
        type: {
            type: String,
            enum: ["Point"],
            default: "Point",
        },
        coordinates: {
            type: [Number],
            required: true,
        },
    },
    gps_valid: {
        type: <PERSON>olean,
        required: true,
    },
    severe_weather_alerts_type: {
        type: [String],
        required: true,
    },
    severe_weather_alerts_description: {
        type: [String],
        required: true,
    },
    geo_hazard_alerts_type: {
        type: [String],
        required: true,
    },
    geo_hazard_alerts_description: {
        type: [String],
        required: true,
    },
    visual_alerts_type: {
        type: [String],
        required: true,
    },
    visual_alerts_description: {
        type: [String],
        required: true,
    },
    max_alert_severity: {
        type: Number,
        required: true,
    },
    emergency_type: {
        type: Number,
        required: true,
    },
    emergency_description: {
        type: String,
        required: true,
    },
    reported_at: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
});

const VesselAlert = db.qmShared.model<IVesselAlert>("VesselAlert", vesselAlertSchema, "vessel_alerts");

export default VesselAlert;
