
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/routes/ArtifactSuggestions.route.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/routes</a> ArtifactSuggestions.route.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.75% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>19/107</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/27</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/26</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>19/95</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import express, { Request, Response } from "express";
import natural from "natural";
import SpellingCorrector from "../modules/spellingCorrector";
import ArtifactSuggestion from "../models/ArtifactSuggestion";
import ArtifactSynonym from "../models/ArtifactSynonym";
import { cleanSuggestion, consoleLogObjectSize } from "../utils/functions";
import { IArtifactSuggestion, IArtifactSynonym, ISuggestionResult } from "../interfaces/ArtifactSuggestion";
&nbsp;
const router = express.Router();
&nbsp;
const wordnet = new natural.WordNet();
const spelling = new SpellingCorrector();
spelling.loadDictionary();
&nbsp;
let lastSynonymCheckedAt: Date | null = null;
const synonymCache: { synonyms: Record&lt;string, string&gt; | null } = { synonyms: null };
const synonymCachePeriodMs: number = 300000; // 5 minutes
const SUGGESTION_LIMIT: number = 10;
&nbsp;
const getSynonyms = <span class="fstat-no" title="function not covered" >(w</span>ord: string): Promise&lt;string[]&gt; =&gt; {
<span class="cstat-no" title="statement not covered" >    return new Promise(<span class="fstat-no" title="function not covered" >(r</span>esolve) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >        wordnet.lookup(word, <span class="fstat-no" title="function not covered" >(r</span>esults: { synonyms: string[] }[]) =&gt; {</span>
            const synonyms = <span class="cstat-no" title="statement not covered" >new Set&lt;string&gt;();</span>
<span class="cstat-no" title="statement not covered" >            results.forEach(<span class="fstat-no" title="function not covered" >(r</span>esult: { synonyms: string[] }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                result.synonyms.forEach(<span class="fstat-no" title="function not covered" >(s</span>yn: string) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    <span class="missing-if-branch" title="if path not taken" >I</span>if (syn.toLowerCase() !== word.toLowerCase()) {</span>
<span class="cstat-no" title="statement not covered" >                        synonyms.add(syn);</span>
                    }
                });
            });
<span class="cstat-no" title="statement not covered" >            resolve(Array.from(synonyms).slice(0, 5));</span>
        });
    });
};
&nbsp;
function <span class="fstat-no" title="function not covered" >rankSuggestions(</span>suggestionsArr: string[], suggestionDocs: IArtifactSuggestion[]): string[] {
    const docMap = <span class="cstat-no" title="statement not covered" >new Map&lt;string, IArtifactSuggestion&gt;(suggestionDocs.map(<span class="fstat-no" title="function not covered" >(s</span>: IArtifactSuggestion) =&gt; <span class="cstat-no" title="statement not covered" >[s.search, s])</span>);</span>
    let ranked: string[] = <span class="cstat-no" title="statement not covered" >suggestionsArr</span>
        .map(<span class="fstat-no" title="function not covered" >(s</span>: string) =&gt; {
            const doc = <span class="cstat-no" title="statement not covered" >docMap.get(s);</span>
<span class="cstat-no" title="statement not covered" >            return {</span>
                suggestion: s,
                click: doc ? doc.click : 0,
            };
        })
        .sort(<span class="fstat-no" title="function not covered" >(a</span>: ISuggestionResult, b: ISuggestionResult) =&gt; {
            const aScore = <span class="cstat-no" title="statement not covered" >a.click;</span>
            const bScore = <span class="cstat-no" title="statement not covered" >b.click;</span>
<span class="cstat-no" title="statement not covered" >            <span class="missing-if-branch" title="if path not taken" >I</span>if (bScore !== aScore) <span class="cstat-no" title="statement not covered" >return bScore - aScore;</span></span>
<span class="cstat-no" title="statement not covered" >            return a.suggestion.localeCompare(b.suggestion);</span>
        })
        .map(<span class="fstat-no" title="function not covered" >(r</span>: ISuggestionResult) =&gt; <span class="cstat-no" title="statement not covered" >r.suggestion)</span>;
<span class="cstat-no" title="statement not covered" >    return ranked;</span>
}
&nbsp;
function <span class="fstat-no" title="function not covered" >generateCombinations(</span>wordAlternatives: string[][], limit: number): string[] {
    const out: string[] = <span class="cstat-no" title="statement not covered" >[];</span>
    function <span class="fstat-no" title="function not covered" >combine(</span>arr: string[][], prefix: string[] = <span class="branch-0 cbranch-no" title="branch not covered" >[])</span>: void {
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (out.length &gt;= limit) <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (arr.length === 0) {</span>
<span class="cstat-no" title="statement not covered" >            out.push(prefix.join(" "));</span>
<span class="cstat-no" title="statement not covered" >            return;</span>
        }
<span class="cstat-no" title="statement not covered" >        for (let i = <span class="cstat-no" title="statement not covered" >0;</span> i &lt; arr[0].length; i++) {</span>
<span class="cstat-no" title="statement not covered" >            combine(arr.slice(1), [...prefix, arr[0][i]]);</span>
<span class="cstat-no" title="statement not covered" >            <span class="missing-if-branch" title="if path not taken" >I</span>if (out.length &gt;= limit) <span class="cstat-no" title="statement not covered" >break;</span></span>
        }
    }
<span class="cstat-no" title="statement not covered" >    combine(wordAlternatives);</span>
<span class="cstat-no" title="statement not covered" >    return out;</span>
}
&nbsp;
const getSynonymMap = <span class="fstat-no" title="function not covered" >async </span>(): Promise&lt;Record&lt;string, string&gt;&gt; =&gt; {
<span class="cstat-no" title="statement not covered" >    <span class="missing-if-branch" title="if path not taken" >I</span>if (!lastSynonymCheckedAt || !synonymCache.synonyms || Date.now() - lastSynonymCheckedAt.getTime() &gt;= synonymCachePeriodMs) {</span>
        const allSynonyms = (<span class="cstat-no" title="statement not covered" >await ArtifactSynonym.find({}, { word: 1, synonyms: 1 }))</span> as IArtifactSynonym[];
        const synonymMap: Record&lt;string, string&gt; = <span class="cstat-no" title="statement not covered" >{};</span>
<span class="cstat-no" title="statement not covered" >        for (const syn of allSynonyms) {</span>
<span class="cstat-no" title="statement not covered" >            <span class="missing-if-branch" title="if path not taken" >I</span>if (Array.isArray(syn.synonyms)) {</span>
<span class="cstat-no" title="statement not covered" >                for (const s of syn.synonyms) {</span>
<span class="cstat-no" title="statement not covered" >                    synonymMap[s.toLowerCase()] = syn.word;</span>
                }
            }
<span class="cstat-no" title="statement not covered" >            synonymMap[syn.word.toLowerCase()] = syn.word;</span>
        }
<span class="cstat-no" title="statement not covered" >        synonymCache.synonyms = synonymMap;</span>
<span class="cstat-no" title="statement not covered" >        lastSynonymCheckedAt = new Date();</span>
    }
<span class="cstat-no" title="statement not covered" >    return synonymCache.synonyms!;</span>
};
&nbsp;
function <span class="fstat-no" title="function not covered" >normalizeSuggestionsArray(</span>arr: string[], synonymMap: Record&lt;string, string&gt;): string[] {
    const normalized: string[] = <span class="cstat-no" title="statement not covered" >arr.map(<span class="fstat-no" title="function not covered" >(s</span>tr: string) =&gt;</span>
<span class="cstat-no" title="statement not covered" >        str</span>
            .split(/\s+/)
            .map(<span class="fstat-no" title="function not covered" >(w</span>ord: string) =&gt; <span class="cstat-no" title="statement not covered" >(synonymMap[word.toLowerCase()] || word).toLowerCase())</span>
            .join(" ")
            .trim(),
    );
<span class="cstat-no" title="statement not covered" >    return Array.from(new Set(normalized.filter(Boolean)));</span>
}
&nbsp;
router.post("/", <span class="fstat-no" title="function not covered" >async </span>(req: Request, res: Response) =&gt; {
<span class="cstat-no" title="statement not covered" >    try {</span>
        // 1. Validate and clean the query
        const { query } = <span class="cstat-no" title="statement not covered" >req.body;</span>
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (!query || typeof query !== "string") {</span>
<span class="cstat-no" title="statement not covered" >            return res.status(400).json({ message: "Query is required" });</span>
        }
        const cleanQuery = <span class="cstat-no" title="statement not covered" >cleanSuggestion(query);</span>
        const synonymMap = <span class="cstat-no" title="statement not covered" >await getSynonymMap();</span>
&nbsp;
        // 2. For each word, get alternatives: synonym, spell-correct, or WordNet synonyms
        const words: string[] = <span class="cstat-no" title="statement not covered" >cleanQuery.split(/\s+/);</span>
        const wordAlternatives: string[][] = <span class="cstat-no" title="statement not covered" >await Promise.all(</span>
            words.map(<span class="fstat-no" title="function not covered" >async </span>(word: string) =&gt; {
                const lower: string = <span class="cstat-no" title="statement not covered" >word.toLowerCase();</span>
<span class="cstat-no" title="statement not covered" >                <span class="missing-if-branch" title="if path not taken" >I</span>if (synonymMap[lower]) <span class="cstat-no" title="statement not covered" >return [synonymMap[lower]];</span></span>
<span class="cstat-no" title="statement not covered" >                <span class="missing-if-branch" title="if path not taken" >I</span>if (word &amp;&amp; word.split(/\s+/).length === 1) {</span>
                    const correction: string = <span class="cstat-no" title="statement not covered" >spelling.correct(word);</span>
<span class="cstat-no" title="statement not covered" >                    <span class="missing-if-branch" title="if path not taken" >I</span>if (correction &amp;&amp; correction !== word) {</span>
<span class="cstat-no" title="statement not covered" >                        return [correction];</span>
                    }
                }
                const syns: string[] = <span class="cstat-no" title="statement not covered" >await getSynonyms(word);</span>
<span class="cstat-no" title="statement not covered" >                return [word, ...syns];</span>
            }),
        );
        let generated: string[] = <span class="cstat-no" title="statement not covered" >generateCombinations(wordAlternatives, 20).map(cleanSuggestion).filter(Boolean);</span>
&nbsp;
        // 3. Find DB suggestions (partial match, case-insensitive)
        const regex: RegExp = <span class="cstat-no" title="statement not covered" >new RegExp(words.map(<span class="fstat-no" title="function not covered" >(w</span>: string) =&gt; <span class="cstat-no" title="statement not covered" >w.toLowerCase())</span>.join("|"), "i");</span>
        const dbSuggestions: IArtifactSuggestion[] = (<span class="cstat-no" title="statement not covered" >await ArtifactSuggestion.find({ search: { $regex: regex } }))</span> as IArtifactSuggestion[];
        const dbNorm: string[] = <span class="cstat-no" title="statement not covered" >dbSuggestions.map(<span class="fstat-no" title="function not covered" >(s</span>: IArtifactSuggestion) =&gt; <span class="cstat-no" title="statement not covered" >s.search)</span>;</span>
&nbsp;
        // 4. Merge, normalize, and dedupe all suggestions
        let suggestions: string[] = <span class="cstat-no" title="statement not covered" >normalizeSuggestionsArray([...generated, ...dbNorm], synonymMap);</span>
&nbsp;
        // 5. Rank suggestions
<span class="cstat-no" title="statement not covered" >        suggestions = rankSuggestions(suggestions, dbSuggestions);</span>
&nbsp;
        // 6. Always show the normalized/corrected query at the top
        const normalizedQuery: string = <span class="cstat-no" title="statement not covered" >wordAlternatives</span>
            .map(<span class="fstat-no" title="function not covered" >(a</span>: string[]) =&gt; <span class="cstat-no" title="statement not covered" >a[0])</span>
            .join(" ")
            .toLowerCase();
<span class="cstat-no" title="statement not covered" >        suggestions = [normalizedQuery, ...suggestions.filter(<span class="fstat-no" title="function not covered" >(s</span>: string) =&gt; <span class="cstat-no" title="statement not covered" >s.toLowerCase() !== normalizedQuery)</span>];</span>
&nbsp;
        // 7. Limit suggestions before updating impressions and returning
<span class="cstat-no" title="statement not covered" >        suggestions = suggestions.map(<span class="fstat-no" title="function not covered" >(s</span>: string) =&gt; <span class="cstat-no" title="statement not covered" >s.toLowerCase())</span>.slice(0, SUGGESTION_LIMIT);</span>
        const limitedDbNorm: string[] = <span class="cstat-no" title="statement not covered" >suggestions.filter(<span class="fstat-no" title="function not covered" >(s</span>: string) =&gt; <span class="cstat-no" title="statement not covered" >dbNorm.map(<span class="fstat-no" title="function not covered" >(x</span>: string) =&gt; <span class="cstat-no" title="statement not covered" >x.toLowerCase())</span>.includes(s))</span>;</span>
&nbsp;
        // 8. Update impressions for found suggestions only
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (limitedDbNorm.length &gt; 0) {</span>
<span class="cstat-no" title="statement not covered" >            ArtifactSuggestion.updateMany({ search: { $in: limitedDbNorm } }, { $inc: { impressions: 1 } }).catch(console.error);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        return res.json({ suggestions });</span>
    } catch {
<span class="cstat-no" title="statement not covered" >        return res.status(500).json({ message: "Internal server error" });</span>
    }
});
&nbsp;
<span class="missing-if-branch" title="if path not taken" >I</span>if (process.env.NODE_ENV !== "test") {
<span class="cstat-no" title="statement not covered" >    setInterval(<span class="fstat-no" title="function not covered" >() =</span>&gt; {</span>
<span class="cstat-no" title="statement not covered" >        consoleLogObjectSize(synonymCache, "artifactSuggestions.synonymCache");</span>
    }, 60000); // 1 minute
}
export default router;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2026-01-11T11:02:57.155Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    