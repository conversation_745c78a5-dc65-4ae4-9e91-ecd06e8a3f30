import React, { useState, useEffect, useRef, useMemo } from "react";
import { Mosaic, MosaicWindow } from "react-mosaic-component";
import "react-mosaic-component/react-mosaic-component.css"; // Import default styles
import axiosInstance from "../../../../axios";
import environment from "../../../../../environment";
import VideoPlayer from "../VideoPlayer";
import { Grid, Typography, IconButton, Tooltip, Skeleton } from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { Pause, PlayArrow, Fullscreen } from "@mui/icons-material";
import Slider from "@mui/material/Slider";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import { formatTime } from "../../../../utils";
import { useUser } from "../../../../hooks/UserHook.jsx";
dayjs.extend(utc);
dayjs.extend(timezone);

const Toolbar = ({ streamName }) => {
    return (
        <Grid sx={{ backgroundColor: "black", width: "100%", height: "100%" }}>
            <Typography sx={{ color: "white" }}>{streamName}</Typography>
        </Grid>
    );
};

const Tile = ({
    path,
    streamName,
    streamData,
    tileTitle,
    streamUrl,
    streamMode,
    totalDuration,
    scrubBarSlotInterval,
    selectedStream,
    referenceTime,
    lockSlider,
    sharedPlayback,
    setSharedPlayback,
    setTimestamp,
    streamError,
}) => {
    const [stream, setStream] = useState(streamUrl);
    const [streamInterval, setStreamInterval] = useState(scrubBarSlotInterval);
    const [localError, setLocalError] = useState(null);
    const [play, setPlay] = useState({
        offset: 0,
        currentVideoPlayTime: 0,
    });

    useEffect(() => {
        setStream(streamUrl);
        setLocalError(null);
    }, [streamUrl]);

    const loadStreamUrl = async () => {
        try {
            const url = await axiosInstance
                .get(
                    `${environment.VITE_API_URL}/api/v2/kinesis/dashStreamingSessionURL?${new URLSearchParams({ region: streamData.Region, streamName: streamName, streamMode, startTimestamp: streamInterval }).toString()}`,
                    { meta: { showSnackbar: false } },
                )
                .then((res) => res.data.data.url);
            setStream(url);
            setLocalError(null);
        } catch (err) {
            console.log(err.message || " An unexpected error occurred. Please try again later.");
            const errorMessage = err?.response?.data?.message || err?.message || "An unexpected error occurred. Please try again later.";
            setLocalError(errorMessage);
            setStream(null);
        }
    };

    useEffect(() => {
        if (streamInterval !== scrubBarSlotInterval) {
            loadStreamUrl();
        }
    }, [streamInterval]);
    useEffect(() => {
        setPlay({
            offset: 0,
            currentVideoPlayTime: 0,
        });
    }, [totalDuration]);

    const error = streamError || localError;
    const showErrorMessage = !stream && error;

    return (
        <>
            {stream ? (
                <MosaicWindow title="Live Video" path={path} renderToolbar={() => <Toolbar streamName={tileTitle} />} draggable={false}>
                    <VideoPlayer
                        view="Mosaic"
                        setPlayBack={setPlay}
                        playBack={play}
                        streamUrl={stream}
                        setStreamUrl={setStream}
                        streamMode={streamMode}
                        totalDuration={totalDuration}
                        setScrubBarSlotInterval={setStreamInterval}
                        selectedStream={selectedStream}
                        referenceTime={referenceTime}
                        sharedPlayback={sharedPlayback}
                        setSharedPlayback={setSharedPlayback}
                        lockSlider={lockSlider}
                        setTimestamp={setTimestamp}
                    />
                </MosaicWindow>
            ) : showErrorMessage ? (
                <MosaicWindow title="Live Video" path={path} renderToolbar={() => <Toolbar streamName={tileTitle} />} draggable={false}>
                    <Grid
                        container
                        sx={{
                            width: "100%",
                            height: "100%",
                            backgroundColor: "black",
                            display: "flex",
                            alignItems: "center",
                            justifyContent: "center",
                            padding: 2,
                        }}
                    >
                        <Typography
                            sx={{
                                textAlign: "center",
                                color: "white",
                                fontSize: { xs: "0.9rem", md: "1.1rem" },
                                padding: 2,
                            }}
                        >
                            No streams found in the specified timestamp range.
                        </Typography>
                    </Grid>
                </MosaicWindow>
            ) : (
                ""
            )}
        </>
    );
};

const MosaicView = ({
    streams,
    streamMode,
    totalDuration,
    setScrubBarSlotInterval,
    selectedStream,
    referenceTime,
    scrubBarSlotInterval,
    scrubbarToggle,
    lockSlider,
    mosaicLayout,
    currentIndex,
    setCurrentIndex,
    setTimestamp,
}) => {
    const allStreams = useMemo(() => streams, [streams]);
    const [elementMap, setElementMap] = useState({});
    const [errorMap, setErrorMap] = useState({});
    const [loading, setLoading] = useState(true);

    const { user } = useUser();
    // const [currentIndex, setCurrentIndex] = useState(0);
    const [hoverTime, setHoverTime] = useState(null);
    const scrubBarRef = useRef();
    const videoContainerRef = useRef(null);

    const [sharedPlayback, setSharedPlayback] = useState({
        offset: 0,
        currentVideoPlayTime: 0,
        isPaused: false,
    });
    const [sharedScrubValue, setSharedScrubValue] = useState(0);

    const mosaicLayouts = {
        2: {
            direction: "column",
            first: "a",
            second: "b",
            splitPercentage: 50,
        },
        3: {
            direction: "row",
            first: "a",
            second: "b",
            third: "c",
            splitPercentage: 33.33,
        },
        4: {
            direction: "row",
            first: {
                direction: "column",
                first: "a",
                second: "b",
                splitPercentage: 50,
            },
            second: {
                direction: "column",
                first: "c",
                second: "d",
                splitPercentage: 50,
            },
            splitPercentage: 50,
        },
        6: {
            direction: "column",
            first: {
                direction: "row",
                first: "a",
                second: {
                    direction: "row",
                    first: "b",
                    second: "c",
                    splitPercentage: 50,
                },
                splitPercentage: 33.33,
            },
            second: {
                direction: "row",
                first: "d",
                second: {
                    direction: "row",
                    first: "e",
                    second: "f",
                    splitPercentage: 50,
                },
                splitPercentage: 33.33,
            },
            splitPercentage: 50,
        },

        8: {
            direction: "row",
            first: {
                direction: "column",
                first: {
                    direction: "row",
                    first: "a",
                    second: "b",
                    splitPercentage: 50,
                },
                second: {
                    direction: "row",
                    first: "c",
                    second: "d",
                    splitPercentage: 50,
                },
                splitPercentage: 50,
            },
            second: {
                direction: "column",
                first: {
                    direction: "row",
                    first: "e",
                    second: "f",
                    splitPercentage: 50,
                },
                second: {
                    direction: "row",
                    first: "g",
                    second: "h",
                    splitPercentage: 50,
                },
                splitPercentage: 50,
            },
            splitPercentage: 50,
        },
    };

    const [mosaicState, setMosaicState] = useState(mosaicLayouts[mosaicLayout]);

    useEffect(() => {
        setMosaicState(mosaicLayouts[mosaicLayout]);
    }, [mosaicLayout]);

    const loadAllStreamUrls = async () => {
        const urls = {};
        const errors = {};
        setLoading(true);
        const countToLoad = Math.min(mosaicLayout, allStreams.length);
        for (let i = 0 + currentIndex; i < countToLoad + currentIndex; i++) {
            try {
                if (allStreams[i]) {
                    const url = await axiosInstance
                        .get(
                            `${environment.VITE_API_URL}/api/v2/kinesis/dashStreamingSessionURL?${new URLSearchParams({
                                region: allStreams[i].Region,
                                streamName: allStreams[i].StreamName,
                                streamMode,
                                startTimestamp: scrubBarSlotInterval,
                            }).toString()}`,
                            { meta: { showSnackbar: false } },
                        )
                        .then((res) => res.data.data.url);
                    urls[allStreams[i].StreamName] = url;
                    // Clear error if URL loads successfully
                    if (errors[allStreams[i].StreamName]) {
                        delete errors[allStreams[i].StreamName];
                    }
                }
            } catch (err) {
                console.error("Failed to load stream URL for", allStreams[i]?.StreamName, err);
                const errorMessage = err?.response?.data?.message || err?.message || "An unexpected error occurred. Please try again later.";
                errors[allStreams[i].StreamName] = errorMessage;
                delete urls[allStreams[i].StreamName];
            }
        }
        setLoading(false);
        setElementMap(urls);
        setErrorMap(errors);
    };

    useEffect(() => {
        loadAllStreamUrls();
    }, [streamMode, totalDuration, currentIndex, mosaicLayout, streams]);

    // const [mosaicState, setMosaicState] = useState({
    //     direction: "row",
    //     first: {
    //         direction: "column",
    //         first: "a",
    //         second: "b",
    //         splitPercentage: 50,
    //     },
    //     second: {
    //         direction: "column",
    //         first: "c",
    //         second: "d",
    //         splitPercentage: 50,
    //     },
    //     splitPercentage: 50,
    // });

    // useEffect(() => {
    //     setMosaicState((prevState) => ({
    //         ...prevState,
    //         direction: isMobile ? "column" : "row",
    //     }));
    // }, [isMobile]);

    const handleNext = () => {
        setCurrentIndex((prevIndex) => prevIndex + mosaicLayout);
    };
    const handlePrev = () => {
        setCurrentIndex((prevIndex) => prevIndex - mosaicLayout);
    };
    const isPrevDisabled = currentIndex === 0;
    const isNextDisabled = currentIndex + mosaicLayout >= allStreams.length;
    const handleHover = (event) => {
        const scrubBar = scrubBarRef.current;
        const hoverPosition = event.nativeEvent.offsetX;
        const scrubWidth = scrubBar.offsetWidth;
        const hoveredTime = (hoverPosition / scrubWidth) * totalDuration;
        setHoverTime(hoveredTime);
    };
    const handleSharedScrub = (event, newValue) => {
        setSharedScrubValue(newValue);
    };
    const handleSharedScrubCommit = (event, newValue) => {
        const clickedTime = (newValue / 100) * totalDuration;
        setSharedPlayback({
            offset: clickedTime,
            currentVideoPlayTime: 0,
        });

        // setScrubBarSlotInterval(totalDuration / 60 - clickedTime / 60);
        const startDate = dayjs(referenceTime.current - totalDuration * 1000);
        const pastDate = startDate.add(hoverTime, "second");
        // ISO string with timezone offset
        // const isoDateWithOffset = pastDate.format(); // e.g. "2025-07-24T15:30:00+05:30"
        // ISO string in UTC
        const isoDateUTC = pastDate.toISOString();
        setScrubBarSlotInterval(isoDateUTC);
        allStreams.forEach((stream) => {
            const region = stream.Region;
            const streamName = stream.StreamName;
            // const minutes = totalDuration / 60 - clickedTime / 60;
            const startDate = dayjs(referenceTime.current - totalDuration * 1000);
            const pastDate = startDate.add(hoverTime, "second");
            const isoDateUTC = pastDate.toISOString();

            axiosInstance
                .get(
                    `${environment.VITE_API_URL}/api/v2/kinesis/dashStreamingSessionURL?${new URLSearchParams({
                        region,
                        streamName,
                        streamMode,
                        startTimestamp: isoDateUTC,
                    }).toString()}`,
                    { meta: { showSnackbar: false } },
                )
                .then((res) => {
                    setElementMap((prev) => ({
                        ...prev,
                        [streamName]: res.data.data.url,
                    }));
                })
                .catch((err) => console.error("Failed to update stream URL for", streamName, err));
        });
    };
    const { timeString, formattedDate } = formatTime(user, {
        timeInSeconds: hoverTime || 0,
        totalDuration: totalDuration,
        referenceTime: referenceTime,
    });
    const handlePause = () => {
        const isPlaying = !sharedPlayback.isPaused;
        setSharedPlayback((prev) => ({
            ...prev,
            isPaused: isPlaying,
        }));
    };

    const handleTimeUpdate = () => {
        setSharedPlayback((prev) => ({
            ...prev,
            offset: prev.offset + 1,
        }));
    };

    useEffect(() => {
        const intervalId = setInterval(() => {
            if (!sharedPlayback.isPaused) {
                handleTimeUpdate();
            }
        }, 1000);

        return () => clearInterval(intervalId);
    }, [sharedPlayback.isPaused]);

    const handleFullscreen = () => {
        if (videoContainerRef.current) {
            if (!document.fullscreenElement) {
                videoContainerRef.current.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
    };

    useEffect(() => {
        setSharedScrubValue((sharedPlayback.offset / totalDuration) * 100);
    }, [sharedPlayback.offset, totalDuration]);

    return (
        <Grid
            container
            minHeight={{ xs: 300, lg: "auto" }}
            flexDirection={{ xs: "column", lg: "column" }}
            sx={{ height: { xs: "60vh", lg: "100%" }, zIndex: 0 }}
        >
            {loading ? (
                <Grid width={"100%"} height={"100%"}>
                    <Skeleton animation="wave" variant="rectangular" height={"100%"} />
                </Grid>
            ) : (
                <Grid
                    container
                    ref={videoContainerRef}
                    sx={{
                        backgroundColor: "black",
                        width: "100%",
                        height: "100%",
                        boxSizing: "border-box",
                        position: "relative",
                    }}
                    zIndex={0}
                    flexDirection={"column"}
                >
                    <Grid
                        item
                        container
                        sx={{
                            width: "100%",
                            "& .mosaic-window-body, .mosaic-preview .mosaic-window-body": {
                                backgroundColor: "black",
                            },
                        }}
                        flexDirection={"column"}
                        size={"grow"}
                    >
                        <Mosaic
                            renderTile={(id, path) => {
                                const streamIndex = id.charCodeAt(0) - 97 + currentIndex;
                                const stream = allStreams[streamIndex];
                                const streamName = stream ? stream.StreamName : "No Stream Available";
                                const tileTitle = stream ? stream.VesselName || "Unregistered" : "No Stream Available";
                                const streamUrl = stream ? elementMap[stream.StreamName] : null;
                                const streamError = stream ? errorMap[stream.StreamName] : null;
                                return (
                                    <Tile
                                        key={id}
                                        path={path}
                                        streamData={stream}
                                        streamName={streamName}
                                        streamUrl={streamUrl}
                                        tileTitle={tileTitle}
                                        streamMode={streamMode}
                                        totalDuration={totalDuration}
                                        setScrubBarSlotInterval={setScrubBarSlotInterval}
                                        selectedStream={selectedStream}
                                        referenceTime={referenceTime}
                                        scrubBarSlotInterval={scrubBarSlotInterval}
                                        scrubbarToggle={scrubbarToggle}
                                        lockSlider={lockSlider}
                                        sharedPlayback={sharedPlayback}
                                        setSharedPlayback={setSharedPlayback}
                                        setTimestamp={setTimestamp}
                                        streamError={streamError}
                                    />
                                );
                            }}
                            value={mosaicState}
                            className="mosaic-container"
                        />
                    </Grid>
                    {lockSlider && (
                        <Grid
                            sx={{
                                background: "#FFFFFF4D",
                                display: "flex",
                                flexDirection: "column",
                            }}
                            size="auto"
                        >
                            <Grid
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    width: "100%",
                                    height: "100%",
                                    justifyContent: "space-between"
                                }}
                            >
                                <IconButton
                                    onClick={handlePause}
                                    sx={{
                                        color: "white",
                                        borderRadius: 0,
                                        "&:hover": {
                                            color: "#1976d2",
                                            backgroundColor: "#FFFFFF",
                                        },
                                    }}
                                >
                                    {sharedPlayback.isPaused ? <PlayArrow /> : <Pause />}
                                </IconButton>

                                <Grid
                                    ref={scrubBarRef}
                                    sx={{
                                        flexGrow: 1,
                                        height: "100%",
                                        mx: 0,
                                        display: streamMode === "ON_DEMAND" ? "" : "none"
                                    }}
                                    onMouseMove={handleHover}
                                    onMouseLeave={() => setHoverTime(null)}
                                >
                                    <Tooltip
                                        open={hoverTime !== null}
                                        title={
                                            <Grid sx={{ textAlign: "center" }}>
                                                <Grid>{timeString}</Grid>
                                                <Grid>({formattedDate})</Grid>
                                            </Grid>
                                        }
                                        placement="top"
                                        arrow
                                        followCursor
                                        PopperProps={{
                                            container: videoContainerRef.current,
                                        }}
                                    >
                                        <Slider
                                            value={sharedScrubValue}
                                            onChange={handleSharedScrub}
                                            onChangeCommitted={handleSharedScrubCommit}
                                            sx={{
                                                color: "#1976d2",
                                                padding: "6px 0",
                                                height: "100%",
                                                "& .MuiSlider-thumb": {
                                                    width: 10,
                                                    height: 40,
                                                    borderRadius: 0,
                                                    backgroundColor: "#FFFFFF",
                                                    transform: "translate(-50%, -65%)",
                                                    "&:hover, &.Mui-focusVisible": {
                                                        boxShadow: "none",
                                                    },
                                                },
                                                "& .MuiSlider-track": {
                                                    height: "100%",
                                                    transform: "translateY(-65%)",
                                                    borderRadius: 0,
                                                    color: "#1976d2",
                                                    maxHeight: 40,
                                                },
                                                "& .MuiSlider-rail": {
                                                    height: "100%",
                                                    transform: "translateY(-65%)",
                                                    borderRadius: 0,
                                                    opacity: 0.28,
                                                    maxHeight: 40,
                                                    backgroundColor: "#FFFFFF4D",
                                                },
                                            }}
                                        />
                                    </Tooltip>
                                </Grid>

                                <IconButton
                                    onClick={handleFullscreen}
                                    sx={{
                                        color: "white",
                                        borderRadius: 0,
                                        "&:hover": {
                                            color: "#1976d2",
                                            backgroundColor: "#FFFFFF",
                                        },
                                    }}
                                >
                                    <Fullscreen />
                                </IconButton>
                            </Grid>
                        </Grid>
                    )}
                    {allStreams.length > mosaicLayout && (
                        <Grid item container sx={{ display: "flex", alignItems: "center", justifyContent: "center", color: "white" }}>
                            <IconButton onClick={handlePrev} disabled={isPrevDisabled}>
                                <ArrowBackIcon sx={{ color: isPrevDisabled ? "gray" : "white" }} />
                            </IconButton>
                            {currentIndex + 1}-{Math.min(currentIndex + mosaicLayout, allStreams.length)} of {allStreams.length}
                            <IconButton onClick={handleNext} disabled={isNextDisabled}>
                                <ArrowForwardIcon sx={{ color: isNextDisabled ? "gray" : "white" }} />
                            </IconButton>
                        </Grid>
                    )}
                </Grid>
            )}
        </Grid>
    );
};

export default MosaicView;
