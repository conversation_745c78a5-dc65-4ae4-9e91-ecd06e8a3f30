import mongoose from "mongoose";
import VesselAlert from "../models/VesselAlert";
import VesselAlertBroadcast from "../models/VesselAlertBroadcast";
import { IVesselAlert } from "../interfaces/VesselAlert";
import { IVesselAlertBroadcast } from "../interfaces/VesselAlertBroadcast";

interface IEmergencyVesselsCount {
    count: number;
    vessel_ids: string[];
}

interface INearbyVesselAlertBroadcast {
    _id: mongoose.Types.ObjectId;
    target_unit_id: string;
    target_vessel_id: mongoose.Types.ObjectId;
    broadcasted_alerts: IVesselAlertBroadcast[];
}

class VesselAlertService {
    async getEmergencyVesselsCount(): Promise<IEmergencyVesselsCount> {
        try {
            const emergencyVessels = await VesselAlert.find(
                { emergency_button: true },
                { vessel_id: 1, _id: 0 }
            ).lean();

            const vessel_ids = emergencyVessels.map(vessel => vessel.vessel_id.toString());

            return {
                count: emergencyVessels.length,
                vessel_ids
            };
        } catch (error) {
            console.error("[VesselAlertService.getEmergencyVesselsCount] Error:", error);
            throw new Error("Failed to fetch emergency vessels count");
        }
    }

    async getVesselAlertsStatus(): Promise<IVesselAlert[]> {
        try {
            const vesselAlerts = await VesselAlert.find({}).lean();
            return vesselAlerts;
        } catch (error) {
            console.error("[VesselAlertService.getVesselAlertsStatus] Error:", error);
            throw new Error("Failed to fetch vessel alerts status");
        }
    }

    /**
     * Fetch nearby vessel alert broadcasts
     * Groups alert broadcasts by target vessel and returns them with their broadcasted alerts
     */
    async getNearbyVesselAlertBroadcasts(): Promise<INearbyVesselAlertBroadcast[]> {
        try {
            const aggregationPipeline = [
                {
                    $group: {
                        _id: {
                            target_unit_id: "$target_unit_id",
                            target_vessel_id: "$target_vessel_id"
                        },
                        broadcasted_alerts: {
                            $push: {
                                _id: "$_id",
                                origin_unit_id: "$origin_unit_id",
                                origin_vessel_id: "$origin_vessel_id",
                                target_unit_id: "$target_unit_id",
                                target_vessel_id: "$target_vessel_id",
                                location: "$location",
                                emergency_type: "$emergency_type",
                                emergency_description: "$emergency_description",
                                reported_at: "$reported_at",
                                creation_timestamp: "$creation_timestamp"
                            }
                        }
                    }
                },
                {
                    $project: {
                        _id: new mongoose.Types.ObjectId(),
                        target_unit_id: "$_id.target_unit_id",
                        target_vessel_id: "$_id.target_vessel_id",
                        broadcasted_alerts: 1
                    }
                }
            ];

            const result = await VesselAlertBroadcast.aggregate(aggregationPipeline);
            return result;
        } catch (error) {
            console.error("[VesselAlertService.getNearbyVesselAlertBroadcasts] Error:", error);
            throw new Error("Failed to fetch nearby vessel alert broadcasts");
        }
    }
}

const vesselAlertService = new VesselAlertService();

export default vesselAlertService;
