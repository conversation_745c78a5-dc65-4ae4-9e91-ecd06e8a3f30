import { jest, describe, it, expect, beforeEach, afterEach } from '@jest/globals';

const originalEnv = process.env;

const mockSet = jest.fn();
const mockOn = jest.fn();
const mockCreateConnection = jest.fn(() => ({
    on: mockOn,
}));

jest.mock('mongoose', () => ({
    set: mockSet,
    createConnection: mockCreateConnection,
}));

describe('Database Module', () => {
    beforeEach(() => {
        process.env = {
            ...originalEnv,
            MONGO_URI: 'mongodb://localhost:27017/test',
            MONGO_SEARCH_URI: 'mongodb://localhost:27017/test',
        };
        jest.clearAllMocks();
        jest.resetModules();
    });

    afterEach(() => {
        process.env = originalEnv;
        jest.resetModules();
    });

    it('should throw error when MONGO_URI environment variable is not configured', () => {
        delete process.env.MONGO_URI;

        expect(() => {
            require('../../modules/db');
        }).toThrow('MONGO_URI must be set in env variables');
    });

    it('should throw error when MONGO_SEARCH_URI environment variable is not configured', () => {
        delete process.env.MONGO_SEARCH_URI;

        expect(() => {
            require('../../modules/db');
        }).toThrow('MONGO_SEARCH_URI must be set in env variables');
    });

    it('should establish connections to all required MongoDB databases when MONGO_URI is configured', () => {
        const _db = require('../../modules/db').default;

        expect(mockSet).toHaveBeenCalledWith('strictQuery', false);
        expect(mockCreateConnection).toHaveBeenCalledTimes(9);
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_URI, { dbName: 'quartermaster-local' });
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_URI, { dbName: 'artifact_processor-local' });
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_URI, { dbName: 'quartermaster-shared-local' });
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_URI, { dbName: 'locations_optimized' });
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_URI, { dbName: 'locations_raw' });
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_URI, { dbName: 'ais_raw' });
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_URI, { dbName: 'audio_processor' });
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_URI, { dbName: 'lookups' });
        expect(mockCreateConnection).toHaveBeenCalledWith(process.env.MONGO_SEARCH_URI, { dbName: 'artifacts' });
    });

    it('should export all database connection objects for application use', () => {
        const db = require('../../modules/db').default;

        expect(db.qm).toBeDefined();
        expect(db.qmai).toBeDefined();
        expect(db.qmShared).toBeDefined();
        expect(db.locationsOptimized).toBeDefined();
        expect(db.locationsRaw).toBeDefined();
        expect(db.aisRaw).toBeDefined();
        expect(db.audio).toBeDefined();
        expect(db.lookups).toBeDefined();
        expect(db.searchArtifacts).toBeDefined();
    });

    it('should register event listeners for connection open and error events on all database connections', () => {
        require('../../modules/db').default;

        const openRegistrations = mockOn.mock.calls.filter((call: any[]) => call[0] === 'open');
        const errorRegistrations = mockOn.mock.calls.filter((call: any[]) => call[0] === 'error');

        expect(openRegistrations.length).toBe(9);
        expect(errorRegistrations.length).toBe(9);
        openRegistrations.forEach((call: any[]) => expect(call[1]).toEqual(expect.any(Function)));
        errorRegistrations.forEach((call: any[]) => expect(call[1]).toEqual(expect.any(Function)));
    });

    it('should log successful connection messages when database connections are established', () => {
        const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => { });
        require('../../modules/db').default;

        const openHandlers = mockOn.mock.calls
            .filter((call: any[]) => call[0] === 'open')
            .map((call: any[]) => call[1] as () => void);

        expect(openHandlers.length).toBe(9);
        openHandlers.forEach((handler: () => void) => handler());
        expect(consoleSpy).toHaveBeenCalledTimes(9);

        consoleSpy.mockRestore();
    });

    it('should log error messages when database connection failures occur', () => {
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => { });
        require('../../modules/db').default;

        const errorHandlers = mockOn.mock.calls
            .filter((call: any[]) => call[0] === 'error')
            .map((call: any[]) => call[1] as (err: Error) => void);

        expect(errorHandlers.length).toBe(9);
        errorHandlers.forEach((handler: (err: Error) => void) => handler(new Error('Connection failed')));
        expect(consoleSpy).toHaveBeenCalledTimes(9);

        consoleSpy.mockRestore();
    });
});