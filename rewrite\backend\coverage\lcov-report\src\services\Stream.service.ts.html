
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/services/Stream.service.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/services</a> Stream.service.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">22.22% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>12/54</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/25</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.66% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/15</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.44% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>11/45</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import Region from "../models/Region";
import vesselService from "../services/Vessel.service";
import awsKinesis from "../modules/awsKinesis";
import pLimit from "p-limit";
import RegionGroup from "../models/RegionGroup";
import { consoleLogObjectSize } from "../utils/functions";
import { IRegion } from "../interfaces/Region";
import { IVessel } from "../interfaces/Vessel";
import { IRegionGroup } from "../interfaces/RegionGroup";
import { IStreamServiceInfo } from "../interfaces/Kinesis";
&nbsp;
const limit = pLimit(1);
&nbsp;
class StreamService {
    private streamsInfo: { data: IStreamServiceInfo[]; lastCheck: Date };
&nbsp;
    constructor() {
        this.streamsInfo = { data: [], lastCheck: new Date(0) };
        if (process.env.NODE_ENV === "test") return;
<span class="cstat-no" title="statement not covered" >        setInterval(<span class="fstat-no" title="function not covered" >() =</span>&gt; {</span>
<span class="cstat-no" title="statement not covered" >            consoleLogObjectSize(this.streamsInfo.data, "streamService.streamsInfo");</span>
        }, 60000); // 1 minutes
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    async </span>resetCache(): Promise&lt;void&gt; {
<span class="cstat-no" title="statement not covered" >        this.streamsInfo = { data: [], lastCheck: new Date(0) };</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    async </span>fetchAll({ regions }: { regions?: string[] } = <span class="branch-0 cbranch-no" title="branch not covered" >{})</span>: Promise&lt;IStreamServiceInfo[]&gt; {
        const lastCheck = <span class="cstat-no" title="statement not covered" >this.streamsInfo.lastCheck;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (Date.now() - lastCheck.getTime() &lt; 300000) {</span>
            // return existing data if last check was less than 5 minutes ago
<span class="cstat-no" title="statement not covered" >            console.log("returning existing");</span>
<span class="cstat-no" title="statement not covered" >            return this.streamsInfo.data.filter(<span class="fstat-no" title="function not covered" >(s</span>tream) =&gt; (<span class="cstat-no" title="statement not covered" >regions === undefined ? true : regions.includes(stream.region))</span>);</span>
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        console.log("[StreamService.fetchAll] Fetching updated list");</span>
&nbsp;
        const awsRegions = <span class="cstat-no" title="statement not covered" >(await Region.find()).filter(<span class="fstat-no" title="function not covered" >(r</span>: IRegion) =&gt; <span class="cstat-no" title="statement not covered" >r.is_live)</span>;</span>
&nbsp;
        const allStreams = <span class="cstat-no" title="statement not covered" >await Promise.all(</span>
            awsRegions.map(<span class="fstat-no" title="function not covered" >async </span>(region) =&gt; {
<span class="cstat-no" title="statement not covered" >                try {</span>
<span class="cstat-no" title="statement not covered" >                    return await awsKinesis.listStreams({ region: region.value });</span>
                } catch {
<span class="cstat-no" title="statement not covered" >                    console.error(`[StreamService.fetchAll] [FATAL] Error fetching streams for region ${region.value}`);</span>
<span class="cstat-no" title="statement not covered" >                    return [];</span>
                }
            }),
        );
&nbsp;
        const flattenedStreams = <span class="cstat-no" title="statement not covered" >allStreams.flat();</span>
&nbsp;
        const [regionGroups, vessels] = <span class="cstat-no" title="statement not covered" >await Promise.all([</span>
            RegionGroup.find(),
            vesselService.find({ unit_id: { $ne: null } }, { _id: 1, unit_id: 1, name: 1, thumbnail_compressed_s3_key: 1, region_group_id: 1 }),
        ]);
&nbsp;
        const unitToVesselMap = <span class="cstat-no" title="statement not covered" >new Map&lt;string, Partial&lt;IVessel&gt;&gt;();</span>
<span class="cstat-no" title="statement not covered" >        vessels.forEach(<span class="fstat-no" title="function not covered" >(v</span>essel: Partial&lt;IVessel&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >            <span class="missing-if-branch" title="if path not taken" >I</span>if (vessel.unit_id) {</span>
<span class="cstat-no" title="statement not covered" >                unitToVesselMap.set(vessel.unit_id, vessel);</span>
            }
        });
&nbsp;
        const streamsInfo: IStreamServiceInfo[] = <span class="cstat-no" title="statement not covered" >flattenedStreams.map(<span class="fstat-no" title="function not covered" >(s</span>tream) =&gt; {</span>
            const vessel = <span class="cstat-no" title="statement not covered" >unitToVesselMap.get(stream.StreamName);</span>
            // const regionGroup = vessel ? regionGroups.find((rg) =&gt; rg.vessel_ids.some((v) =&gt; v.toString() === vessel._id.toString())) : null;
            const regionGroup = <span class="cstat-no" title="statement not covered" >vessel ? regionGroups.find(<span class="fstat-no" title="function not covered" >(r</span>g: IRegionGroup) =&gt; <span class="cstat-no" title="statement not covered" >rg._id.toString() === vessel.region_group_id?.toString())</span> : null;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >            return {</span>
                unit_id: stream.StreamName,
                name: vessel?.name || null,
                thumbnail: vessel?.thumbnail_compressed_s3_key || null,
                region: stream.Region || "",
                is_live: stream.IsLive || false,
                timezone: regionGroup?.timezone || null,
                // region_group_id: regionGroup?._id || null,
                region_group_id: vessel?.region_group_id || null,
            };
        });
&nbsp;
<span class="cstat-no" title="statement not covered" >        this.streamsInfo = { data: streamsInfo, lastCheck: new Date() };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return streamsInfo.filter(<span class="fstat-no" title="function not covered" >(s</span>tream) =&gt; (<span class="cstat-no" title="statement not covered" >regions === undefined ? true : regions.includes(stream.region))</span>);</span>
    }
&nbsp;
<span class="fstat-no" title="function not covered" >    async </span>fetchSingle({ unitId }: { unitId: string }): Promise&lt;IStreamServiceInfo | null&gt; {
        let streamInfo = <span class="cstat-no" title="statement not covered" >this.streamsInfo.data.find(<span class="fstat-no" title="function not covered" >(s</span>) =&gt; <span class="cstat-no" title="statement not covered" >s.unit_id === unitId)</span>;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (!streamInfo) {</span>
            const streams = <span class="cstat-no" title="statement not covered" >await limit(<span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >this.fetchAll())</span>;</span>
<span class="cstat-no" title="statement not covered" >            streamInfo = streams.find(<span class="fstat-no" title="function not covered" >(s</span>) =&gt; <span class="cstat-no" title="statement not covered" >s.unit_id === unitId)</span>;</span>
<span class="cstat-no" title="statement not covered" >            <span class="missing-if-branch" title="if path not taken" >I</span>if (!streamInfo) {</span>
<span class="cstat-no" title="statement not covered" >                console.error("[StreamService.fetchSingle] couldn't find stream", unitId);</span>
            }
        }
&nbsp;
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (!streamInfo) <span class="cstat-no" title="statement not covered" >return null;</span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        return streamInfo;</span>
    }
}
&nbsp;
const streamService = new StreamService();
&nbsp;
export default streamService;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2026-01-11T11:02:56.580Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    