import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import listService from '../../services/List.service';
import List from '../../models/List';
import ListACL from '../../models/ListACL';
import ListArtifact from '../../models/ListArtifact';
import ArtifactFavourites from '../../models/ArtifactFavourites';
import User from '../../models/User';
import Vessel from '../../models/Vessel';
import db from '../../modules/db';
import { canAccessVessel } from '../../utils/functions';
import mongoose from 'mongoose';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/List', () => require('../mocks/models/list.mock'));
jest.mock('../../models/ListACL', () => require('../mocks/models/listACL.mock'));
jest.mock('../../models/ListArtifact', () => require('../mocks/models/listArtifact.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ArtifactFavourites', () => require('../mocks/models/artifactFavourites.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
jest.mock('../../modules/microservice_socket', () => require('../mocks/modules/microservice_socket.mock'));

describe('List.service', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    describe('canManageList', () => {
        it('returns true when user is owner', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const list = { owner_id: userId } as any;
            const user = { _id: userId } as any;
            const result = await listService.canManageList(list, user);
            expect(result).toBe(true);
        });

        it('returns false when user is not owner', async () => {
            const list = { owner_id: '507f1f77bcf86cd799439011' } as any;
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const result = await listService.canManageList(list, user);
            expect(result).toBe(false);
        });

        it('returns false when owner_id is different type but same value', async () => {
            const list = { owner_id: new mongoose.Types.ObjectId('507f1f77bcf86cd799439011') } as any;
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            const result = await listService.canManageList(list, user);
            expect(result).toBe(true);
        });
    });

    describe('canReadList', () => {
        it('returns true when user is owner', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const list = { owner_id: userId } as any;
            const user = { _id: userId } as any;
            const result = await listService.canReadList(list, user);
            expect(result).toBe(true);
        });

        it('returns true when user has ACL entry', async () => {
            const userId = '507f1f77bcf86cd799439012';
            const list = { owner_id: '507f1f77bcf86cd799439011', _id: '507f1f77bcf86cd799439020' } as any;
            const user = { _id: userId } as any;
            const mockLean = jest.fn().mockResolvedValue({ list_id: list._id, user_id: userId } as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            const result = await listService.canReadList(list, user);
            expect(result).toBe(true);
            expect(ListACL.findOne).toHaveBeenCalledWith({ list_id: list._id, user_id: userId });
        });

        it('returns true when list is shared with organization and user has organization', async () => {
            const userId = '507f1f77bcf86cd799439012';
            const orgId = '507f1f77bcf86cd799439030';
            const list = { owner_id: '507f1f77bcf86cd799439011', shared_with_organization: true, _id: '507f1f77bcf86cd799439020' } as any;
            const user = { _id: userId, organization: { _id: orgId } } as any;
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            const result = await listService.canReadList(list, user);
            expect(result).toBe(true);
        });

        it('returns false when list is not shared with organization', async () => {
            const userId = '507f1f77bcf86cd799439012';
            const list = { owner_id: '507f1f77bcf86cd799439011', shared_with_organization: false, _id: '507f1f77bcf86cd799439020' } as any;
            const user = { _id: userId, organization: { _id: '507f1f77bcf86cd799439030' } } as any;
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            const result = await listService.canReadList(list, user);
            expect(result).toBe(false);
        });

        it('returns false when user has no organization', async () => {
            const userId = '507f1f77bcf86cd799439012';
            const list = { owner_id: '507f1f77bcf86cd799439011', shared_with_organization: true, _id: '507f1f77bcf86cd799439020' } as any;
            const user = { _id: userId } as any;
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            const result = await listService.canReadList(list, user);
            expect(result).toBe(false);
        });

        it('returns false when user organization has no _id', async () => {
            const userId = '507f1f77bcf86cd799439012';
            const list = { owner_id: '507f1f77bcf86cd799439011', shared_with_organization: true, _id: '507f1f77bcf86cd799439020' } as any;
            const user = { _id: userId, organization: {} } as any;
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            const result = await listService.canReadList(list, user);
            expect(result).toBe(false);
        });
    });

    describe('createList', () => {
        it('creates list with sharedWithOrganization true', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            const name = 'Test List';
            const mockList = { _id: '507f1f77bcf86cd799439020', owner_id: userId, name, shared_with_organization: true };
            (List.create as jest.Mock).mockResolvedValue({
                toObject: jest.fn().mockReturnValue(mockList)
            } as never);
            const result = await listService.createList({ name, sharedWithOrganization: true, user });
            expect(result).toEqual(mockList);
            expect(List.create).toHaveBeenCalledWith({ owner_id: userId, name, shared_with_organization: true });
        });

        it('creates list with sharedWithOrganization false', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            const name = 'Test List';
            const mockList = { _id: '507f1f77bcf86cd799439020', owner_id: userId, name, shared_with_organization: false };
            (List.create as jest.Mock).mockResolvedValue({
                toObject: jest.fn().mockReturnValue(mockList)
            } as never);
            const result = await listService.createList({ name, sharedWithOrganization: false, user });
            expect(result).toEqual(mockList);
            expect(List.create).toHaveBeenCalledWith({ owner_id: userId, name, shared_with_organization: false });
        });

        it('creates list with sharedWithOrganization undefined defaults to false', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            const name = 'Test List';
            const mockList = { _id: '507f1f77bcf86cd799439020', owner_id: userId, name, shared_with_organization: false };
            (List.create as jest.Mock).mockResolvedValue({
                toObject: jest.fn().mockReturnValue(mockList)
            } as never);
            const result = await listService.createList({ name, user });
            expect(result).toEqual(mockList);
            expect(List.create).toHaveBeenCalledWith({ owner_id: userId, name, shared_with_organization: false });
        });
    });

    describe('renameList', () => {
        it('renames list successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const newName = 'Renamed List';
            const user = { _id: userId } as any;
            const mockList = {
                _id: listId,
                owner_id: userId,
                name: newName,
                updated_at: new Date(),
                save: jest.fn().mockResolvedValue(true as never),
                toObject: jest.fn().mockReturnValue({ _id: listId, owner_id: userId, name: newName })
            };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            const result = await listService.renameList({ listId, name: newName, user });
            expect(result.name).toBe(newName);
            expect(mockList.save).toHaveBeenCalled();
            expect(mockList.updated_at).toBeInstanceOf(Date);
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.renameList({ listId, name: 'New Name', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 403 when user cannot manage list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.renameList({ listId, name: 'New Name', user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });
    });

    describe('toggleOrgShare', () => {
        it('toggles organization sharing to true', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = {
                _id: listId,
                owner_id: userId,
                shared_with_organization: false,
                updated_at: new Date(),
                save: jest.fn().mockResolvedValue(true as never),
                toObject: jest.fn().mockReturnValue({ _id: listId, owner_id: userId, shared_with_organization: true })
            };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            const result = await listService.toggleOrgShare({ listId, sharedWithOrganization: true, user });
            expect(result.shared_with_organization).toBe(true);
            expect(mockList.shared_with_organization).toBe(true);
            expect(mockList.save).toHaveBeenCalled();
        });

        it('toggles organization sharing to false', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = {
                _id: listId,
                owner_id: userId,
                shared_with_organization: true,
                updated_at: new Date(),
                save: jest.fn().mockResolvedValue(true as never),
                toObject: jest.fn().mockReturnValue({ _id: listId, owner_id: userId, shared_with_organization: false })
            };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            const result = await listService.toggleOrgShare({ listId, sharedWithOrganization: false, user });
            expect(result.shared_with_organization).toBe(false);
            expect(mockList.shared_with_organization).toBe(false);
            expect(mockList.save).toHaveBeenCalled();
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.toggleOrgShare({ listId, sharedWithOrganization: true, user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 403 when user cannot manage list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.toggleOrgShare({ listId, sharedWithOrganization: true, user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });
    });

    describe('deleteList', () => {
        it('deletes list successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = {
                _id: listId,
                owner_id: userId,
                is_deleted: false,
                updated_at: new Date(),
                save: jest.fn().mockResolvedValue(true as never)
            };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            const result = await listService.deleteList({ listId, user });
            expect(result).toEqual({ deleted: true });
            expect(mockList.is_deleted).toBe(true);
            expect(mockList.save).toHaveBeenCalled();
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.deleteList({ listId, user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 403 when user cannot manage list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.deleteList({ listId, user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });
    });

    describe('shareWithUser', () => {
        it('shares list with user successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const targetUserId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (ListACL.findOne as jest.Mock).mockResolvedValue(null as never);
            (ListACL.create as jest.Mock).mockResolvedValue({} as never);
            const result = await listService.shareWithUser({ listId, targetUserId, user });
            expect(result).toEqual({ ok: true });
            expect(ListACL.findOne).toHaveBeenCalled();
            expect(ListACL.create).toHaveBeenCalled();
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.shareWithUser({ listId, targetUserId: '507f1f77bcf86cd799439012', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 404 when list is deleted', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: true } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.shareWithUser({ listId, targetUserId: '507f1f77bcf86cd799439012', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 400 when trying to share with self', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const ownerIdObj = { toString: () => userId };
            const mockList = { _id: listId, owner_id: ownerIdObj, is_deleted: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.shareWithUser({ listId, targetUserId: userId, user })).rejects.toEqual({ status: 400, message: 'Cannot share list with yourself' });
        });

        it('throws 403 when user cannot manage list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', is_deleted: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.shareWithUser({ listId, targetUserId: '507f1f77bcf86cd799439013', user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });

        it('throws 409 when list is already shared with user', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const targetUserId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            const existingShare = { list_id: listId, user_id: targetUserId };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (ListACL.findOne as jest.Mock).mockResolvedValue(existingShare as never);
            await expect(listService.shareWithUser({ listId, targetUserId, user })).rejects.toEqual({ status: 409, message: 'List is already shared with this user' });
        });
    });

    describe('shareWithEmail', () => {
        it('shares list with user by email successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const targetUserId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const email = '<EMAIL>';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            const existingUser = { _id: targetUserId, email: email.toLowerCase() };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (User.findOne as jest.Mock).mockResolvedValue(existingUser as never);
            (ListACL.findOne as jest.Mock).mockResolvedValue(null as never);
            (ListACL.create as jest.Mock).mockResolvedValue({} as never);
            const result = await listService.shareWithEmail({ listId, email, user });
            expect(result).toEqual({ ok: true });
            expect(User.findOne).toHaveBeenCalledWith({ email: email.trim().toLowerCase(), is_deleted: false });
            expect(ListACL.findOne).toHaveBeenCalled();
            expect(ListACL.create).toHaveBeenCalled();
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.shareWithEmail({ listId, email: '<EMAIL>', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 404 when list is deleted', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: true } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.shareWithEmail({ listId, email: '<EMAIL>', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 403 when user cannot manage list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', is_deleted: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.shareWithEmail({ listId, email: '<EMAIL>', user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });

        it('throws 404 when user not found with email', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const email = '<EMAIL>';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (User.findOne as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.shareWithEmail({ listId, email, user })).rejects.toEqual({ status: 404, message: 'User not found with this email address' });
        });

        it('throws 400 when trying to share with self by email', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const email = '<EMAIL>';
            const user = { _id: userId } as any;
            const ownerIdObj = { toString: () => userId };
            const mockList = { _id: listId, owner_id: ownerIdObj, is_deleted: false } as any;
            const existingUserIdObj = { toString: () => userId };
            const existingUser = { _id: existingUserIdObj, email: email.toLowerCase() };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (User.findOne as jest.Mock).mockResolvedValue(existingUser as never);
            await expect(listService.shareWithEmail({ listId, email, user })).rejects.toEqual({ status: 400, message: 'Cannot share list with yourself' });
        });

        it('handles email with whitespace', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const targetUserId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const email = '  <EMAIL>  ';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            const existingUser = { _id: targetUserId, email: '<EMAIL>' };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (User.findOne as jest.Mock).mockResolvedValue(existingUser as never);
            (ListACL.findOne as jest.Mock).mockResolvedValue(null as never);
            (ListACL.create as jest.Mock).mockResolvedValue({} as never);
            await listService.shareWithEmail({ listId, email, user });
            expect(User.findOne).toHaveBeenCalledWith({ email: '<EMAIL>', is_deleted: false });
        });

        it('throws 409 when list is already shared with user by email', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const targetUserId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const email = '<EMAIL>';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            const existingUser = { _id: targetUserId, email: email.toLowerCase() };
            const existingShare = { list_id: listId, user_id: targetUserId };
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (User.findOne as jest.Mock).mockResolvedValue(existingUser as never);
            (ListACL.findOne as jest.Mock).mockResolvedValue(existingShare as never);
            await expect(listService.shareWithEmail({ listId, email, user })).rejects.toEqual({ status: 409, message: 'List is already shared with this user' });
        });
    });

    describe('unshareWithUser', () => {
        it('unshares list with user successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const targetUserId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const mockList = { _id: listId, owner_id: userId, is_deleted: false, shared_with_organization: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (ListACL.deleteOne as jest.Mock).mockResolvedValue({} as never);
            const result = await listService.unshareWithUser({ listId, targetUserId });
            expect(result).toEqual({ ok: true });
            expect(ListACL.deleteOne).toHaveBeenCalled();
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.unshareWithUser({ listId, targetUserId: '507f1f77bcf86cd799439012' })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 404 when list is deleted', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const mockList = { _id: listId, owner_id: userId, is_deleted: true } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.unshareWithUser({ listId, targetUserId: '507f1f77bcf86cd799439012' })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 400 when trying to unshare with self', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const ownerIdObj = { toString: () => userId };
            const mockList = { _id: listId, owner_id: ownerIdObj, is_deleted: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.unshareWithUser({ listId, targetUserId: userId })).rejects.toEqual({ status: 400, message: 'Cannot unshare list with yourself' });
        });

        it('throws 404 when list is not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', is_deleted: true } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.unshareWithUser({ listId, targetUserId: '507f1f77bcf86cd799439013' })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        // it('throws 403 when trying to unshare from organization-shared list', async () => {
        //     const userId = '507f1f77bcf86cd799439011';
        //     const targetUserId = '507f1f77bcf86cd799439012';
        //     const listId = '507f1f77bcf86cd799439020';
        //     const mockList = { _id: listId, owner_id: userId, is_deleted: false, shared_with_organization: true } as any;
        //     (List.findById as jest.Mock).mockResolvedValue(mockList as never);
        //     await expect(listService.unshareWithUser({ listId, targetUserId })).rejects.toEqual({ status: 403, message: 'Cannot remove users from organization-shared lists' });
        // });
    });

    describe('addArtifact', () => {
        it('adds artifact to list successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const artifactId = '507f1f77bcf86cd799439030';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, name: 'Test List' } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            (ListArtifact.updateOne as jest.Mock).mockResolvedValue({} as never);
            const result = await listService.addArtifact({ listId, artifactId, user });
            expect(result).toEqual({ ok: true, listName: 'Test List' });
            expect(ListArtifact.updateOne).toHaveBeenCalled();
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.addArtifact({ listId, artifactId: '507f1f77bcf86cd799439030', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 404 when list is deleted', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', is_deleted: true } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.addArtifact({ listId, artifactId: '507f1f77bcf86cd799439030', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 403 when user cannot read list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', shared_with_organization: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            await expect(listService.addArtifact({ listId, artifactId: '507f1f77bcf86cd799439030', user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });
    });

    describe('removeArtifact', () => {
        it('removes artifact from list successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const artifactId = '507f1f77bcf86cd799439030';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, name: 'Test List' } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            (ListArtifact.deleteOne as jest.Mock).mockResolvedValue({} as never);
            const result = await listService.removeArtifact({ listId, artifactId, user });
            expect(result).toEqual({ ok: true, listName: 'Test List' });
            expect(ListArtifact.deleteOne).toHaveBeenCalled();
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.removeArtifact({ listId, artifactId: '507f1f77bcf86cd799439030', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 404 when list is deleted', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', is_deleted: true } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.removeArtifact({ listId, artifactId: '507f1f77bcf86cd799439030', user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 403 when user cannot read list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', shared_with_organization: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            await expect(listService.removeArtifact({ listId, artifactId: '507f1f77bcf86cd799439030', user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });
    });

    describe('getVisibleListsForUser', () => {
        it('returns owned lists', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            const mockOwnedList = { _id: '507f1f77bcf86cd799439020', owner_id: userId, name: 'Owned List', created_at: new Date('2024-01-01') };
            const mockLean1 = jest.fn().mockResolvedValue([mockOwnedList] as never);
            const mockLean2 = jest.fn().mockResolvedValue([] as never);
            (List.find as jest.Mock).mockReturnValueOnce({ lean: mockLean1 });
            (ListACL.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: mockLean2 }) });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: userId, name: 'Test User' }] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(1);
            expect(result[0].owner_name).toBe('Test User');
        });

        it('returns shared lists via ACL', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const ownerId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockSharedList = { _id: listId, owner_id: ownerId, name: 'Shared List', created_at: new Date('2024-01-02') };
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ list_id: listId }] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([mockSharedList] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: ownerId, name: 'Owner User' }] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(1);
            expect(result[0].owner_name).toBe('Owner User');
        });

        it('returns empty array when no shared lists via ACL', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(0);
        });

        it('returns organization shared lists when user has organization', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const orgId = '507f1f77bcf86cd799439030';
            const orgOwnerId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId, organization: { _id: orgId } } as any;
            const mockOrgList = { _id: listId, owner_id: orgOwnerId, name: 'Org List', shared_with_organization: true, created_at: new Date('2024-01-03') };
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (User.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: orgOwnerId }] as never) }) });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([mockOrgList] as never) });
            (User.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: orgOwnerId, name: 'Org Owner' }] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(1);
            expect(result[0].owner_name).toBe('Org Owner');
        });

        it('does not return organization shared lists when user has no organization', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(0);
        });

        it('does not return organization shared lists when orgOwners is empty', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const orgId = '507f1f77bcf86cd799439030';
            const user = { _id: userId, organization: { _id: orgId } } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            (User.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (User.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(0);
        });

        it('sorts lists by created_at descending', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            const list1 = { _id: '507f1f77bcf86cd799439020', owner_id: userId, name: 'List 1', created_at: new Date('2024-01-01') };
            const list2 = { _id: '507f1f77bcf86cd799439021', owner_id: userId, name: 'List 2', created_at: new Date('2024-01-02') };
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([list1, list2] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: userId, name: 'Test User' }] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(2);
            expect(result[0].created_at).toEqual(list2.created_at);
            expect(result[1].created_at).toEqual(list1.created_at);
        });

        it('enriches lists with owner name, shared users count, and artifact count', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, name: 'Test List', created_at: new Date('2024-01-01') };
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([mockList] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: userId, name: 'Test User' }] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([{ _id: listId, count: 3 }] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([{ _id: listId, count: 5 }] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result[0].owner_name).toBe('Test User');
            expect(result[0].shared_users_count).toBe(3);
            expect(result[0].artifact_count).toBe(5);
        });

        it('uses default values when owner name, shared users count, or artifact count are missing', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, name: 'Test List', created_at: new Date('2024-01-01') };
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([mockList] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result[0].owner_name).toBe('Unknown');
            expect(result[0].shared_users_count).toBe(0);
            expect(result[0].artifact_count).toBe(0);
        });

        it('deduplicates lists when same list appears in multiple sources', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, name: 'Test List', created_at: new Date('2024-01-01') };
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([mockList] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ list_id: listId }] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([mockList] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: userId, name: 'Test User' }] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(1);
        });

        it('handles user with organization but no _id', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId, organization: {} } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ lean: jest.fn().mockResolvedValue([] as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListACL.aggregate as jest.Mock).mockResolvedValue([] as never);
            (ListArtifact.aggregate as jest.Mock).mockResolvedValue([] as never);
            const result = await listService.getVisibleListsForUser(user);
            expect(result).toHaveLength(0);
        });
    });

    describe('getUserListsArtifacts', () => {
        it('returns empty object when user has no lists', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            const result = await listService.getUserListsArtifacts({ user });
            expect(result).toEqual({});
        });

        it('returns artifacts for owned lists', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const artifactId = '507f1f77bcf86cd799439030';
            const user = { _id: userId } as any;
            const mockList = { _id: listId };
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([mockList] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListArtifact.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ list_id: listId, artifact_id: artifactId }] as never) }) });
            const result = await listService.getUserListsArtifacts({ user });
            expect(result[String(listId)]).toContain(String(artifactId));
        });

        it('returns artifacts for shared lists via ACL', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const artifactId = '507f1f77bcf86cd799439030';
            const user = { _id: userId } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ list_id: listId }] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: listId }] as never) }) });
            (ListArtifact.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ list_id: listId, artifact_id: artifactId }] as never) }) });
            const result = await listService.getUserListsArtifacts({ user });
            expect(result[String(listId)]).toContain(String(artifactId));
        });

        it('returns artifacts for organization shared lists', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const orgId = '507f1f77bcf86cd799439030';
            const orgOwnerId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const artifactId = '507f1f77bcf86cd799439040';
            const user = { _id: userId, organization: { _id: orgId } } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (User.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: orgOwnerId }] as never) }) });
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ _id: listId }] as never) }) });
            (ListArtifact.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ list_id: listId, artifact_id: artifactId }] as never) }) });
            const result = await listService.getUserListsArtifacts({ user });
            expect(result[String(listId)]).toContain(String(artifactId));
        });

        it('deduplicates artifacts for same list', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const artifactId = '507f1f77bcf86cd799439030';
            const user = { _id: userId } as any;
            const mockList = { _id: listId };
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([mockList] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListArtifact.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnValue({
                    lean: jest.fn().mockResolvedValue([
                        { list_id: listId, artifact_id: artifactId },
                        { list_id: listId, artifact_id: artifactId }
                    ] as never)
                })
            });
            const result = await listService.getUserListsArtifacts({ user });
            expect(result[String(listId)]).toHaveLength(1);
            expect(result[String(listId)]).toContain(String(artifactId));
        });

        it('returns multiple artifacts for same list', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const artifactId1 = '507f1f77bcf86cd799439030';
            const artifactId2 = '507f1f77bcf86cd799439031';
            const user = { _id: userId } as any;
            const mockList = { _id: listId };
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([mockList] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListArtifact.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnValue({
                    lean: jest.fn().mockResolvedValue([
                        { list_id: listId, artifact_id: artifactId1 },
                        { list_id: listId, artifact_id: artifactId2 }
                    ] as never)
                })
            });
            const result = await listService.getUserListsArtifacts({ user });
            expect(result[String(listId)]).toHaveLength(2);
            expect(result[String(listId)]).toContain(String(artifactId1));
            expect(result[String(listId)]).toContain(String(artifactId2));
        });

        it('deduplicates list IDs when same list appears in multiple sources', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const artifactId = '507f1f77bcf86cd799439030';
            const user = { _id: userId } as any;
            const mockList = { _id: listId };
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([mockList] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ list_id: listId }] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([mockList] as never) }) });
            (ListArtifact.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([{ list_id: listId, artifact_id: artifactId }] as never) }) });
            const result = await listService.getUserListsArtifacts({ user });
            expect(Object.keys(result)).toHaveLength(1);
            expect(result[String(listId)]).toContain(String(artifactId));
        });

        it('handles user with organization but no _id', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId, organization: {} } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            const result = await listService.getUserListsArtifacts({ user });
            expect(result).toEqual({});
        });

        it('does not return organization shared lists when orgOwners is empty', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const orgId = '507f1f77bcf86cd799439030';
            const user = { _id: userId, organization: { _id: orgId } } as any;
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (User.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            const result = await listService.getUserListsArtifacts({ user });
            expect(result).toEqual({});
        });

        it('returns artifacts for multiple lists', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId1 = '507f1f77bcf86cd799439020';
            const listId2 = '507f1f77bcf86cd799439021';
            const artifactId1 = '507f1f77bcf86cd799439030';
            const artifactId2 = '507f1f77bcf86cd799439031';
            const user = { _id: userId } as any;
            const mockList1 = { _id: listId1 };
            const mockList2 = { _id: listId2 };
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([mockList1, mockList2] as never) }) });
            const mockSelect = jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            (ListACL.find as jest.Mock).mockReturnValue({ select: mockSelect });
            (List.find as jest.Mock).mockReturnValueOnce({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) }) });
            (ListArtifact.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnValue({
                    lean: jest.fn().mockResolvedValue([
                        { list_id: listId1, artifact_id: artifactId1 },
                        { list_id: listId2, artifact_id: artifactId2 }
                    ] as never)
                })
            });
            const result = await listService.getUserListsArtifacts({ user });
            expect(Object.keys(result)).toHaveLength(2);
            expect(result[String(listId1)]).toContain(String(artifactId1));
            expect(result[String(listId2)]).toContain(String(artifactId2));
        });
    });

    describe('getSharedUsers', () => {
        it('returns shared users for list', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const sharedUserId1 = '507f1f77bcf86cd799439012';
            const sharedUserId2 = '507f1f77bcf86cd799439013';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            const mockACLs = [
                { list_id: listId, user_id: sharedUserId1 },
                { list_id: listId, user_id: sharedUserId2 }
            ];
            const mockUsers = [
                { _id: sharedUserId1, name: 'User 1', email: '<EMAIL>' },
                { _id: sharedUserId2, name: 'User 2', email: '<EMAIL>' }
            ];
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (ListACL.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(mockACLs as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue(mockUsers as never) }) });
            const result = await listService.getSharedUsers({ listId, user });
            expect(result).toHaveLength(2);
            expect(result[0]).toEqual({ _id: sharedUserId1, name: 'User 1', email: '<EMAIL>' });
            expect(result[1]).toEqual({ _id: sharedUserId2, name: 'User 2', email: '<EMAIL>' });
        });

        it('returns empty array when no shared users', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (ListACL.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue([] as never) });
            const result = await listService.getSharedUsers({ listId, user });
            expect(result).toEqual([]);
        });

        it('filters out deleted users', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const sharedUserId1 = '507f1f77bcf86cd799439012';
            const sharedUserId2 = '507f1f77bcf86cd799439013';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            const mockACLs = [
                { list_id: listId, user_id: sharedUserId1 },
                { list_id: listId, user_id: sharedUserId2 }
            ];
            const mockUsers = [
                { _id: sharedUserId1, name: 'User 1', email: '<EMAIL>' }
            ];
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            (ListACL.find as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(mockACLs as never) });
            (User.find as jest.Mock).mockReturnValue({ select: jest.fn().mockReturnValue({ lean: jest.fn().mockResolvedValue(mockUsers as never) }) });
            const result = await listService.getSharedUsers({ listId, user });
            expect(result).toHaveLength(1);
            expect(result[0]._id).toBe(sharedUserId1);
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011' } as any;
            (List.findById as jest.Mock).mockResolvedValue(null as never);
            await expect(listService.getSharedUsers({ listId, user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 404 when list is deleted', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: true } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.getSharedUsers({ listId, user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 403 when user cannot manage list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', is_deleted: false } as any;
            (List.findById as jest.Mock).mockResolvedValue(mockList as never);
            await expect(listService.getSharedUsers({ listId, user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });
    });

    describe('downloadList', () => {
        const microservice_socket = require('../../modules/microservice_socket').default;

        beforeEach(() => {
            jest.clearAllMocks();
            (canAccessVessel as jest.Mock).mockReturnValue(true);
        });

        it('downloads list successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId, email: '<EMAIL>' } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            (List.findById as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(mockList as never) });
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            const mockListArtifacts = [{ artifact_id: '507f1f77bcf86cd799439030' }];
            (ListArtifact.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnThis(),
                lean: jest.fn().mockResolvedValue(mockListArtifacts as never)
            });
            (Vessel.find as jest.Mock).mockReturnValue({
                lean: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439040', is_active: true, region_group_id: '507f1f77bcf86cd799439050' }] as never)
            });
            (db.qmai.collection as jest.Mock).mockReturnValue({
                find: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        toArray: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439030', onboard_vessel_id: '507f1f77bcf86cd799439040' }])
                    })
                })
            });
            const result = await listService.downloadList({ listId, user });
            expect(result.message).toBe('Download started! You will receive an email with the download link once it\'s ready.');
            expect(microservice_socket.emit).toHaveBeenCalledWith('lists/downloadList', JSON.stringify({ type: 'list', listId, userId }));
        });

        it('throws 404 when list not found', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439011', email: '<EMAIL>' } as any;
            (List.findById as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(null as never) });
            await expect(listService.downloadList({ listId, user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 404 when list is deleted', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId, email: '<EMAIL>' } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: true } as any;
            (List.findById as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(mockList as never) });
            await expect(listService.downloadList({ listId, user })).rejects.toEqual({ status: 404, message: 'List not found' });
        });

        it('throws 403 when user cannot read list', async () => {
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: '507f1f77bcf86cd799439012', email: '<EMAIL>' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', shared_with_organization: false, is_deleted: false } as any;
            (List.findById as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(mockList as never) });
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            await expect(listService.downloadList({ listId, user })).rejects.toEqual({ status: 403, message: 'Forbidden' });
        });

        it('throws 400 when user has no email', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            (List.findById as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(mockList as never) });
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            await expect(listService.downloadList({ listId, user })).rejects.toEqual({ status: 400, message: 'User email not found' });
        });

        it('throws 400 when list has no artifacts', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId, email: '<EMAIL>' } as any;
            const mockList = { _id: listId, owner_id: userId, is_deleted: false } as any;
            (List.findById as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(mockList as never) });
            const mockLean = jest.fn().mockResolvedValue(null as never);
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            (ListArtifact.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnThis(),
                lean: jest.fn().mockResolvedValue([] as never)
            });
            await expect(listService.downloadList({ listId, user })).rejects.toEqual({ status: 400, message: 'List is empty' });
        });

        it('allows download when user has ACL access', async () => {
            const userId = '507f1f77bcf86cd799439012';
            const listId = '507f1f77bcf86cd799439020';
            const user = { _id: userId, email: '<EMAIL>' } as any;
            const mockList = { _id: listId, owner_id: '507f1f77bcf86cd799439011', is_deleted: false } as any;
            (List.findById as jest.Mock).mockReturnValue({ lean: jest.fn().mockResolvedValue(mockList as never) });
            const mockLean = jest.fn().mockResolvedValue({ list_id: listId, user_id: userId } as never);
            (ListArtifact.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnThis(),
                lean: jest.fn().mockResolvedValue([{ artifact_id: '507f1f77bcf86cd799439030' }] as never)
            });
            (Vessel.find as jest.Mock).mockReturnValue({
                lean: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439040', is_active: true, region_group_id: '507f1f77bcf86cd799439050' }] as never)
            });
            (db.qmai.collection as jest.Mock).mockReturnValue({
                find: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        toArray: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439030', onboard_vessel_id: '507f1f77bcf86cd799439040' }])
                    })
                })
            });
            (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });
            (ListArtifact.countDocuments as jest.Mock).mockResolvedValue(3 as never);
            const result = await listService.downloadList({ listId, user });
            expect(result.message).toBe('Download started! You will receive an email with the download link once it\'s ready.');
        });
    });

    describe('downloadFavorites', () => {
        const microservice_socket = require('../../modules/microservice_socket').default;

        beforeEach(() => {
            jest.clearAllMocks();
            (canAccessVessel as jest.Mock).mockReturnValue(true);
        });

        it('downloads favorites successfully', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId, email: '<EMAIL>' } as any;
            (ArtifactFavourites.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnThis(),
                lean: jest.fn().mockResolvedValue([{ artifact_id: '507f1f77bcf86cd799439030' }] as never)
            });
            (Vessel.find as jest.Mock).mockReturnValue({
                lean: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439040', is_active: true, region_group_id: '507f1f77bcf86cd799439050' }] as never)
            });
            (db.qmai.collection as jest.Mock).mockReturnValue({
                find: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        toArray: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439030', onboard_vessel_id: '507f1f77bcf86cd799439040' }])
                    })
                })
            });
            const result = await listService.downloadFavorites({ user });
            expect(result.message).toBe('Download started! You will receive an email with the download link once it\'s ready.');
            expect(microservice_socket.emit).toHaveBeenCalledWith('lists/downloadList', JSON.stringify({ type: 'favorite', userId }));
        });

        it('throws 400 when user has no email', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId } as any;
            await expect(listService.downloadFavorites({ user })).rejects.toEqual({ status: 400, message: 'User email not found' });
        });

        it('throws 400 when favorites list is empty', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId, email: '<EMAIL>' } as any;
            (ArtifactFavourites.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnThis(),
                lean: jest.fn().mockResolvedValue([] as never)
            });
            await expect(listService.downloadFavorites({ user })).rejects.toEqual({ status: 400, message: 'Favorites list is empty' });
        });

        it('emits correct socket event with favorite type', async () => {
            const userId = '507f1f77bcf86cd799439011';
            const user = { _id: userId, email: '<EMAIL>' } as any;
            (ArtifactFavourites.find as jest.Mock).mockReturnValue({
                select: jest.fn().mockReturnThis(),
                lean: jest.fn().mockResolvedValue([{ artifact_id: '507f1f77bcf86cd799439030' }] as never)
            });
            (Vessel.find as jest.Mock).mockReturnValue({
                lean: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439040', is_active: true, region_group_id: '507f1f77bcf86cd799439050' }] as never)
            });
            (db.qmai.collection as jest.Mock).mockReturnValue({
                find: jest.fn().mockReturnValue({
                    project: jest.fn().mockReturnValue({
                        toArray: jest.fn().mockResolvedValue([{ _id: '507f1f77bcf86cd799439030', onboard_vessel_id: '507f1f77bcf86cd799439040' }])
                    })
                })
            });
            await listService.downloadFavorites({ user });
            expect(microservice_socket.emit).toHaveBeenCalledTimes(1);
            const callArgs = (microservice_socket.emit as jest.Mock).mock.calls[0];
            expect(callArgs[0]).toBe('lists/downloadList');
            const parsedData = JSON.parse(callArgs[1] as string);
            expect(parsedData.type).toBe('favorite');
            expect(parsedData.userId).toBe(userId);
            expect(parsedData.listId).toBeUndefined();
        });
    });

    describe('copyArtifactsFromList', () => {
        const userId = '507f1f77bcf86cd799439011';
        const user = { _id: userId } as any;
        const sourceListId = '507f1f77bcf86cd799439020';
        const targetListId = '507f1f77bcf86cd799439021';
        const artifactId1 = new mongoose.Types.ObjectId('507f1f77bcf86cd799439030');
        const artifactId2 = new mongoose.Types.ObjectId('507f1f77bcf86cd799439031');
        const artifactId3 = new mongoose.Types.ObjectId('507f1f77bcf86cd799439032');

        beforeEach(() => {
            jest.clearAllMocks();
        });

        describe('Copy from list to list', () => {
            it('copies artifacts successfully when target list has no existing artifacts', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                const targetList = { _id: targetListId, name: 'Target List', owner_id: userId, is_deleted: false };

                (List.findById as jest.Mock)
                    .mockResolvedValueOnce(sourceList as never)
                    .mockResolvedValueOnce(targetList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const sourceArtifacts = [
                    { artifact_id: String(artifactId1) },
                    { artifact_id: String(artifactId2) },
                    { artifact_id: String(artifactId3) },
                ];
                const mockSourceLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockSourceLean }),
                });

                const existingArtifacts: any[] = [];
                const mockTargetLean = jest.fn().mockResolvedValue(existingArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockTargetLean }),
                });

                (ListArtifact.bulkWrite as jest.Mock).mockResolvedValue({} as never);

                const result = await listService.copyArtifactsFromList({
                    targetType: 'list',
                    targetListId,
                    sourceListId,
                    sourceType: 'list',
                    user,
                });

                expect(result.message).toBe('Source List has been copied to Target List');
                expect(List.findById).toHaveBeenCalledWith(sourceListId);
                expect(List.findById).toHaveBeenCalledWith(targetListId);
                expect(ListArtifact.bulkWrite).toHaveBeenCalled();
            });

            it('returns message when all artifacts already exist in target list', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                const targetList = { _id: targetListId, name: 'Target List', owner_id: userId, is_deleted: false };

                (List.findById as jest.Mock)
                    .mockResolvedValueOnce(sourceList as never)
                    .mockResolvedValueOnce(targetList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const sourceArtifacts = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockSourceLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockSourceLean }),
                });

                const existingArtifacts = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockTargetLean = jest.fn().mockResolvedValue(existingArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockTargetLean }),
                });

                const result = await listService.copyArtifactsFromList({
                    targetType: 'list',
                    targetListId,
                    sourceListId,
                    sourceType: 'list',
                    user,
                });

                expect(result.message).toBe('All artifacts from Source List already exist in Target List');
                expect(ListArtifact.bulkWrite).not.toHaveBeenCalled();
            });

            it('copies only new artifacts when some already exist', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                const targetList = { _id: targetListId, name: 'Target List', owner_id: userId, is_deleted: false };

                (List.findById as jest.Mock)
                    .mockResolvedValueOnce(sourceList as never)
                    .mockResolvedValueOnce(targetList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                // Use string IDs to match how the service processes them
                const sourceArtifacts = [
                    { artifact_id: String(artifactId1) },
                    { artifact_id: String(artifactId2) },
                    { artifact_id: String(artifactId3) },
                ];
                const mockSourceLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                const mockSourceSelect = jest.fn().mockReturnValue({ lean: mockSourceLean });

                // Existing artifact should have the same ID format
                const existingArtifacts = [{ artifact_id: String(artifactId1) }];
                const mockTargetLean = jest.fn().mockResolvedValue(existingArtifacts as never);
                const mockTargetSelect = jest.fn().mockReturnValue({ lean: mockTargetLean });

                (ListArtifact.find as jest.Mock)
                    .mockReturnValueOnce({ select: mockSourceSelect })
                    .mockReturnValueOnce({ select: mockTargetSelect });

                (ListArtifact.bulkWrite as jest.Mock).mockResolvedValue({} as never);

                const result = await listService.copyArtifactsFromList({
                    targetType: 'list',
                    targetListId,
                    sourceListId,
                    sourceType: 'list',
                    user,
                });

                expect(result.message).toBe('Source List has been copied to Target List');
                expect(ListArtifact.bulkWrite).toHaveBeenCalled();
                const bulkWriteCall = (ListArtifact.bulkWrite as jest.Mock).mock.calls[0][0];
                expect(bulkWriteCall).toHaveLength(2); // Only 2 new artifacts (artifactId2 and artifactId3)
            });

            it('throws 404 when source list not found', async () => {
                (List.findById as jest.Mock).mockResolvedValueOnce(null as never);

                await expect(
                    listService.copyArtifactsFromList({
                        targetType: 'list',
                        targetListId,
                        sourceListId,
                        sourceType: 'list',
                        user,
                    })
                ).rejects.toEqual({ status: 404, message: 'Source list not found' });
            });

            it('throws 404 when target list not found', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                (List.findById as jest.Mock)
                    .mockResolvedValueOnce(sourceList as never)
                    .mockResolvedValueOnce(null as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const sourceArtifacts = [{ artifact_id: artifactId1 }];
                const mockSourceLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockSourceLean }),
                });

                await expect(
                    listService.copyArtifactsFromList({
                        targetType: 'list',
                        targetListId,
                        sourceListId,
                        sourceType: 'list',
                        user,
                    })
                ).rejects.toEqual({ status: 404, message: 'Target list not found' });
            });

            it('throws 400 when source list is empty', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                (List.findById as jest.Mock).mockResolvedValueOnce(sourceList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const mockSourceLean = jest.fn().mockResolvedValue([] as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockSourceLean }),
                });

                const result = await listService.copyArtifactsFromList({
                    targetType: 'list',
                    targetListId,
                    sourceListId,
                    sourceType: 'list',
                    user,
                });

                expect(result.message).toBe('Source list is empty');
            });

            it('throws 400 when targetListId is missing for list target', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                (List.findById as jest.Mock).mockResolvedValueOnce(sourceList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const sourceArtifacts = [{ artifact_id: String(artifactId1) }];
                const mockSourceLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockSourceLean }),
                });

                await expect(
                    listService.copyArtifactsFromList({
                        targetType: 'list',
                        sourceListId,
                        sourceType: 'list',
                        user,
                    })
                ).rejects.toEqual({ status: 400, message: 'Target list ID is required when targetType is \'list\'' });
            });

            it('throws 400 when sourceListId is missing for list source', async () => {
                await expect(
                    listService.copyArtifactsFromList({
                        targetType: 'list',
                        targetListId,
                        sourceType: 'list',
                        user,
                    })
                ).rejects.toEqual({ status: 400, message: 'Source list ID is required when sourceType is \'list\'' });
            });
        });

        describe('Copy from favorites to list', () => {
            it('copies favorites to list successfully', async () => {
                const targetList = { _id: targetListId, name: 'Target List', owner_id: userId, is_deleted: false };
                (List.findById as jest.Mock).mockResolvedValueOnce(targetList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const favorites = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockFavoritesLean = jest.fn().mockResolvedValue(favorites as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                });

                const existingArtifacts: any[] = [];
                const mockTargetLean = jest.fn().mockResolvedValue(existingArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockTargetLean }),
                });

                (ListArtifact.bulkWrite as jest.Mock).mockResolvedValue({} as never);

                const result = await listService.copyArtifactsFromList({
                    targetType: 'list',
                    targetListId,
                    sourceType: 'favorites',
                    user,
                });

                expect(result.message).toBe('Favorites has been copied to Target List');
                expect(ArtifactFavourites.find).toHaveBeenCalledWith({ user_id: userId });
                expect(ListArtifact.bulkWrite).toHaveBeenCalled();
            });

            it('returns message when all favorites already exist in target list', async () => {
                const targetList = { _id: targetListId, name: 'Target List', owner_id: userId, is_deleted: false };
                (List.findById as jest.Mock).mockResolvedValueOnce(targetList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const favorites = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockFavoritesLean = jest.fn().mockResolvedValue(favorites as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                });

                const existingArtifacts = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockTargetLean = jest.fn().mockResolvedValue(existingArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockTargetLean }),
                });

                const result = await listService.copyArtifactsFromList({
                    targetType: 'list',
                    targetListId,
                    sourceType: 'favorites',
                    user,
                });

                expect(result.message).toBe('All artifacts from Favorites already exist in Target List');
                expect(ListArtifact.bulkWrite).not.toHaveBeenCalled();
            });
        });

        describe('Copy from list to favorites', () => {
            it('copies list to favorites successfully', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                (List.findById as jest.Mock).mockResolvedValueOnce(sourceList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const sourceArtifacts = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockSourceLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockSourceLean }),
                });

                const existingFavorites: any[] = [];
                const mockFavoritesLean = jest.fn().mockResolvedValue(existingFavorites as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                });

                (ArtifactFavourites.bulkWrite as jest.Mock).mockResolvedValue({} as never);

                const result = await listService.copyArtifactsFromList({
                    targetType: 'favorites',
                    sourceListId,
                    sourceType: 'list',
                    user,
                });

                expect(result.message).toBe('Source List has been copied to Favorites');
                expect(ArtifactFavourites.bulkWrite).toHaveBeenCalled();
            });

            it('returns message when all list artifacts already exist in favorites', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                (List.findById as jest.Mock).mockResolvedValueOnce(sourceList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const sourceArtifacts = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockSourceLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockSourceLean }),
                });

                const existingFavorites = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockFavoritesLean = jest.fn().mockResolvedValue(existingFavorites as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                });

                const result = await listService.copyArtifactsFromList({
                    targetType: 'favorites',
                    sourceListId,
                    sourceType: 'list',
                    user,
                });

                expect(result.message).toBe('All artifacts from Source List already exist in Favorites');
                expect(ArtifactFavourites.bulkWrite).not.toHaveBeenCalled();
            });
        });

        describe('Copy from favorites to favorites', () => {
            it('copies favorites to favorites successfully', async () => {
                const favorites = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockFavoritesLean = jest.fn().mockResolvedValue(favorites as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValue({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                });

                const existingFavorites: any[] = [];
                const mockExistingLean = jest.fn().mockResolvedValue(existingFavorites as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                }).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockExistingLean }),
                });

                (ArtifactFavourites.bulkWrite as jest.Mock).mockResolvedValue({} as never);

                const result = await listService.copyArtifactsFromList({
                    targetType: 'favorites',
                    sourceType: 'favorites',
                    user,
                });

                expect(result.message).toBe('Favorites has been copied to Favorites');
                expect(ArtifactFavourites.bulkWrite).toHaveBeenCalled();
            });

            it('returns message when all favorites already exist', async () => {
                const favorites = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockFavoritesLean = jest.fn().mockResolvedValue(favorites as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValue({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                });

                const existingFavorites = [
                    { artifact_id: artifactId1 },
                    { artifact_id: artifactId2 },
                ];
                const mockExistingLean = jest.fn().mockResolvedValue(existingFavorites as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                }).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockExistingLean }),
                });

                const result = await listService.copyArtifactsFromList({
                    targetType: 'favorites',
                    sourceType: 'favorites',
                    user,
                });

                expect(result.message).toBe('All artifacts from Favorites already exist in Favorites');
                expect(ArtifactFavourites.bulkWrite).not.toHaveBeenCalled();
            });

            it('returns message when favorites source is empty', async () => {
                const mockFavoritesLean = jest.fn().mockResolvedValue([] as never);
                (ArtifactFavourites.find as jest.Mock).mockReturnValue({
                    select: jest.fn().mockReturnValue({ lean: mockFavoritesLean }),
                });

                const result = await listService.copyArtifactsFromList({
                    targetType: 'favorites',
                    sourceType: 'favorites',
                    user,
                });

                expect(result.message).toBe('Source list is empty');
            });
        });

        describe('Access control', () => {
            it('throws 403 when user cannot access source list', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: '507f1f77bcf86cd799439099', is_deleted: false };
                (List.findById as jest.Mock).mockResolvedValueOnce(sourceList as never);

                const mockLean = jest.fn().mockResolvedValue(null as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                await expect(
                    listService.copyArtifactsFromList({
                        targetType: 'list',
                        targetListId,
                        sourceListId,
                        sourceType: 'list',
                        user,
                    })
                ).rejects.toEqual({ status: 403, message: 'Forbidden: Cannot access source list' });
            });

            it('throws 403 when user cannot access target list', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                const targetList = { _id: targetListId, name: 'Target List', owner_id: '507f1f77bcf86cd799439099', is_deleted: false, shared_with_organization: false };

                (List.findById as jest.Mock)
                    .mockResolvedValueOnce(sourceList as never)
                    .mockResolvedValueOnce(targetList as never);

                // Mock canReadList for source list (returns true - user is owner)
                const mockSourceACL = jest.fn().mockResolvedValue(null as never);
                // Mock canReadList for target list (returns false - user is not owner, no ACL, not shared with org)
                const mockTargetACL = jest.fn().mockResolvedValue(null as never);
                (ListACL.findOne as jest.Mock)
                    .mockReturnValueOnce({ lean: mockSourceACL })
                    .mockReturnValueOnce({ lean: mockTargetACL });

                const sourceArtifacts = [{ artifact_id: String(artifactId1) }];
                const mockSourceArtifactsLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                const mockSourceSelect = jest.fn().mockReturnValue({ lean: mockSourceArtifactsLean });
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: mockSourceSelect,
                });

                // Ensure user has no organization to prevent canReadList from returning true
                const testUser = { ...user, organization: undefined };

                await expect(
                    listService.copyArtifactsFromList({
                        targetType: 'list',
                        targetListId,
                        sourceListId,
                        sourceType: 'list',
                        user: testUser,
                    })
                ).rejects.toEqual({ status: 403, message: 'Forbidden: Cannot access target list' });
            });
        });

        describe('Deleted lists', () => {
            it('throws 404 when source list is deleted', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: true };
                (List.findById as jest.Mock).mockResolvedValueOnce(sourceList as never);

                await expect(
                    listService.copyArtifactsFromList({
                        targetType: 'list',
                        targetListId,
                        sourceListId,
                        sourceType: 'list',
                        user,
                    })
                ).rejects.toEqual({ status: 404, message: 'Source list not found' });
            });

            it('throws 404 when target list is deleted', async () => {
                const sourceList = { _id: sourceListId, name: 'Source List', owner_id: userId, is_deleted: false };
                const targetList = { _id: targetListId, name: 'Target List', owner_id: userId, is_deleted: true };

                (List.findById as jest.Mock)
                    .mockResolvedValueOnce(sourceList as never)
                    .mockResolvedValueOnce(targetList as never);

                const mockLean = jest.fn().mockResolvedValue(true as never);
                (ListACL.findOne as jest.Mock).mockReturnValue({ lean: mockLean });

                const sourceArtifacts = [{ artifact_id: artifactId1 }];
                const mockSourceLean = jest.fn().mockResolvedValue(sourceArtifacts as never);
                (ListArtifact.find as jest.Mock).mockReturnValueOnce({
                    select: jest.fn().mockReturnValue({ lean: mockSourceLean }),
                });

                await expect(
                    listService.copyArtifactsFromList({
                        targetType: 'list',
                        targetListId,
                        sourceListId,
                        sourceType: 'list',
                        user,
                    })
                ).rejects.toEqual({ status: 404, message: 'Target list not found' });
            });
        });
    });
});

