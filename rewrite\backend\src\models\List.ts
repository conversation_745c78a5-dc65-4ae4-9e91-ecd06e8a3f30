import mongoose from "mongoose";
import db from "../modules/db";
import ioEmitter from "../modules/ioEmitter";

export interface IList extends mongoose.Document {
    owner_id: mongoose.Types.ObjectId | string;
    name: string;
    shared_with_organization: boolean;
    is_deleted: boolean;
    created_at: Date;
    updated_at: Date;
}

const listSchema = new mongoose.Schema<IList>({
    owner_id: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true, index: true },
    name: { type: String, required: true, unique: true, trim: true }, // globally unique
    shared_with_organization: { type: Boolean, required: true, default: false },
    is_deleted: { type: Boolean, required: true, default: false },
    created_at: { type: Date, required: true, default: () => new Date() },
    updated_at: { type: Date, required: true, default: () => new Date() },
});

// Secondary indexes for common queries
listSchema.index({ owner_id: 1, created_at: -1 });

listSchema.post("save", emitChangedEvent);
listSchema.post("findOneAndUpdate", emitChangedEvent);
listSchema.post("findOneAndDelete", emitChangedEvent);

export function emitChangedEvent(list: IList) {
    if (!list) return;
    ioEmitter.emit("notifyAll", { name: "list/changed", data: list.toObject?.() ?? list });
}

const List = db.qm.model<IList>("list", listSchema);

export default List;
