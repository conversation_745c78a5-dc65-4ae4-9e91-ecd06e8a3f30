import mongoose from "mongoose";
import db from "../modules/db";

export interface IListArtifact extends mongoose.Document {
    list_id: mongoose.Types.ObjectId | string;
    artifact_id: mongoose.Types.ObjectId | string;
    added_by: mongoose.Types.ObjectId | string;
    created_at: Date;
}

const listArtifactSchema = new mongoose.Schema<IListArtifact>({
    list_id: { type: mongoose.Schema.Types.ObjectId, ref: "List", required: true, index: true },
    artifact_id: { type: mongoose.Schema.Types.ObjectId, ref: "Artifact", required: true, index: true },
    added_by: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true },
    created_at: { type: Date, required: true, default: () => new Date() },
});

// Ensure each artifact appears once per list
listArtifactSchema.index({ list_id: 1, artifact_id: 1 }, { unique: true });
listArtifactSchema.index({ list_id: 1, created_at: -1 });

const ListArtifact = db.qm.model<IListArtifact>("list_artifact", listArtifactSchema);

export default ListArtifact;
