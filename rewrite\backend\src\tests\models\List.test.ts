import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('List Model', () => {
    let mockMongoose: any;
    let mockDb: any;
    let mockIoEmitter: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        mockIoEmitter = testSetup.mockIoEmitter;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create List model with proper schema definition and database registration', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/List')];

        const ListModule = await import('../../models/List');
        const List = ListModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('list', expect.any(Object));
        expect(List).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];
        expect(schemaArg.paths).toBeDefined();

        expect(schemaArg.paths.owner_id).toBeDefined();
        expect(schemaArg.paths.owner_id.required).toBe(true);
        expect(schemaArg.paths.owner_id.index).toBe(true);

        expect(schemaArg.paths.name).toBeDefined();
        expect(schemaArg.paths.name.type).toBe(String);
        expect(schemaArg.paths.name.required).toBe(true);
        expect(schemaArg.paths.name.unique).toBe(true);
        expect(schemaArg.paths.name.trim).toBe(true);

        expect(schemaArg.paths.shared_with_organization).toBeDefined();
        expect(schemaArg.paths.shared_with_organization.type).toBe(Boolean);
        expect(schemaArg.paths.shared_with_organization.required).toBe(true);
        expect(schemaArg.paths.shared_with_organization.default).toBe(false);

        expect(schemaArg.paths.is_deleted).toBeDefined();
        expect(schemaArg.paths.is_deleted.type).toBe(Boolean);
        expect(schemaArg.paths.is_deleted.required).toBe(true);
        expect(schemaArg.paths.is_deleted.default).toBe(false);

        expect(schemaArg.paths.created_at).toBeDefined();
        expect(schemaArg.paths.created_at.type).toBe(Date);
        expect(schemaArg.paths.created_at.required).toBe(true);
        expect(schemaArg.paths.created_at.default).toBeDefined();
        const createdAtDefault = schemaArg.paths.created_at.default();
        expect(createdAtDefault).toBeInstanceOf(Date);

        expect(schemaArg.paths.updated_at).toBeDefined();
        expect(schemaArg.paths.updated_at.type).toBe(Date);
        expect(schemaArg.paths.updated_at.required).toBe(true);
        expect(schemaArg.paths.updated_at.default).toBeDefined();
        const updatedAtDefault = schemaArg.paths.updated_at.default();
        expect(updatedAtDefault).toBeInstanceOf(Date);

        expect(schemaArg.index).toHaveBeenCalledWith({ owner_id: 1, created_at: -1 });

        expect(schemaArg.post).toHaveBeenCalledTimes(3);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndUpdate", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(3, "findOneAndDelete", expect.any(Function));
    });

    describe('emitChangedEvent', () => {
        it('should return early when list is null', async () => {
            jest.resetModules();
            jest.doMock('../../modules/ioEmitter', () => ({
                default: mockIoEmitter,
                __esModule: true
            }));
            jest.doMock('mongoose', () => mockMongoose);
            jest.doMock('../../modules/db', () => mockDb);

            const ListModule = await import('../../models/List');
            const { emitChangedEvent } = ListModule;

            emitChangedEvent(null as any);

            expect(mockIoEmitter.emit).not.toHaveBeenCalled();
        });

        it('should return early when list is undefined', async () => {
            jest.resetModules();
            jest.doMock('../../modules/ioEmitter', () => ({
                default: mockIoEmitter,
                __esModule: true
            }));
            jest.doMock('mongoose', () => mockMongoose);
            jest.doMock('../../modules/db', () => mockDb);

            const ListModule = await import('../../models/List');
            const { emitChangedEvent } = ListModule;

            emitChangedEvent(undefined as any);

            expect(mockIoEmitter.emit).not.toHaveBeenCalled();
        });

        it('should emit event with toObject result when list has toObject method', async () => {
            jest.resetModules();
            jest.doMock('../../modules/ioEmitter', () => ({
                default: mockIoEmitter,
                __esModule: true
            }));
            jest.doMock('mongoose', () => mockMongoose);
            jest.doMock('../../modules/db', () => mockDb);

            const ListModule = await import('../../models/List');
            const { emitChangedEvent } = ListModule;

            const mockList = {
                toObject: jest.fn().mockReturnValue({ _id: 'test-id', name: 'Test List' })
            };

            emitChangedEvent(mockList as any);

            expect(mockIoEmitter.emit).toHaveBeenCalledWith("notifyAll", {
                name: "list/changed",
                data: { _id: 'test-id', name: 'Test List' }
            });
        });

        it('should use list as fallback when toObject returns undefined', async () => {
            jest.resetModules();
            jest.doMock('../../modules/ioEmitter', () => ({
                default: mockIoEmitter,
                __esModule: true
            }));
            jest.doMock('mongoose', () => mockMongoose);
            jest.doMock('../../modules/db', () => mockDb);

            const ListModule = await import('../../models/List');
            const { emitChangedEvent } = ListModule;

            const mockList = {
                toObject: jest.fn().mockReturnValue(undefined),
                _id: 'test-id',
                name: 'Test List'
            };

            emitChangedEvent(mockList as any);

            expect(mockIoEmitter.emit).toHaveBeenCalledWith("notifyAll", {
                name: "list/changed",
                data: mockList
            });
        });

        it('should use list as fallback when toObject returns null', async () => {
            jest.resetModules();
            jest.doMock('../../modules/ioEmitter', () => ({
                default: mockIoEmitter,
                __esModule: true
            }));
            jest.doMock('mongoose', () => mockMongoose);
            jest.doMock('../../modules/db', () => mockDb);

            const ListModule = await import('../../models/List');
            const { emitChangedEvent } = ListModule;

            const mockList = {
                toObject: jest.fn().mockReturnValue(null),
                _id: 'test-id',
                name: 'Test List'
            };

            emitChangedEvent(mockList as any);

            expect(mockIoEmitter.emit).toHaveBeenCalledWith("notifyAll", {
                name: "list/changed",
                data: mockList
            });
        });

        it('should use list as fallback when list has no toObject method', async () => {
            jest.resetModules();
            jest.doMock('../../modules/ioEmitter', () => ({
                default: mockIoEmitter,
                __esModule: true
            }));
            jest.doMock('mongoose', () => mockMongoose);
            jest.doMock('../../modules/db', () => mockDb);

            const ListModule = await import('../../models/List');
            const { emitChangedEvent } = ListModule;

            const mockList = { _id: 'test-id', name: 'Test List' };

            emitChangedEvent(mockList as any);

            expect(mockIoEmitter.emit).toHaveBeenCalledWith("notifyAll", {
                name: "list/changed",
                data: mockList
            });
        });
    });
});