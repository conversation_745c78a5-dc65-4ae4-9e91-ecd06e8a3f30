import { expect, jest } from '@jest/globals';

const mockJest = jest as any;
export const createMongooseConnectionMock = () => ({
    connect: mockJest.fn().mockResolvedValue({}),
    disconnect: mockJest.fn().mockResolvedValue({}),
    connection: {
        readyState: 1,
        on: mockJest.fn(),
        once: mockJest.fn(),
        emit: mockJest.fn(),
        close: mockJest.fn().mockResolvedValue({}),
        db: {
            databaseName: 'test-database',
            admin: mockJest.fn().mockReturnValue({
                ping: mockJest.fn().mockResolvedValue({})
            })
        }
    },
    model: mockJest.fn().mockImplementation((name: string, schema: any, collection?: string) => {
        const MockModel = function (this: any, data: any) {
            Object.assign(this, data);
        };
        MockModel.modelName = name;
        MockModel.schema = schema;
        MockModel.collection = collection;
        return MockModel;
    }),
    Schema: Object.assign(mockJest.fn().mockImplementation(function (this: any, definition: any) {
        this.definition = definition;
        this.paths = {};
        this.s = { hooks: { _posts: new Map(), _pres: new Map() } };

        if (definition && typeof definition === 'object') {
            Object.keys(definition).forEach(key => {
                this.paths[key] = definition[key];
            });
        }

        this.index = mockJest.fn();
        this.pre = mockJest.fn((event: string, fn: Function) => {
            if (!this.s.hooks._pres.has(event)) {
                this.s.hooks._pres.set(event, []);
            }
            this.s.hooks._pres.get(event).push({ fn });
        });
        this.post = mockJest.fn((event: string, fn: Function) => {
            if (!this.s.hooks._posts.has(event)) {
                this.s.hooks._posts.set(event, []);
            }
            this.s.hooks._posts.get(event).push({ fn });
        });

        return this;
    }), {
        Types: {
            ObjectId: mockJest.fn().mockImplementation((id) => id || '507f1f77bcf86cd799439011'),
            Mixed: mockJest.fn().mockImplementation(() => 'mock-mixed'),
            Boolean: mockJest.fn().mockImplementation(() => 'mock-boolean')
        }
    }),
    Types: {
        ObjectId: mockJest.fn().mockImplementation((id) => id || '507f1f77bcf86cd799439011'),
        Mixed: mockJest.fn().mockImplementation(() => 'mock-mixed'),
        Boolean: mockJest.fn().mockImplementation(() => 'mock-boolean')
    }
});

export const setupDatabaseScenarios = () => {
    const mockConnection = createMongooseConnectionMock();

    return {
        mockConnection,
        connectionSuccess: () => {
            mockConnection.connect.mockResolvedValue({} as any);
            mockConnection.connection.readyState = 1;
        },
        connectionFailure: (errorMessage = 'Connection failed') => {
            const error = new Error(errorMessage);
            mockConnection.connect.mockRejectedValue(error);
            mockConnection.connection.readyState = 0;
            return error;
        },
        connectionTimeout: () => {
            const error = new Error('Connection timeout');
            error.name = 'MongoTimeoutError';
            mockConnection.connect.mockRejectedValue(error);
            return error;
        },
        authenticationFailure: () => {
            const error = new Error('Authentication failed');
            error.name = 'MongoAuthError';
            mockConnection.connect.mockRejectedValue(error);
            return error;
        },
        networkError: () => {
            const error = new Error('Network error');
            error.name = 'MongoNetworkError';
            mockConnection.connect.mockRejectedValue(error);
            return error;
        },
        disconnection: () => {
            mockConnection.disconnect.mockResolvedValue({});
            mockConnection.connection.readyState = 0;
        }
    };
};

export const createDatabaseOperationMocks = () => ({
    startSession: mockJest.fn().mockResolvedValue({
        startTransaction: mockJest.fn(),
        commitTransaction: mockJest.fn().mockResolvedValue({}),
        abortTransaction: mockJest.fn().mockResolvedValue({}),
        endSession: mockJest.fn().mockResolvedValue({})
    }),
    collection: mockJest.fn().mockReturnValue({
        insertOne: mockJest.fn().mockResolvedValue({ insertedId: '507f1f77bcf86cd799439011' }),
        insertMany: mockJest.fn().mockResolvedValue({ insertedIds: ['507f1f77bcf86cd799439011'] }),
        findOne: mockJest.fn().mockResolvedValue({}),
        find: mockJest.fn().mockReturnValue({
            toArray: mockJest.fn().mockResolvedValue([]),
            limit: mockJest.fn().mockReturnThis(),
            skip: mockJest.fn().mockReturnThis(),
            sort: mockJest.fn().mockReturnThis()
        }),
        updateOne: mockJest.fn().mockResolvedValue({ modifiedCount: 1 }),
        updateMany: mockJest.fn().mockResolvedValue({ modifiedCount: 1 }),
        deleteOne: mockJest.fn().mockResolvedValue({ deletedCount: 1 }),
        deleteMany: mockJest.fn().mockResolvedValue({ deletedCount: 1 }),
        countDocuments: mockJest.fn().mockResolvedValue(0),
        aggregate: mockJest.fn().mockReturnValue({
            toArray: mockJest.fn().mockResolvedValue([])
        }),
        createIndex: mockJest.fn().mockResolvedValue('index_name'),
        dropIndex: mockJest.fn().mockResolvedValue({})
    }),
    admin: mockJest.fn().mockReturnValue({
        ping: mockJest.fn().mockResolvedValue({}),
        serverStatus: mockJest.fn().mockResolvedValue({
            ok: 1,
            version: '4.4.0'
        }),
        listDatabases: mockJest.fn().mockResolvedValue({
            databases: [{ name: 'test-database', sizeOnDisk: 1024 }]
        })
    })
});

export const createMultiDatabaseMocks = () => ({
    qm: createMongooseConnectionMock(),
    qmai: createMongooseConnectionMock(),
    qmShared: createMongooseConnectionMock(),
    lookups: createMongooseConnectionMock(),
    setupAllConnections: function () {
        this.qm.connection.db.databaseName = 'quartermaster';
        this.qmai.connection.db.databaseName = 'quartermaster-ai';
        this.qmShared.connection.db.databaseName = 'quartermaster-shared';
        this.lookups.connection.db.databaseName = 'lookups';

        return {
            qm: this.qm,
            qmai: this.qmai,
            qmShared: this.qmShared,
            lookups: this.lookups
        };
    },
    simulateConnectionFailures: function (databases: string[] = ['qm', 'qmai', 'qmShared', 'lookups']) {
        databases.forEach(db => {
            if (this[db]) {
                this[db].connect.mockRejectedValue(new Error(`${db} connection failed`));
                this[db].connection.readyState = 0;
            }
        });
    },
    simulateSuccessfulConnections: function (databases: string[] = ['qm', 'qmai', 'qmShared', 'lookups']) {
        databases.forEach(db => {
            if (this[db]) {
                this[db].connect.mockResolvedValue({});
                this[db].connection.readyState = 1;
            }
        });
    }
});

export const setupDatabaseTest = () => {
    const mockConnections = createMultiDatabaseMocks();
    const mockOperations = createDatabaseOperationMocks();

    return {
        mockConnections,
        mockOperations
    };
};

export const expectDatabaseConnected = (mockConnection: any, connectionString?: string) => {
    expect(mockConnection.connect).toHaveBeenCalled();
    if (connectionString) {
        expect(mockConnection.connect).toHaveBeenCalledWith(connectionString, expect.any(Object));
    }
};

export const expectDatabaseDisconnected = (mockConnection: any) => {
    expect(mockConnection.disconnect).toHaveBeenCalled();
};

export const expectTransactionStarted = (mockSession: any) => {
    expect(mockSession.startTransaction).toHaveBeenCalled();
};

export const expectTransactionCommitted = (mockSession: any) => {
    expect(mockSession.commitTransaction).toHaveBeenCalled();
};

export const expectTransactionAborted = (mockSession: any) => {
    expect(mockSession.abortTransaction).toHaveBeenCalled();
};

export const createDatabaseHealthMocks = () => ({
    checkConnection: mockJest.fn().mockResolvedValue({ status: 'connected', latency: 10 }),
    checkAllConnections: mockJest.fn().mockResolvedValue({
        qm: { status: 'connected', latency: 10 },
        qmai: { status: 'connected', latency: 15 },
        qmShared: { status: 'connected', latency: 12 },
        lookups: { status: 'connected', latency: 13 }
    }),
    getDatabaseStats: mockJest.fn().mockResolvedValue({
        collections: 10,
        documents: 1000,
        dataSize: 1024000,
        indexSize: 512000
    }),
    getConnectionInfo: mockJest.fn().mockResolvedValue({
        host: 'localhost',
        port: 27017,
        database: 'test-database',
        readyState: 1
    })
});

export const cleanupDatabaseTest = () => {
    jest.clearAllMocks();
};

export const setupModelTest = (database: string = 'qm') => {
    const mockConnection = createMongooseConnectionMock();
    const mockDb = { [database]: mockConnection };
    const mockIoEmitter = { emit: mockJest.fn() };
    const mockUtils = { defaultDateTimeFormat: 'MM/DD/YYYY HH:mm:ss' };

    return {
        mockMongoose: mockConnection,
        mockDb,
        mockIoEmitter,
        mockUtils
    };
};

export const cleanupModelTest = () => {
    jest.clearAllMocks();
};

const mockJestFn = jest.fn;
export const qm = {
    model: mockJestFn().mockReturnValue({
        find: mockJestFn(),
        aggregate: mockJestFn(),
        create: mockJestFn(),
        findOne: mockJestFn(),
        findById: mockJestFn(),
        findOneAndDelete: mockJestFn(),
        findOneAndUpdate: mockJestFn(),
        updateOne: mockJestFn(),
        updateMany: mockJestFn(),
        deleteOne: mockJestFn(),
        deleteMany: mockJestFn(),
        countDocuments: mockJestFn(),
        distinct: mockJestFn(),
        save: mockJestFn()
    }),
    collection: mockJestFn(),
    connection: {
        readyState: 1,
        on: mockJestFn(),
        once: mockJestFn(),
        emit: mockJestFn(),
        close: mockJestFn()
    }
};

export const qmai = {
    model: mockJestFn(),
    collection: mockJestFn(),
    connection: {
        readyState: 1,
        on: mockJestFn(),
        once: mockJestFn(),
        emit: mockJestFn(),
        close: mockJestFn()
    }
};

export const qmShared = {
    model: mockJestFn(),
    collection: mockJestFn(),
    connection: {
        readyState: 1,
        on: mockJestFn(),
        once: mockJestFn(),
        emit: mockJestFn(),
        close: mockJestFn()
    }
};

export const lookups = {
    model: mockJestFn().mockReturnValue({
        find: mockJestFn(),
        aggregate: mockJestFn(),
        create: mockJestFn(),
        findOne: mockJestFn(),
        findById: mockJestFn(),
        findOneAndDelete: mockJestFn(),
        findOneAndUpdate: mockJestFn(),
        updateOne: mockJestFn(),
        updateMany: mockJestFn(),
        deleteOne: mockJestFn(),
        deleteMany: mockJestFn(),
        countDocuments: mockJestFn(),
        distinct: mockJestFn(),
        save: mockJestFn()
    }),
    collection: mockJestFn().mockReturnValue({
        find: mockJestFn().mockReturnValue({
            toArray: mockJestFn(),
            limit: mockJestFn().mockReturnThis(),
            skip: mockJestFn().mockReturnThis(),
            sort: mockJestFn().mockReturnThis()
        }),
        countDocuments: mockJestFn(),
        aggregate: mockJestFn().mockReturnValue({
            toArray: mockJestFn()
        }),
        insertOne: mockJestFn(),
        insertMany: mockJestFn(),
        findOne: mockJestFn(),
        updateOne: mockJestFn(),
        updateMany: mockJestFn(),
        deleteOne: mockJestFn(),
        deleteMany: mockJestFn(),
        createIndex: mockJestFn(),
        dropIndex: mockJestFn()
    }),
    connection: {
        readyState: 1,
        on: mockJestFn(),
        once: mockJestFn(),
        emit: mockJestFn(),
        close: mockJestFn()
    }
};

export const searchArtifacts = {
    model: mockJestFn().mockReturnValue({
        find: mockJestFn(),
        aggregate: mockJestFn(),
        create: mockJestFn(),
        findOne: mockJestFn(),
        findById: mockJestFn(),
        findOneAndDelete: mockJestFn(),
        findOneAndUpdate: mockJestFn(),
        updateOne: mockJestFn(),
        updateMany: mockJestFn(),
        deleteOne: mockJestFn(),
        deleteMany: mockJestFn(),
        countDocuments: mockJestFn(),
        distinct: mockJestFn(),
        save: mockJestFn()
    }),
    collection: mockJestFn().mockReturnValue({
        find: mockJestFn().mockReturnValue({
            toArray: mockJestFn(),
            limit: mockJestFn().mockReturnThis(),
            skip: mockJestFn().mockReturnThis(),
            sort: mockJestFn().mockReturnThis()
        }),
        countDocuments: mockJestFn(),
        aggregate: mockJestFn().mockReturnValue({
            toArray: mockJestFn()
        }),
        insertOne: mockJestFn(),
        insertMany: mockJestFn(),
        findOne: mockJestFn(),
        updateOne: mockJestFn(),
        updateMany: mockJestFn(),
        deleteOne: mockJestFn(),
        deleteMany: mockJestFn(),
        createIndex: mockJestFn(),
        dropIndex: mockJestFn()
    }),
    connection: {
        readyState: 1,
        on: mockJestFn(),
        once: mockJestFn(),
        emit: mockJestFn(),
        close: mockJestFn()
    }
};

const db = {
    qm,
    qmai,
    qmShared,
    locations: qm,
    locationsOptimized: qm,
    locationsRaw: qm,
    ais: qm,
    audio: qm,
    lookups,
    searchArtifacts
};

export default db;
