import { Button, Grid, Modal, FormControl, Autocomplete, TextField, alpha, Typography, Chip, Checkbox, ListItemText } from "@mui/material";
import React, { useState, useEffect } from "react";
import ModalContainer from "../../../components/ModalContainer";
import theme from "../../../theme";

const FilterEvaluatedArtifactsModal = ({
    showFilterModal,
    setShowFilterModal,
    selectedEvaluationTypes,
    setSelectedEvaluationTypes,
    evaluationTypeOptions,
    onApply,
}) => {
    const [localEvaluationTypes, setLocalEvaluationTypes] = useState([]);

    useEffect(() => {
        if (showFilterModal) {
            setLocalEvaluationTypes(selectedEvaluationTypes);
        }
    }, [showFilterModal, selectedEvaluationTypes]);

    const handleClose = (event, reason) => {
        if (reason === "backdropClick") {
            return;
        }
        setShowFilterModal(false);
        // Reset to original values on close
        setLocalEvaluationTypes(selectedEvaluationTypes);
    };

    const handleSubmit = () => {
        setSelectedEvaluationTypes(localEvaluationTypes);
        setShowFilterModal(false);
        if (onApply) {
            onApply(localEvaluationTypes);
        }
    };

    const handleClear = () => {
        setLocalEvaluationTypes([]);
        setSelectedEvaluationTypes([]);
        setShowFilterModal(false);
        if (onApply) {
            onApply([]);
        }
    };

    const renderAutocomplete = (items, selected, setSelected, label, getOptionLabel) => (
        <FormControl sx={{ width: "100%", maxHeight: "150px", overflowY: "auto" }} size="small">
            <Autocomplete
                multiple
                value={selected}
                onChange={(event, newValue) => setSelected(newValue)}
                options={items}
                disableCloseOnSelect
                getOptionLabel={(option) => (getOptionLabel ? getOptionLabel(option) : option)}
                renderInput={(params) => <TextField {...params} label={`Select ${label}`} variant="outlined" />}
                sx={{ "& .MuiFormLabel-root": { color: alpha("#FFFFFF", 0.6), fontWeight: 400, maxHeight: "250px", overflowY: "auto" } }}
                noOptionsText={
                    <Typography sx={{ color: "#FFFFFF", fontWeight: 500, padding: "8px" }}>No Options</Typography>
                }
                renderOption={(props, option, { selected }) => {
                    const { key, ...restProps } = props;
                    return (
                        <React.Fragment key={key}>
                            <li {...restProps}>
                                <Checkbox checked={selected} />
                                <ListItemText primary={getOptionLabel ? getOptionLabel(option) : option} />
                            </li>
                        </React.Fragment>
                    );
                }}
                isOptionEqualToValue={(option, value) => {
                    if (typeof option === "string" && typeof value === "string") {
                        return option === value;
                    }
                    if (option?.value && value?.value) {
                        return option.value === value.value;
                    }
                    return option === value;
                }}
            />
        </FormControl>
    );

    return (
        <Modal open={Boolean(showFilterModal)} onClose={handleClose}>
            <ModalContainer title="Filter" onClose={handleClose} showDivider>
                <Grid container direction="row" gap={2} width={{ xs: 300, sm: 500 }} sx={{ maxHeight: "70vh", overflowY: "auto" }}>
                    {renderAutocomplete(
                        evaluationTypeOptions.map((opt) => opt.value),
                        localEvaluationTypes,
                        setLocalEvaluationTypes,
                        "Evaluation Types",
                        (option) => {
                            const opt = evaluationTypeOptions.find((o) => o.value === option);
                            return opt?.label || option;
                        },
                    )}
                </Grid>
                <Grid container gap={2} justifyContent="space-between" mt={2}>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                            }}
                            onClick={handleClear}
                        >
                            Clear filters
                        </Button>
                    </Grid>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                backgroundColor: theme.palette.custom.mainBlue,
                                "&:hover": { backgroundColor: theme.palette.custom.mainBlue },
                            }}
                            variant="contained"
                            onClick={handleSubmit}
                        >
                            Apply
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default FilterEvaluatedArtifactsModal;

