import { useEffect, useState } from "react";
import { StoreContext } from "../contexts/StoreContext";
import favouriteArtifactsController from "../controllers/FavouriteArtifacts.controller";
import { useUser } from "../hooks/UserHook";
import { getSocket } from "../socket";

export const StoreProvider = ({ children }) => {
    const [favouritesArtifacts, setFavouritesArtifacts] = useState([]); // fav artifacts ids
    const [artifactsFavourites, setArtifactsFavourites] = useState([]); //fav artifacts details
    const [selectedSaveList, setSelectedSaveList] = useState(() => {
        if (typeof window !== "undefined") {
            const saved = localStorage.getItem("selectedSaveList");
            if (saved) {
                try {
                    return JSON.parse(saved);
                } catch (e) {
                    return { id: "favorites", name: "Favorites" };
                }
            }
        }
        return { id: "favorites", name: "Favorites" };
    });
    const { user } = useUser();
    const fetchFavouritesArtifacts = async () => {
        try {
            const { favourites, artifacts } = await favouriteArtifactsController.getUserFavouriteArtifacts();
            const favoritesIds = favourites.map((f) => f.artifact_id);
            setFavouritesArtifacts(favoritesIds);
            setArtifactsFavourites(artifacts);
            localStorage.setItem("favouritesArtifacts", JSON.stringify(favoritesIds));
            // Dispatch custom event for same-tab localStorage changes
            window.dispatchEvent(
                new CustomEvent("localStorageChange", {
                    detail: {
                        key: "favouritesArtifacts",
                        newValue: JSON.stringify(favoritesIds),
                    },
                }),
            );
        } catch (err) {
            console.error("An error occurred while  fetchFavouritesArtifacts in the Store :", err);
        }
    };

    useEffect(() => {
        if (user) {
            fetchFavouritesArtifacts();
        }
    }, [user]);

    // Add socket listeners for realtime favourites updates
    useEffect(() => {
        if (!user) return;
        const socket = getSocket();
        const handleFavouriteChange = () => {
            fetchFavouritesArtifacts(); // Refresh favourites from server
        };

        socket.on("favourites/changed", handleFavouriteChange);

        return () => {
            socket.off("favourites/changed", handleFavouriteChange);
        };
    }, [user]);

    useEffect(() => {
        if (typeof window !== "undefined") {
            localStorage.setItem("selectedSaveList", JSON.stringify(selectedSaveList));
        }
    }, [selectedSaveList]);

    useEffect(() => {
        const handleResetSelectedSaveList = () => {
            const defaultList = { id: "favorites", name: "Favorites" };
            setSelectedSaveList(defaultList);
            if (typeof window !== "undefined") {
                localStorage.setItem("selectedSaveList", JSON.stringify(defaultList));
                window.dispatchEvent(
                    new CustomEvent("localStorageChange", {
                        detail: {
                            key: "selectedSaveList",
                            newValue: JSON.stringify(defaultList),
                        },
                    }),
                );
            }
        };

        window.addEventListener("resetSelectedSaveList", handleResetSelectedSaveList);
        return () => {
            window.removeEventListener("resetSelectedSaveList", handleResetSelectedSaveList);
        };
    }, [setSelectedSaveList]);

    return (
        <StoreContext.Provider
            value={{
                favouritesArtifacts,
                fetchFavouritesArtifacts,
                setFavouritesArtifacts,
                artifactsFavourites,
                selectedSaveList,
                setSelectedSaveList,
            }}
        >
            {children}
        </StoreContext.Provider>
    );
};
