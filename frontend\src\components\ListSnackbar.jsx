import React, { forwardRef, useCallback } from "react";
import { useSnackbar, SnackbarContent } from "notistack";
import { Box, Typography, Link, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";

const ListSnackbar = forwardRef(({ id, ...props }, ref) => {
    const { closeSnackbar } = useSnackbar();

    const handleDismiss = useCallback(() => {
        closeSnackbar(id);
    }, [id, closeSnackbar]);

    const handleSettingsClick = useCallback((e) => {
        e.preventDefault();
        e.stopPropagation();
        closeSnackbar(id);

        window.dispatchEvent(new CustomEvent("showManageLists", { detail: { show: true } }));

        if (window.navigateTo) {
            const currentPath = window.location.pathname;
            if (!currentPath.startsWith("/dashboard/events")) {
                window.navigateTo("/dashboard/events");
            }
        }
    }, [id, closeSnackbar]);

    return (
        <SnackbarContent ref={ref}>
            <Box
                sx={{
                    backgroundColor: "#4CAF50",
                    color: "#FFFFFF",
                    padding: "12px 16px",
                    borderRadius: "4px",
                    minWidth: "300px",
                    maxWidth: "500px",
                    display: "flex",
                    alignItems: "flex-start",
                    justifyContent: "space-between",
                    boxShadow: "0px 3px 5px -1px rgba(0,0,0,0.2), 0px 6px 10px 0px rgba(0,0,0,0.14), 0px 1px 18px 0px rgba(0,0,0,0.12)",
                }}
            >
                <Box sx={{ flex: 1, minWidth: 0 }}>
                    <Typography
                        sx={{
                            color: "#FFFFFF",
                            fontSize: "14px",
                            mb: 0.5,
                            fontWeight: 400,
                        }}
                    >
                        {props.message}
                    </Typography>
                    <Typography
                        sx={{
                            color: "#FFFFFF",
                            fontSize: "14px",
                            fontWeight: 400,
                        }}
                    >
                        Change the default list in{" "}
                        <Link
                            component="button"
                            onClick={handleSettingsClick}
                            sx={{
                                color: "#FFFFFF",
                                textDecoration: "underline",
                                cursor: "pointer",
                                background: "none",
                                border: "none",
                                padding: 0,
                                font: "inherit",
                                "&:hover": {
                                    textDecoration: "underline",
                                },
                            }}
                        >
                            Manage Lists
                        </Link>
                    </Typography>
                </Box>
                <IconButton
                    size="small"
                    onClick={handleDismiss}
                    sx={{
                        color: "#FFFFFF",
                        padding: "4px",
                        marginLeft: "8px",
                        "&:hover": {
                            backgroundColor: "rgba(255, 255, 255, 0.1)",
                        },
                    }}
                >
                    <CloseIcon fontSize="small" />
                </IconButton>
            </Box>
        </SnackbarContent>
    );
});

ListSnackbar.displayName = "ListSnackbar";

export default ListSnackbar;

