import { jest, describe, it, beforeEach, expect, afterEach } from '@jest/globals';
import { createMockRequest, createMockResponse, createMockNext, expectForbiddenResponse, expectNextCalled, expectNextNotCalled } from '../mocks/middlewares/auth.mock';
import restrictEndpointByUser from '../../middlewares/restrictEndpointByUser';

jest.mock('../../utils/userEndpointRestrictions', () => ({
    userEndpointRestrictions: {
        1: ['user1', 'user2'],
        2: ['user3'],
        3: []
    }
}));

describe('restrictEndpointByUser middleware', () => {
    let req: any;
    let res: any;
    let next: jest.Mock;
    let originalEnv: any;

    beforeEach(() => {
        req = createMockRequest();
        res = createMockResponse();
        next = createMockNext();
        originalEnv = process.env.NODE_ENV;
    });

    afterEach(() => {
        process.env.NODE_ENV = originalEnv;
    });

    it('should call next if no endpoint_id is present', () => {
        req._endpoint_id = undefined;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should call next if endpoint_id is null', () => {
        req._endpoint_id = null;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should call next if endpoint_id is not in restrictions', () => {
        req._endpoint_id = 999;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should call next if in portal environment and user is allowed', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should return 403 if in portal environment but user is not allowed', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = { _id: 'user3' };

        restrictEndpointByUser(req, res, next);

        expectForbiddenResponse(res, 'Access denied. Operation restricted to specific user(s).');
        expectNextNotCalled(next);
    });

    it('should return 403 if in portal environment and endpoint has no allowed users', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 3;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectForbiddenResponse(res, 'Access denied. Operation restricted to specific user(s).');
        expectNextNotCalled(next);
    });

    it('should handle user with ObjectId string', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = { _id: { toString: () => 'user1' } };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should handle user with ObjectId object', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        const mockObjectId = { toString: jest.fn().mockReturnValue('user1') };
        req.user = { _id: mockObjectId };

        restrictEndpointByUser(req, res, next);

        expect(mockObjectId.toString).toHaveBeenCalled();
        expectNextCalled(next);
    });

    it('should handle user without _id property', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = { name: 'test' };

        expect(() => restrictEndpointByUser(req, res, next)).toThrow();
    });

    it('should handle user with null _id', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = { _id: null };

        expect(() => restrictEndpointByUser(req, res, next)).toThrow();
    });

    it('should handle user with undefined _id', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = { _id: undefined };

        expect(() => restrictEndpointByUser(req, res, next)).toThrow();
    });

    it('should handle req.user being null', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = null;

        expect(() => restrictEndpointByUser(req, res, next)).toThrow();
    });

    it('should handle req.user being undefined', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = undefined;

        expect(() => restrictEndpointByUser(req, res, next)).toThrow();
    });

    it('should handle string endpoint_id', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = '1';
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should handle numeric endpoint_id', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should handle production environment', () => {
        process.env.NODE_ENV = 'portal';
        req._endpoint_id = 1;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should handle test environment', () => {
        process.env.NODE_ENV = 'test';
        req._endpoint_id = 1;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });

    it('should handle undefined NODE_ENV', () => {
        delete process.env.NODE_ENV;
        req._endpoint_id = 1;
        req.user = { _id: 'user1' };

        restrictEndpointByUser(req, res, next);

        expectNextCalled(next);
    });
});
