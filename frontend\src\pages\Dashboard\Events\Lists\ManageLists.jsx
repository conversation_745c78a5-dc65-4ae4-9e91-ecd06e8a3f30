import { Box, Typo<PERSON>, IconButton, Tooltip, Stack, TableContainer, Table, TableHead, TableRow, TableCell, TableBody, Grid, Card, CardContent, useMediaQuery } from "@mui/material";
import theme from "../../../../theme";
import { useMemo, useState } from "react";
import { Star, DeleteOutline, Share, Edit, RemoveCircleOutlineOutlined, ListOutlined } from "@mui/icons-material";
import ListModal from "./ListModal";
import DeleteListModal from "./DeleteListModal";
import ShareListModal from "./ShareListModal";
import useStore from "../../../../hooks/Store";

const HeaderCell = ({ children }) => (
    <TableCell sx={{ color: "#9AA4BF", fontSize: "12px", borderBottom: `1px solid ${theme.palette.custom.borderColor}` }}>{children}</TableCell>
);
const BodyCell = ({ children, align }) => (
    <TableCell
        align={align}
        sx={{
            color: "#FFFFFF",
            fontSize: "14px",
            borderBottom: `1px solid ${theme.palette.custom.borderColor}`,
            verticalAlign: "top",
        }}
    >
        {children}
    </TableCell>
);

const ColGroup = () => (
    <colgroup>
        <col style={{ width: "25%" }} />
        <col style={{ width: "15%" }} />
        <col style={{ width: "10%" }} />
        <col style={{ width: "15%" }} />
        <col style={{ width: "15%" }} />
        <col style={{ width: "20%" }} />
    </colgroup>
);

export default function ManageLists({ lists = [], user = {}, onSelect, onListUpdate, onListDelete, onListShare }) {
    const [editingList, setEditingList] = useState(null);
    const [showRenameModal, setShowRenameModal] = useState(false);
    const [deletingList, setDeletingList] = useState(null);
    const [showDeleteModal, setShowDeleteModal] = useState(false);
    const [isSharedListDelete, setIsSharedListDelete] = useState(false);
    const [sharingList, setSharingList] = useState(null);
    const [showShareModal, setShowShareModal] = useState(false);
    const { artifactsFavourites, selectedSaveList } = useStore();
    const isMobile = useMediaQuery(theme.breakpoints.down("md"));

    const yourLists = useMemo(() => {
        const favourites = {
            _id: "favorites",
            name: "Favorites",
            owner_id: user?._id,
            owner_name: user?.name || "Unknown",
            shared_with_organization: false,
            created_at: null,
            updated_at: null,
            artifact_count: artifactsFavourites?.length || 0,
        }
        return [favourites, ...lists.filter((l) => String(l.owner_id) === String(user?._id))]
    }, [lists, user, artifactsFavourites]);
    const sharedWithYou = useMemo(() => lists.filter((l) => String(l.owner_id) !== String(user?._id)), [lists, user]);

    const handleEditClick = (list) => {
        setEditingList(list);
        setShowRenameModal(true);
    };

    const handleDeleteClick = (list, isShared = false) => {
        setDeletingList(list);
        setIsSharedListDelete(isShared);
        setShowDeleteModal(true);
    };

    const handleShareClick = (list) => {
        setSharingList(list);
        setShowShareModal(true);
    };

    const renderListCard = (l, isShared = false) => (
        <Card
            key={l._id}
            sx={{
                backgroundColor: theme.palette.custom.darkBlue,
                border: `1px solid ${theme.palette.custom.borderColor}`,
                borderRadius: 2,
                mb: 2,
                "&:last-child": { mb: 0 },
            }}
        >
            <CardContent sx={{ p: 2, "&:last-child": { pb: 2 } }}>
                <Stack spacing={2}>
                    <Stack direction="row" spacing={1} alignItems="center" justifyContent="space-between">
                        <Stack direction="row" spacing={1} alignItems="center" sx={{ minWidth: 0, flex: 1 }}>
                            {isShared ? (
                                <img src="/icons/shared-list-icon.svg" alt="List" width={20} height={20} style={{ flexShrink: 0 }} />
                            ) : (
                                <ListOutlined sx={{ color: "#9AA4BF", fontSize: 16, flexShrink: 0 }} />
                            )}
                            <Tooltip title={l.name} placement="top" arrow>
                                <Typography
                                    onClick={() => onSelect && onSelect(l._id)}
                                    sx={{
                                        color: theme.palette.custom.mainBlue,
                                        fontSize: "14px",
                                        textDecoration: "underline",
                                        cursor: "pointer",
                                        overflow: "hidden",
                                        textOverflow: "ellipsis",
                                        whiteSpace: "nowrap",
                                        minWidth: 0,
                                        flex: 1,
                                    }}
                                >
                                    {l.name}
                                </Typography>
                            </Tooltip>
                            {selectedSaveList?.id === l._id && <Star sx={{ color: "#FFD700", fontSize: 16, flexShrink: 0 }} />}
                        </Stack>
                        <Stack direction="row" spacing={1}>
                            {!isShared && (
                                <>
                                    <Tooltip title="Edit" placement="top">
                                        <IconButton
                                            size="small"
                                            disabled={l._id === "favorites"}
                                            onClick={() => handleEditClick(l)}
                                            sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px" }}
                                        >
                                            <Edit sx={{ color: "#9AA4BF", fontSize: 16 }} />
                                        </IconButton>
                                    </Tooltip>
                                    <Tooltip title="Share" placement="top">
                                        <IconButton
                                            size="small"
                                            disabled={l._id === "favorites"}
                                            onClick={() => handleShareClick(l)}
                                            sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px" }}
                                        >
                                            <Share sx={{ color: "#9AA4BF", fontSize: 16 }} />
                                        </IconButton>
                                    </Tooltip>
                                </>
                            )}
                            <Tooltip title={isShared ? "Remove" : "Delete"} placement="top">
                                <IconButton
                                    size="small"
                                    disabled={(!isShared && l._id === "favorites") || (isShared && l.shared_with_organization)}
                                    onClick={() => handleDeleteClick(l, isShared)}
                                    sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px" }}
                                >
                                    {isShared ? (
                                        <RemoveCircleOutlineOutlined sx={{ color: "#E60000", fontSize: 16 }} />
                                    ) : (
                                        <DeleteOutline sx={{ color: "#E60000", fontSize: 16 }} />
                                    )}
                                </IconButton>
                            </Tooltip>
                        </Stack>
                    </Stack>
                    <Stack direction="row" spacing={2} flexWrap="wrap">
                        {isShared ? (
                            <>
                                <Box>
                                    <Typography sx={{ color: "#9AA4BF", fontSize: "12px" }}>Created by</Typography>
                                    <Typography sx={{ color: "#FFFFFF", fontSize: "14px" }}>{l.owner_name || "-"}</Typography>
                                </Box>
                            </>
                        ) : (
                            <>
                                <Box>
                                    <Typography sx={{ color: "#9AA4BF", fontSize: "12px" }}>Shared with</Typography>
                                    <Typography sx={{ color: "#FFFFFF", fontSize: "14px" }}>
                                        {l._id === "favorites" ? "-" : `${l.shared_users_count || 0} user${l.shared_users_count !== 1 ? "s" : ""}`}
                                    </Typography>
                                </Box>
                            </>
                        )}
                        <Box>
                            <Typography sx={{ color: "#9AA4BF", fontSize: "12px" }}>Artifacts</Typography>
                            <Typography sx={{ color: "#FFFFFF", fontSize: "14px" }}>{l.artifact_count || 0}</Typography>
                        </Box>
                        <Box>
                            <Typography sx={{ color: "#9AA4BF", fontSize: "12px" }}>Date Created</Typography>
                            <Typography sx={{ color: "#FFFFFF", fontSize: "14px" }}>{l.created_at ? new Date(l.created_at).toLocaleDateString() : "-"}</Typography>
                        </Box>
                        <Box>
                            <Typography sx={{ color: "#9AA4BF", fontSize: "12px" }}>Date Updated</Typography>
                            <Typography sx={{ color: "#FFFFFF", fontSize: "14px" }}>{l.updated_at ? new Date(l.updated_at).toLocaleDateString() : "-"}</Typography>
                        </Box>
                    </Stack>
                </Stack>
            </CardContent>
        </Card>
    );

    return (
        <Grid container sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: 2, p: { xs: 1, md: 2 }, display: "flex", flexDirection: "column", gap: { xs: 2, md: 3 }, height: { xs: "auto", md: "90%" } }}>
            <Grid item size={{ xs: 12 }} sx={{ flex: isMobile ? "none" : 1, minHeight: 0 }}>
                <Box sx={{ display: "flex", flexDirection: "column", height: isMobile ? "auto" : "100%" }}>
                    <Typography sx={{ color: "#FFFFFF", fontSize: "16px", mb: 1 }}>Your lists</Typography>
                    {isMobile ? (
                        <Box>
                            {yourLists.map((l) => renderListCard(l, false))}
                        </Box>
                    ) : (
                        <TableContainer sx={{ flex: 1, overflowY: "auto", borderRadius: 1, scrollbarGutter: "stable" }}>
                            <Table size="small" stickyHeader sx={{ background: "transparent", tableLayout: "fixed" }}>
                                <ColGroup />
                                <TableHead>
                                    <TableRow sx={{ "& .MuiTableCell-head": { color: theme.palette.custom.mainBlue, backgroundColor: theme.palette.custom.darkBlue, borderBottom: 0 } }}>
                                        <HeaderCell>List name</HeaderCell>
                                        <HeaderCell>Shared with</HeaderCell>
                                        <HeaderCell>Artifacts</HeaderCell>
                                        <HeaderCell>Date Created</HeaderCell>
                                        <HeaderCell>Date Updated</HeaderCell>
                                        <HeaderCell align="right">Actions</HeaderCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {yourLists.map((l) => (
                                        <TableRow key={l._id} sx={{ "& .MuiTableCell-root": { borderBottom: 0 } }}>
                                            <BodyCell>
                                                <Stack direction="row" spacing={1} alignItems="center" sx={{ minWidth: 0, flex: 1 }}>
                                                    <ListOutlined sx={{ color: "#9AA4BF", fontSize: 16, flexShrink: 0 }} />
                                                    <Tooltip title={l.name} placement="right" arrow>
                                                        <Typography
                                                            onClick={() => onSelect && onSelect(l._id)}
                                                            sx={{
                                                                color: theme.palette.custom.mainBlue,
                                                                fontSize: "14px",
                                                                textDecoration: "underline",
                                                                cursor: "pointer",
                                                                overflow: "hidden",
                                                                textOverflow: "ellipsis",
                                                                whiteSpace: "nowrap",
                                                                minWidth: 0,
                                                                // flex: 1,
                                                            }}
                                                        >
                                                            {l.name}
                                                        </Typography>
                                                    </Tooltip>
                                                    {selectedSaveList?.id === l._id && <Star sx={{ color: "#FFD700", fontSize: 16, flexShrink: 0 }} />}
                                                </Stack>
                                            </BodyCell>
                                            <BodyCell>
                                                {l._id === "favorites" ? "-" : `${l.shared_users_count || 0} user${l.shared_users_count !== 1 ? "s" : ""}`}
                                            </BodyCell>
                                            <BodyCell>{l.artifact_count || 0}</BodyCell>
                                            <BodyCell>{l.created_at ? new Date(l.created_at).toLocaleDateString() : "-"}</BodyCell>
                                            <BodyCell>{l.updated_at ? new Date(l.updated_at).toLocaleDateString() : "-"}</BodyCell>
                                            <BodyCell align="left">
                                                <Stack direction="row" spacing={1} justifyContent="flex-start">
                                                    <Tooltip title="Edit" placement="top">
                                                        <IconButton
                                                            size="small"
                                                            disabled={l._id === "favorites"}
                                                            onClick={() => handleEditClick(l)}
                                                            sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px" }}
                                                        >
                                                            <Edit sx={{ color: "#9AA4BF", fontSize: 16 }} />
                                                        </IconButton>
                                                    </Tooltip>
                                                    <Tooltip title="Share" placement="top">
                                                        <IconButton
                                                            size="small"
                                                            disabled={l._id === "favorites"}
                                                            onClick={() => handleShareClick(l)}
                                                            sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px" }}
                                                        >
                                                            <Share sx={{ color: "#9AA4BF", fontSize: 16 }} />
                                                        </IconButton>
                                                    </Tooltip>
                                                    <Tooltip title="Delete" placement="top">
                                                        <IconButton
                                                            size="small"
                                                            disabled={l._id === "favorites"}
                                                            onClick={() => handleDeleteClick(l, false)}
                                                            sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px" }}
                                                        >
                                                            <DeleteOutline sx={{ color: "#E60000", fontSize: 16 }} />
                                                        </IconButton>
                                                    </Tooltip>
                                                </Stack>
                                            </BodyCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    )}
                </Box>
            </Grid>
            <Grid item size={{ xs: 12 }} sx={{ flex: isMobile ? "none" : 1, minHeight: 0 }}>
                <Box sx={{ display: "flex", flexDirection: "column", height: isMobile ? "auto" : "100%" }}>
                    <Typography sx={{ color: "#FFFFFF", fontSize: "16px", mb: 1 }}>Lists shared with you</Typography>
                    {isMobile ? (
                        <Box>
                            {sharedWithYou.map((l) => renderListCard(l, true))}
                        </Box>
                    ) : (
                        <TableContainer sx={{ flex: 1, overflowY: "auto", borderRadius: 1, scrollbarGutter: "stable" }}>
                            <Table size="small" stickyHeader sx={{ background: "transparent", tableLayout: "fixed" }}>
                                <ColGroup />
                                <TableHead>
                                    <TableRow sx={{ "& .MuiTableCell-head": { color: theme.palette.custom.mainBlue, backgroundColor: theme.palette.custom.darkBlue, borderBottom: 0 } }}>
                                        <HeaderCell>List name</HeaderCell>
                                        <HeaderCell>Created by</HeaderCell>
                                        <HeaderCell>Artifacts</HeaderCell>
                                        <HeaderCell>Date Created</HeaderCell>
                                        <HeaderCell>Date Updated</HeaderCell>
                                        <HeaderCell align="right">Action</HeaderCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {sharedWithYou.map((l) => (
                                        <TableRow key={l._id} sx={{ "& .MuiTableCell-root": { borderBottom: 0 } }}>
                                            <BodyCell>
                                                <Stack direction="row" spacing={1} alignItems="center" sx={{ minWidth: 0, flex: 1 }}>
                                                    <img src="/icons/shared-list-icon.svg" alt="List" width={20} height={20} style={{ flexShrink: 0 }} />
                                                    <Tooltip title={l.name} placement="right" arrow>
                                                        <Typography
                                                            onClick={() => onSelect && onSelect(l._id)}
                                                            sx={{
                                                                color: theme.palette.custom.mainBlue,
                                                                fontSize: "14px",
                                                                textDecoration: "underline",
                                                                cursor: "pointer",
                                                                overflow: "hidden",
                                                                textOverflow: "ellipsis",
                                                                whiteSpace: "nowrap",
                                                                minWidth: 0,
                                                                // flex: 1,
                                                            }}
                                                        >
                                                            {l.name}
                                                        </Typography>
                                                    </Tooltip>
                                                    {selectedSaveList?.id === l._id && <Star sx={{ color: "#FFD700", fontSize: 16, flexShrink: 0 }} />}
                                                </Stack>
                                            </BodyCell>
                                            <BodyCell>{l.owner_name || "-"}</BodyCell>
                                            <BodyCell>{l.artifact_count || 0}</BodyCell>
                                            <BodyCell>{l.created_at ? new Date(l.created_at).toLocaleDateString() : "-"}</BodyCell>
                                            <BodyCell>{l.updated_at ? new Date(l.updated_at).toLocaleDateString() : "-"}</BodyCell>
                                            <BodyCell align="left">
                                                <Stack direction="row" spacing={1} justifyContent="flex-start">
                                                    <Tooltip title="Remove" placement="top">
                                                        <IconButton
                                                            size="small"
                                                            disabled={l.shared_with_organization}
                                                            onClick={() => handleDeleteClick(l, true)}
                                                            sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px" }}
                                                        >
                                                            <RemoveCircleOutlineOutlined sx={{ color: "#E60000", fontSize: 16 }} />
                                                        </IconButton>
                                                    </Tooltip>
                                                </Stack>
                                            </BodyCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </TableContainer>
                    )}
                </Box>
            </Grid>
            <ListModal
                open={showRenameModal}
                onClose={() => {
                    setShowRenameModal(false);
                    setEditingList(null);
                }}
                list={editingList}
                onSuccess={(updatedList) => {
                    if (onListUpdate) {
                        onListUpdate(updatedList);
                    }
                }}
            />
            <DeleteListModal
                open={showDeleteModal}
                onClose={() => {
                    setShowDeleteModal(false);
                    setDeletingList(null);
                    setIsSharedListDelete(false);
                }}
                list={deletingList}
                isSharedList={isSharedListDelete}
                user={user}
                onSuccess={() => {
                    if (deletingList && onListDelete) {
                        onListDelete(deletingList._id);
                    }
                }}
            />
            <ShareListModal
                open={showShareModal}
                onClose={() => {
                    setShowShareModal(false);
                    setSharingList(null);
                }}
                list={sharingList}
                user={user}
                onListShare={onListShare}
            />
        </Grid>
    );
}


