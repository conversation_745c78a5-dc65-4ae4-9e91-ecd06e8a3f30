import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import request from 'supertest';
import app from '../../server';
import User from '../../models/User';
import ApiKey from '../../models/ApiKey';
import { generateUserToken, authorizedUser, nonAuthorizedUser } from '../data/Auth';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import aisVesselFlagService from '../../services/AisVesselFlag.service';
import ioEmitter from '../../modules/ioEmitter';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../services/AisVesselFlag.service', () => require('../mocks/services/aisVesselFlag.mock'));
jest.mock('../../modules/ioEmitter', () => require('../mocks/modules/ioEmitter.mock'));

describe('VesselAis Flag API', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    describe('POST /api/vesselAis/flag', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validMmsi = '123456789';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/vesselAis/flag').send({ mmsi: validMmsi });
                    expect(res.status).toBe(401);
                });

                it('should return 400 when MMSI is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/vesselAis/flag').set('Authorization', authToken).send({});
                    expect(res.status).toBe(400);
                });

                it('should return 400 when MMSI is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/vesselAis/flag').set('Authorization', authToken).send({ mmsi: '' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when already flagged', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (aisVesselFlagService.flagAisVessel as jest.Mock).mockRejectedValueOnce(
                        new Error('You have already flagged this AIS vessel') as never
                    );
                    const res = await request(app)
                        .post('/api/vesselAis/flag')
                        .set('Authorization', authToken)
                        .send({ mmsi: validMmsi });
                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe('You have already flagged this AIS vessel');
                });

                it('should flag AIS vessel successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockFlag = { _id: 'flag123', mmsi: validMmsi, flaggedBy: userOrApiKey.authorized._id };
                    (aisVesselFlagService.flagAisVessel as jest.Mock).mockResolvedValueOnce(mockFlag as never);
                    const res = await request(app)
                        .post('/api/vesselAis/flag')
                        .set('Authorization', authToken)
                        .send({ mmsi: validMmsi });
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS vessel flagged successfully');
                    expect(res.body.flag).toEqual(mockFlag);
                });

                it('should return 500 when error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (aisVesselFlagService.flagAisVessel as jest.Mock).mockRejectedValueOnce(new Error('Database error') as never);
                    const res = await request(app)
                        .post('/api/vesselAis/flag')
                        .set('Authorization', authToken)
                        .send({ mmsi: validMmsi });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });

    describe('POST /api/vesselAis/unflag', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validMmsi = '123456789';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/vesselAis/unflag').send({ mmsi: validMmsi });
                    expect(res.status).toBe(401);
                });

                it('should return 400 when MMSI is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post('/api/vesselAis/unflag').set('Authorization', authToken).send({});
                    expect(res.status).toBe(400);
                });

                it('should return 404 when flag not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (aisVesselFlagService.unflagAisVessel as jest.Mock).mockRejectedValueOnce(new Error('Flag not found') as never);
                    const res = await request(app)
                        .post('/api/vesselAis/unflag')
                        .set('Authorization', authToken)
                        .send({ mmsi: validMmsi });
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Flag not found');
                });

                it('should unflag AIS vessel successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockFlag = { _id: 'flag123', mmsi: validMmsi };
                    (aisVesselFlagService.unflagAisVessel as jest.Mock).mockResolvedValueOnce(mockFlag as never);
                    const res = await request(app)
                        .post('/api/vesselAis/unflag')
                        .set('Authorization', authToken)
                        .send({ mmsi: validMmsi });
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS vessel unflagged successfully');
                    expect(res.body.flag).toEqual(mockFlag);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });

    describe('GET /api/vesselAis/flagged', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/vesselAis/flagged').query({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(401);
                });

                it('should return 400 when page is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/vesselAis/flagged')
                        .set('Authorization', authToken)
                        .query({ pageSize: 10 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when pageSize is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/vesselAis/flagged')
                        .set('Authorization', authToken)
                        .query({ page: 1 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when page is less than 1', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/vesselAis/flagged')
                        .set('Authorization', authToken)
                        .query({ page: 0, pageSize: 10 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when pageSize is greater than 100', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/vesselAis/flagged')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 101 });
                    expect(res.status).toBe(400);
                });

                it('should return flagged vessels successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = {
                        vessels: [
                            {
                                _id: 'vessel1',
                                mmsi: '123456789',
                                flagCount: 1,
                                flags: [],
                            },
                        ],
                        totalCount: 1,
                        page: 1,
                        pageSize: 10,
                    };
                    (aisVesselFlagService.getFlaggedAisVessels as jest.Mock).mockResolvedValueOnce(mockResult as never);
                    const res = await request(app)
                        .get('/api/vesselAis/flagged')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(200);
                    expect(res.body).toEqual(mockResult);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });

    describe('GET /api/vesselAis/flagged/user', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/vesselAis/flagged/user');
                    expect(res.status).toBe(401);
                });

                it('should return user flagged MMSIs successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockMmsis = ['123456789', '987654321'];
                    (aisVesselFlagService.getUserFlaggedAisVesselMmsis as jest.Mock).mockResolvedValueOnce(mockMmsis as never);
                    const res = await request(app).get('/api/vesselAis/flagged/user').set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.flaggedMmsis).toEqual(mockMmsis);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });

    describe('DELETE /api/vesselAis/flagged/:mmsi', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validMmsi = '123456789';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete(`/api/vesselAis/flagged/${validMmsi}`);
                    expect(res.status).toBe(401);
                });

                it('should return 400 when MMSI is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).delete('/api/vesselAis/flagged/').set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should remove all flags successfully', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = { deletedCount: 3 };
                    (aisVesselFlagService.removeAllFlagsFromAisVessel as jest.Mock).mockResolvedValueOnce(mockResult as never);
                    const res = await request(app)
                        .delete(`/api/vesselAis/flagged/${validMmsi}`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('All flags removed from AIS vessel successfully');
                    expect(res.body.deletedCount).toBe(3);
                    expect(ioEmitter.emit).toHaveBeenCalledWith('notifyAll', { name: 'ais_vessels_flagged/changed' });
                });

                it('should not emit socket event when no flags deleted', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = { deletedCount: 0 };
                    (aisVesselFlagService.removeAllFlagsFromAisVessel as jest.Mock).mockResolvedValueOnce(mockResult as never);
                    const res = await request(app)
                        .delete(`/api/vesselAis/flagged/${validMmsi}`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                    expect(ioEmitter.emit).not.toHaveBeenCalled();
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });
});

