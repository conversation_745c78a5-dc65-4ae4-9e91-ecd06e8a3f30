import { useEffect, useState, useRef, useCallback, memo } from "react";
import { Grid, CircularProgress, Typography } from "@mui/material";
import { DirectionsBoat } from "@mui/icons-material";
import aisVesselFlagController from "../../../controllers/AisVesselFlag.controller";
import { getSocket } from "../../../socket";
import theme from "../../../theme";
import VirtualizedCardList from "./VirtualizedCardList";
import FlaggedAISCard from "./FlaggedAISCard";
import { useApp } from "../../../hooks/AppHook";
import AISDetailModal from "./AISDetailModal";

const FlaggedAIS = () => {
    const [flaggedVessels, setFlaggedVessels] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedVessel, setSelectedVessel] = useState(null);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const { isMobile } = useApp();
    const containerRef = useRef();
    const listRef = useRef();
    const hasMore = useRef(true);
    const pageRef = useRef(1);
    const loadMoreTimeoutRef = useRef();
    const isLoadingMoreRef = useRef(false);
    const totalLoadedRef = useRef(0);
    const [dataVersion, setDataVersion] = useState(0);

    const fetchFlaggedAisVessels = useCallback(async (isSocketTriggered = false, isLoadMore = false) => {
        if (isLoadMore && isLoadingMoreRef.current) {
            return;
        }

        if (isLoadMore && !hasMore.current) {
            return;
        }

        setLoading(true);

        if (isLoadMore) {
            isLoadingMoreRef.current = true;
        }

        try {
            const currentPage = isLoadMore ? pageRef.current + 1 : 1;
            const pageSize = 30;
            const response = await aisVesselFlagController.getFlaggedAisVessels(currentPage, pageSize);
            const { vessels, totalCount, page: responsePage, pageSize: responsePageSize } = response;

            if (vessels.length === 0) {
                if (isLoadMore) {
                    hasMore.current = false;
                } else {
                    setFlaggedVessels([]);
                    pageRef.current = 1;
                    totalLoadedRef.current = 0;
                    hasMore.current = false;
                }
                setLoading(false);
                isLoadingMoreRef.current = false;
                return;
            }
            if (isLoadMore) {
                setFlaggedVessels((prev) => {
                    const newList = [...prev, ...vessels];
                    totalLoadedRef.current = newList.length;
                    return newList;
                });
                pageRef.current = responsePage || currentPage;
            } else {
                setFlaggedVessels([...vessels]);
                pageRef.current = responsePage || 1;
                totalLoadedRef.current = vessels.length;
                setDataVersion((prev) => prev + 1);

                if (isSocketTriggered) {
                    aisVesselFlagController.getUserFlaggedAisVesselMmsis().catch(console.error);
                }
            }

            const gotFullPage = vessels.length >= responsePageSize;
            const currentTotal = isLoadMore ? totalLoadedRef.current : vessels.length;
            hasMore.current = gotFullPage && currentTotal < totalCount;

            setLoading(false);
            isLoadingMoreRef.current = false;
        } catch (error) {
            console.error("Error fetching flagged AIS vessels:", error);
            setLoading(false);
            isLoadingMoreRef.current = false;
        }
    }, []);

    const handleLoadMore = useCallback(() => {
        if (loading || !hasMore.current || isLoadingMoreRef.current) {
            return;
        }

        if (loadMoreTimeoutRef.current) {
            clearTimeout(loadMoreTimeoutRef.current);
        }

        loadMoreTimeoutRef.current = setTimeout(() => {
            fetchFlaggedAisVessels(false, true);
        }, 500);
    }, [loading, fetchFlaggedAisVessels]);

    useEffect(() => {
        fetchFlaggedAisVessels();
        const socket = getSocket();

        const handleFlagChanged = () => {
            fetchFlaggedAisVessels(true, false);
        };

        socket.on("ais_vessels_flagged/changed", handleFlagChanged);

        return () => {
            socket.off("ais_vessels_flagged/changed", handleFlagChanged);
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }
        };
    }, [fetchFlaggedAisVessels]);

    return (
        <>
            {loading && flaggedVessels.length === 0 ? (
                <Grid
                    container
                    display={"flex"}
                    justifyContent={"center"}
                    alignItems={"center"}
                    height={{ xs: "90%", sm: "90%" }}
                    overflow={"auto"}
                    marginBottom={2}
                    size="grow"
                >
                    <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
                </Grid>
            ) : flaggedVessels.length === 0 ? (
                <Grid
                    container
                    padding={6}
                    justifyContent={"center"}
                    alignItems={"center"}
                    flexDirection={"column"}
                    gap={2}
                    size="grow"
                >
                    <DirectionsBoat sx={{ fontSize: "64px", color: theme.palette.custom.mainBlue, opacity: 0.5 }} />
                    <Typography fontSize={"18px"} fontWeight={500} color="#FFFFFF">
                        No flagged AIS vessels found
                    </Typography>
                    <Typography fontSize={"14px"} color={theme.palette.custom.mainBlue} textAlign={"center"}>
                        Flag AIS vessels from the map to see them here
                    </Typography>
                </Grid>
            ) : (
                <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
                    <Grid
                        container
                        overflow={"auto"}
                        display={"block"}
                        border={`1px solid ${theme.palette.custom.borderColor}`}
                        borderRadius={"12px"}
                        padding={"10px 24px"}
                        size="grow"
                        sx={{
                            backgroundColor: theme.palette.custom.darkBlue,
                        }}
                    >
                        <Grid container height={"100%"} overflow={"hidden"} ref={containerRef}>
                            <VirtualizedCardList
                                key={`flagged-ais-${dataVersion}`}
                                ref={listRef}
                                events={flaggedVessels}
                                isLoading={loading}
                                onLoadMore={handleLoadMore}
                                hasMore={hasMore.current}
                                containerRef={containerRef}
                                setShowDetailModal={setShowDetailModal}
                                setSelectedCard={setSelectedVessel}
                                CustomCard={FlaggedAISCard}
                                itemHeight={isMobile ? 150 : 160}
                            />
                        </Grid>
                    </Grid>
                </Grid>
            )}
            <AISDetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedVessel={selectedVessel}
            />
        </>
    );
};

export default memo(FlaggedAIS);

