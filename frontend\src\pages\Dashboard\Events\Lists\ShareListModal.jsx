import { <PERSON>alog, DialogTitle, DialogContent, DialogActions, Button, IconButton, Typography, Box, TextField, Stack, alpha, Checkbox, FormControlLabel, Pop<PERSON> } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import LaunchIcon from "@mui/icons-material/Launch";
import { useState, useEffect, useRef } from "react";
import theme from "../../../../theme";
import listsController from "../../../../controllers/Lists.controller";
import axiosInstance from "../../../../axios";
import { useUser } from "../../../../hooks/UserHook";

export default function ShareListModal({ open, onClose, list, onListShare }) {
    const { user } = useUser();
    const [recipients, setRecipients] = useState([]);
    const [newRecipient, setNewRecipient] = useState({ value: "", isExternal: false, userId: null, name: null, email: null, error: "" });
    const [suggestions, setSuggestions] = useState([]);
    // const [activeRecipientIndex, setActiveRecipientIndex] = useState(0);
    const [shareWithOrganization, setShareWithOrganization] = useState(false);
    const [loading, setLoading] = useState(false);
    const timeoutRef = useRef(null);
    const [initialShareWithOrganization, setInitialShareWithOrganization] = useState(false);
    const [removeRecipientIds, setRemoveRecipientIds] = useState([]);
    // const textFieldRefs = useRef({});
    const newRecipientRef = useRef(null);

    useEffect(() => {
        if (open && list) {
            const initialOrgShare = list.shared_with_organization || false;
            setRecipients([]);
            setSuggestions([]);
            // setActiveRecipientIndex(0);
            setShareWithOrganization(initialOrgShare);
            setInitialShareWithOrganization(initialOrgShare);
            fetchExistingSharedUsers();
        }
    }, [open, list]);

    const fetchExistingSharedUsers = async () => {
        if (!list?._id) return;
        try {
            const users = await listsController.getSharedUsers({ listId: list._id });

            const existingRecipients = (users || []).map((user) => ({
                value: `${user.name} - ${user.email}`,
                isExternal: false,
                userId: user._id,
                name: user.name,
                email: user.email,
                error: "",
                isExisting: true,
            }));

            setRecipients((prev) => {
                const newRecipients = prev.filter((r) => !r.isExisting);
                return [...newRecipients, ...existingRecipients];
            });
        } catch (err) {
            console.error("Error fetching shared users", err);
        }
    };

    const searchUsers = async (query) => {
        if (!user?._id) return;
        const trimmedQuery = query?.trim();
        if (!trimmedQuery || trimmedQuery.length < 2) {
            setSuggestions([]);
            return;
        }
        try {
            const res = await axiosInstance.get(`/users/suggest`, { params: { q: trimmedQuery } }, { meta: { showSnackbar: false } });
            setSuggestions(res.data.filter((iUser) => iUser._id !== user._id && !recipients.some((recipient) => recipient.userId === iUser._id)));
        } catch (err) {
            console.error("Error searching users", err);
            setSuggestions([]);
        }
    };

    const handleInputChange = (value) => {
        // setActiveRecipientIndex(index);
        // const newRecipients = [...recipients];
        // const currentRecipient = newRecipients[index];
        // newRecipients[index] = { ...currentRecipient, value, error: "" };
        // setRecipients(newRecipients);

        if (timeoutRef.current) clearTimeout(timeoutRef.current);

        const trimmedValue = value.trim();

        if (!trimmedValue) {
            setSuggestions([]);
            setNewRecipient({ value: "", isExternal: false, userId: null, name: null, email: null, error: "" });
            return;
        }

        const isEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmedValue);
        if (isEmail) {
            setNewRecipient({ value: trimmedValue, isExternal: true, userId: null, name: null, email: trimmedValue, error: "" });
            setSuggestions([]);
        } else {
            setNewRecipient({ value: trimmedValue, isExternal: false, userId: null, name: null, email: null, error: "" });
        }

        timeoutRef.current = setTimeout(() => searchUsers(trimmedValue), 300);
    };

    const handleSelectUser = (selectedUser) => {
        // const targetIndex = index !== undefined ? index : activeRecipientIndex;
        // if (targetIndex === null || targetIndex === undefined) return;
        setRecipients(existing => [{
            value: `${selectedUser.name} - ${selectedUser.email}`,
            isExternal: false,
            userId: selectedUser._id,
            name: selectedUser.name,
            email: selectedUser.email,
            error: "",
        }, ...existing]);
        setSuggestions([]);
        clearNewRecipient();
    };

    const clearNewRecipient = () => {
        setNewRecipient({ value: "", isExternal: false, userId: null, name: null, email: null, error: "" });
    };

    const handleAddRecipient = () => {
        if (newRecipient.value.trim() === "" || (!newRecipient.userId && !newRecipient.email)) {
            setNewRecipient({ ...newRecipient, error: "Please select a user from suggestions or enter a valid email address" });
            return;
        }
        setRecipients(existing => [newRecipient, ...existing]);
        clearNewRecipient();
    };

    const handleRemoveRecipient = (index) => {
        const target = recipients[index];
        if (!target) throw new Error("[handleRemoveRecipient] Recipient at index does not exist");
        if (target.isExisting) {
            const userId = target.userId;
            if (!userId) throw new Error("[handleRemoveRecipient] User ID is required");
            setRemoveRecipientIds(prev => [...prev, userId]);
        }
        setRecipients(prev => prev.filter((_, i) => i !== index));
    };

    const validateEmail = (email) => {
        return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
    };

    const hasChanges = () => {
        if (shareWithOrganization !== initialShareWithOrganization) {
            return true;
        }

        if (removeRecipientIds.length > 0) {
            return true;
        }

        const newRecipients = recipients.filter((r) => !r.isExisting);

        const hasEmptyFields = newRecipients.some((r) => r.value.trim() === "");
        if (hasEmptyFields) {
            return false;
        }

        if (newRecipients.length === 0) {
            return false;
        }

        const allValid = newRecipients.every((r) => {
            if (r.isExternal) {
                return validateEmail(r.value.trim());
            } else {
                return !!r.userId;
            }
        });

        return allValid;
    };

    const handleSubmit = async () => {
        const newRecipients = [...recipients];
        let hasErrors = false;

        recipients.forEach((recipient, index) => {
            if (recipient.isExisting) {
                return;
            }

            if (recipient.value.trim() === "") {
                newRecipients[index].error = "";
                return;
            }

            if (recipient.isExternal) {
                if (!validateEmail(recipient.value.trim())) {
                    newRecipients[index].error = "Enter a valid email address";
                    hasErrors = true;
                } else {
                    newRecipients[index].error = "";
                }
            } else {
                if (!recipient.userId) {
                    newRecipients[index].error = "Please select a user from suggestions";
                    hasErrors = true;
                } else {
                    newRecipients[index].error = "";
                }
            }
        });

        setRecipients(newRecipients);

        if (hasErrors) {
            return;
        }

        const validRecipients = recipients.filter((r) => !r.isExisting && r.value.trim() !== "");

        const userIds = new Map();
        const emails = new Map();
        let duplicateFound = false;

        recipients.forEach((recipient, originalIndex) => {
            if (recipient.isExisting) return;
            if (recipient.value.trim() === "") return;

            if (recipient.isExternal) {
                const email = recipient.email || recipient.value.trim().toLowerCase();
                if (emails.has(email)) {
                    emails.get(email).forEach(idx => {
                        newRecipients[idx].error = "This recipient is already added";
                    });
                    newRecipients[originalIndex].error = "This recipient is already added";
                    emails.get(email).push(originalIndex);
                    duplicateFound = true;
                } else {
                    emails.set(email, [originalIndex]);
                }
            } else if (recipient.userId) {
                if (userIds.has(recipient.userId)) {
                    userIds.get(recipient.userId).forEach(idx => {
                        newRecipients[idx].error = "This recipient is already added";
                    });
                    newRecipients[originalIndex].error = "This recipient is already added";
                    userIds.get(recipient.userId).push(originalIndex);
                    duplicateFound = true;
                } else {
                    userIds.set(recipient.userId, [originalIndex]);
                }
            }
        });

        if (duplicateFound) {
            setRecipients(newRecipients);
            return;
        }

        setRecipients(newRecipients);
        const needsOrgUpdate = shareWithOrganization !== (list?.shared_with_organization || false);
        const hasUserRecipients = validRecipients.length > 0;

        if (!needsOrgUpdate && !hasUserRecipients && removeRecipientIds.length === 0) {
            const firstNewRecipientIndex = recipients.findIndex((r) => !r.isExisting);
            if (firstNewRecipientIndex >= 0) {
                newRecipients[firstNewRecipientIndex].error = `Please add at least one recipient or ${shareWithOrganization ? "uncheck" : "check"} organization sharing`;
                setRecipients(newRecipients);
            }
            return;
        }

        setLoading(true);

        try {
            if (needsOrgUpdate) {
                await listsController.updateList({ listId: list._id, sharedWithOrganization: shareWithOrganization });
            }

            let currentShareCount = list.shared_users_count || 0;

            if (removeRecipientIds.length > 0) {
                const removePromises = removeRecipientIds.map(async (userId) => {
                    // const originalIndex = recipients.findIndex((r) => r.userId === userId);

                    try {
                        await listsController.removeFromSharedList({ listId: list._id, userId });
                        return { success: true };
                    } catch (err) {
                        // let errorMessage = err?.message || "Failed to remove access";
                        // const unexpectedErrorPrefix = /^Unexpected error (occurred|occured):\s*/i;
                        // if (unexpectedErrorPrefix.test(errorMessage)) {
                        //     errorMessage = errorMessage.replace(unexpectedErrorPrefix, "").trim();
                        // }
                        // return { success: false, index: originalIndex, error: errorMessage };
                        console.error("Error removing recipient from shared list", err);
                        return { success: false };
                    }
                });

                const results = await Promise.allSettled(removePromises);

                results.forEach((result) => {
                    if (result.status === 'fulfilled') {
                        const { success } = result.value;
                        if (success) {
                            currentShareCount--;
                        }
                    }
                });
            }

            if (hasUserRecipients) {
                const recipientIndexMap = new Map();
                recipients.forEach((r, idx) => {
                    if (!r.isExisting && r.value.trim() !== "") {
                        const key = r.isExternal
                            ? `external:${r.email || r.value.trim().toLowerCase()}`
                            : `internal:${r.userId}`;
                        recipientIndexMap.set(key, idx);
                    }
                });

                const sharePromises = validRecipients.map(async (recipient) => {
                    const key = recipient.isExternal
                        ? `external:${recipient.email || recipient.value.trim().toLowerCase()}`
                        : `internal:${recipient.userId}`;
                    const originalIndex = recipientIndexMap.get(key);

                    try {
                        if (recipient.isExternal) {
                            await listsController.shareWithEmail({ listId: list._id, email: recipient.email || recipient.value.trim() });
                        } else {
                            await listsController.shareWithUser({ listId: list._id, userId: recipient.userId });
                        }

                        return { success: true, index: originalIndex, recipient };
                    } catch (err) {
                        let errorMessage = err?.message || "Failed to share list";
                        const unexpectedErrorPrefix = /^Unexpected error (occurred|occured):\s*/i;
                        if (unexpectedErrorPrefix.test(errorMessage)) {
                            errorMessage = errorMessage.replace(unexpectedErrorPrefix, "").trim();
                        }
                        return { success: false, index: originalIndex, error: errorMessage, recipient };
                    }
                });

                const results = await Promise.allSettled(sharePromises);

                let updatedRecipients = [...recipients];
                let hasErrors = false;

                results.forEach((result) => {
                    if (result.status === 'fulfilled') {
                        const { success, index, error } = result.value;
                        if (success && index >= 0) {
                            updatedRecipients[index] = {
                                ...updatedRecipients[index],
                                isExisting: true,
                                error: "",
                            };
                            currentShareCount++;
                        } else if (!success && index >= 0) {
                            updatedRecipients[index] = {
                                ...updatedRecipients[index],
                                error: error || "Failed to share list",
                            };
                            hasErrors = true;
                        }
                    }
                });

                setRecipients(updatedRecipients);

                onListShare(list._id, currentShareCount);

                await fetchExistingSharedUsers();

                if (!hasErrors) {
                    handleClose();
                }
            } else {
                await fetchExistingSharedUsers();
                handleClose();
                onListShare(list._id, currentShareCount);
            }
        } catch (err) {
            console.error("Unexpected error in handleSubmit", err);
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setRecipients([]);
        clearNewRecipient();
        setSuggestions([]);
        setShareWithOrganization(false);
        setRemoveRecipientIds([]);
        onClose();
    };

    if (!list) return null;

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            maxWidth="sm"
            fullWidth
            PaperProps={{
                sx: {
                    backgroundColor: theme.palette.custom.darkBlue,
                    borderRadius: "12px",
                    border: `1px solid ${theme.palette.custom.borderColor}`,
                    overflow: "visible !important",
                    position: "relative",
                    maxHeight: "none !important",
                },
            }}
            sx={{
                "& .MuiDialog-container": {
                    overflow: "visible !important",
                },
                "& .MuiDialog-paper": {
                    overflow: "visible !important",
                    maxHeight: "none !important",
                },
                "& .MuiDialogContent-root": {
                    overflow: "visible !important",
                    maxHeight: "none !important",
                },
            }}
        >
            <DialogTitle sx={{ color: "#FFFFFF", fontWeight: 600, fontSize: "18px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                Share this list
                <IconButton onClick={handleClose} size="small" sx={{ color: "#FFFFFF" }}>
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent
                sx={{
                    padding: "24px",
                    paddingBottom: "0px",
                    overflow: "visible",
                    position: "relative",
                    "&.MuiDialogContent-root": {
                        overflow: "visible !important",
                    },
                }}
            >
                <FormControlLabel
                    control={
                        <Checkbox
                            checked={shareWithOrganization}
                            onChange={(e) => setShareWithOrganization(e.target.checked)}
                            sx={{
                                color: "#FFFFFF",
                                "&.Mui-checked": {
                                    color: theme.palette.custom.mainBlue,
                                },
                            }}
                        />
                    }
                    label={
                        <Typography sx={{ color: "#FFFFFF", fontSize: "14px" }}>
                            Share with your organization
                        </Typography>
                    }
                    sx={{ mb: 2 }}
                />
                <Typography sx={{ color: "#FFFFFF", fontSize: "14px", mb: 2 }}>
                    To share with a user from your org, enter their name or email address. To share with an external user, enter their email address.
                </Typography>
                <Box
                    ref={newRecipientRef}
                    sx={{ mb: 2, position: "relative" }}
                >
                    <Box sx={{ display: "flex", gap: 1 }}>
                        <TextField
                            fullWidth
                            value={newRecipient.value}
                            onChange={(e) => handleInputChange(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === "Enter") {
                                    handleAddRecipient();
                                }
                            }}
                            // onFocus={() => setActiveRecipientIndex(index)}
                            // onBlur={() => {
                            //     if (!recipient.isExisting) {
                            //         setTimeout(() => setActiveRecipientIndex(null), 200);
                            //     }
                            // }}
                            placeholder="User's name or email"
                            // disabled={recipient.isExisting}
                            // error={!!recipient.error}
                            sx={{
                                "& .MuiOutlinedInput-root": {
                                    backgroundColor: theme.palette.custom.darkBlue,
                                    color: "#FFFFFF",
                                    paddingRight: newRecipient.value ? "40px" : "14px",
                                    borderRadius: "8px",
                                    "& fieldset": {
                                        borderColor: newRecipient.error ? "#E60000" : theme.palette.custom.borderColor,
                                        borderRadius: "8px",
                                    },
                                    "&:hover fieldset": {
                                        borderColor: newRecipient.error ? "#E60000" : theme.palette.custom.borderColor,
                                    },
                                    "&.Mui-focused fieldset": {
                                        borderColor: newRecipient.error ? "#E60000" : theme.palette.custom.mainBlue,
                                    },
                                    "&.Mui-error fieldset": {
                                        borderColor: "#E60000",
                                    },
                                    "& .Mui-disabled": {
                                        color: "#ffffff",
                                        WebkitTextFillColor: "rgb(255, 255, 255, 0.7) !important",
                                    },
                                },
                                "& .MuiInputBase-input": {
                                    color: "#FFFFFF",
                                    padding: "12px 14px",
                                    fontSize: "14px",
                                },
                                "& .MuiInputBase-input::placeholder": {
                                    color: "#9AA4BF",
                                    opacity: 1,
                                },
                            }}
                            InputProps={{
                                startAdornment: newRecipient.isExternal && newRecipient.value ? (
                                    <LaunchIcon sx={{ color: "#9AA4BF", fontSize: 16, mr: 1 }} />
                                ) : null,
                            }}
                        />
                        {newRecipient.email &&
                            <IconButton
                                size="small"
                                onClick={handleAddRecipient}
                                sx={{
                                    mt: 0.5,
                                    color: "#FFFFFF",
                                    border: `1px solid ${theme.palette.custom.borderColor}`,
                                    borderRadius: "8px",
                                    padding: "8px",
                                    minWidth: "36px",
                                    height: "36px",
                                    "&:hover": {
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                }}
                            >
                                <AddIcon sx={{ fontSize: "16px" }} />
                            </IconButton>}
                    </Box>
                    <Popper
                        open={suggestions.length > 0}
                        anchorEl={newRecipientRef.current}
                        placement="bottom-start"
                        style={{ zIndex: 1500 }}
                        modifiers={[
                            {
                                name: "offset",
                                options: {
                                    offset: [0, 4],
                                },
                            },
                        ]}
                    >
                        <Box
                            sx={{
                                backgroundColor: theme.palette.custom.borderColor,
                                border: `1px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                maxHeight: "200px",
                                overflow: "hidden",
                                boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.3)",
                                display: "flex",
                                flexDirection: "column",
                                width: newRecipientRef.current?.offsetWidth || "100%",
                            }}
                        >
                            <Box
                                sx={{
                                    maxHeight: "200px",
                                    overflowY: "auto",
                                    overflowX: "hidden",
                                    "&::-webkit-scrollbar": {
                                        width: "6px",
                                    },
                                    "&::-webkit-scrollbar-track": {
                                        backgroundColor: theme.palette.custom.darkBlue,
                                        borderRadius: "8px",
                                    },
                                    "&::-webkit-scrollbar-thumb": {
                                        backgroundColor: theme.palette.custom.borderColor,
                                        borderRadius: "3px",
                                        "&:hover": {
                                            backgroundColor: theme.palette.custom.mainBlue,
                                        },
                                    },
                                }}
                            >
                                {suggestions.map((suggestion) => (
                                    <Box
                                        key={suggestion._id}
                                        onMouseDown={(e) => {
                                            e.preventDefault();
                                            handleSelectUser(suggestion);
                                        }}
                                        sx={{
                                            padding: "12px 16px",
                                            cursor: "pointer",
                                            transition: "background-color 0.2s ease-in-out",
                                            "&:hover": {
                                                backgroundColor: alpha(theme.palette.custom.darkBlue, 0.3),
                                            },
                                            "&:first-of-type": {
                                                borderTopLeftRadius: "8px",
                                                borderTopRightRadius: "8px",
                                            },
                                            "&:last-of-type": {
                                                borderBottomLeftRadius: "8px",
                                                borderBottomRightRadius: "8px",
                                            },
                                        }}
                                    >
                                        <Typography sx={{ color: "#FFFFFF", fontSize: "14px", whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}>
                                            {suggestion.name} - {suggestion.email}
                                        </Typography>
                                    </Box>
                                ))}
                            </Box>
                        </Box>
                    </Popper>
                    {newRecipient.error && (
                        <Typography sx={{ color: "#E60000", fontSize: "12px", mt: 0.5, ml: 1 }}>
                            {newRecipient.error}
                        </Typography>
                    )}
                </Box>
                <Box sx={{ maxHeight: "300px", overflowY: "auto" }}>
                    {recipients.map((recipient, index) => (
                        <Box key={index} sx={{ mb: 2, position: "relative", overflow: "visible" }}>
                            <Box sx={{ display: "flex", gap: 1, alignItems: "flex-start" }}>
                                <Box
                                    // ref={(el) => {
                                    //     if (el) {
                                    //         textFieldRefs.current[index] = el;
                                    //     }
                                    // }}
                                    sx={{ flex: 1, position: "relative", overflow: "visible" }}
                                >
                                    <TextField
                                        fullWidth
                                        value={recipient.value}
                                        // onFocus={() => !recipient.isExisting && setActiveRecipientIndex(index)}
                                        // onBlur={() => {
                                        //     if (!recipient.isExisting) {
                                        //         setTimeout(() => setActiveRecipientIndex(null), 200);
                                        //     }
                                        // }}
                                        placeholder="User's name or email"
                                        disabled
                                        error={!!recipient.error}
                                        sx={{
                                            "& .MuiOutlinedInput-root": {
                                                backgroundColor: recipient.isExisting ? theme.palette.custom.cardBackground : theme.palette.custom.darkBlue,
                                                color: recipient.isExisting ? "#9AA4BF" : "#FFFFFF",
                                                paddingRight: recipient.value ? "40px" : "14px",
                                                borderRadius: "8px",
                                                "& fieldset": {
                                                    borderColor: recipient.error ? "#E60000" : theme.palette.custom.borderColor,
                                                    borderRadius: "8px",
                                                },
                                                "&:hover fieldset": {
                                                    borderColor: recipient.error ? "#E60000" : theme.palette.custom.borderColor,
                                                },
                                                "&.Mui-focused fieldset": {
                                                    borderColor: recipient.error ? "#E60000" : theme.palette.custom.mainBlue,
                                                },
                                                "&.Mui-error fieldset": {
                                                    borderColor: "#E60000",
                                                },
                                                "& .Mui-disabled": {
                                                    color: "#ffffff",
                                                    WebkitTextFillColor: "rgb(255, 255, 255, 0.7) !important",
                                                },
                                            },
                                            "& .MuiInputBase-input": {
                                                color: "#FFFFFF",
                                                padding: "12px 14px",
                                                fontSize: "14px",
                                            },
                                            "& .MuiInputBase-input::placeholder": {
                                                color: "#9AA4BF",
                                                opacity: 1,
                                            },
                                        }}
                                        InputProps={{
                                            startAdornment: recipient.isExternal && recipient.value ? (
                                                <LaunchIcon sx={{ color: "#9AA4BF", fontSize: 16, mr: 1 }} />
                                            ) : null,
                                        }}
                                    />
                                    {recipient.error && (
                                        <Typography sx={{ color: "#E60000", fontSize: "12px", mt: 0.5, ml: 1 }}>
                                            {recipient.error}
                                        </Typography>
                                    )}
                                </Box>
                                {/* <Stack direction="row" spacing={0.5} sx={{ mt: 0.5 }}> */}
                                <Box sx={{ mt: 0.5 }}>
                                    <IconButton
                                        size="small"
                                        onClick={() => handleRemoveRecipient(index)}
                                        sx={{
                                            color: "#FFFFFF",
                                            border: `1px solid ${theme.palette.custom.borderColor}`,
                                            borderRadius: "8px",
                                            padding: "8px",
                                            minWidth: "36px",
                                            height: "36px",
                                            "&:hover": {
                                                backgroundColor: theme.palette.custom.mainBlue,
                                            },
                                        }}
                                    >
                                        <RemoveIcon sx={{ fontSize: "16px" }} />
                                    </IconButton>
                                </Box>
                                {/* {!recipient.isExisting && (() => {
                                        const newRecipientsIndices = recipients
                                            .map((r, idx) => ({ r, idx }))
                                            .filter(({ r }) => !r.isExisting)
                                            .map(({ idx }) => idx);
                                        return newRecipientsIndices[newRecipientsIndices.length - 1] === index;
                                    })() && (
                                            <IconButton
                                                size="small"
                                                onClick={handleAddRecipient}
                                                sx={{
                                                    color: "#FFFFFF",
                                                    border: `1px solid ${theme.palette.custom.borderColor}`,
                                                    borderRadius: "8px",
                                                    padding: "8px",
                                                    minWidth: "36px",
                                                    height: "36px",
                                                    "&:hover": {
                                                        backgroundColor: theme.palette.custom.mainBlue,
                                                    },
                                                }}
                                            >
                                                <AddIcon sx={{ fontSize: "16px" }} />
                                            </IconButton>
                                        )} */}
                                {/* </Stack> */}
                            </Box>
                        </Box>
                    ))}
                </Box>
            </DialogContent>
            <DialogActions sx={{ p: "16px 24px", gap: 1, display: "flex", justifyContent: "center" }}>
                <Button
                    onClick={handleClose}
                    variant="outlined"
                    disabled={loading}
                    sx={{
                        color: "#FFFFFF",
                        borderColor: theme.palette.custom.borderColor,
                        backgroundColor: "transparent",
                        borderRadius: "8px",
                        textTransform: "none",
                        padding: "10px 20px",
                    }}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleSubmit}
                    variant="contained"
                    disabled={loading || !hasChanges()}
                    sx={{
                        backgroundColor: loading || !hasChanges() ? theme.palette.custom.borderColor : theme.palette.custom.mainBlue,
                        color: "#FFFFFF",
                        borderRadius: "8px",
                        textTransform: "none",
                        padding: "10px 20px",
                    }}
                >
                    Submit
                </Button>
            </DialogActions>
        </Dialog>
    );
}

