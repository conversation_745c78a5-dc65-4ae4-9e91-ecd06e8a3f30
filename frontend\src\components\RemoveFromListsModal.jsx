import { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, IconButton, Checkbox, FormControlLabel, Box, Stack, CircularProgress, alpha } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useState, useEffect, useMemo } from "react";
import theme from "../theme";
import listsController from "../controllers/Lists.controller";

export default function RemoveFromListsModal({ open, onClose, artifactId, listIds = [], isInFavorites = false, onConfirm }) {
    const [lists, setLists] = useState([]);
    const [selectedLists, setSelectedLists] = useState(new Set());
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);

    const allListItems = useMemo(() => {
        const items = [];
        if (isInFavorites) {
            items.push({ id: "favorites", name: "<PERSON><PERSON>", isFavorite: true });
        }
        lists.forEach((list) => {
            items.push({ id: String(list._id), name: list.name, isFavorite: false });
        });
        return items;
    }, [lists, isInFavorites]);

    const selectAllState = useMemo(() => {
        if (allListItems.length === 0) return { checked: false, indeterminate: false };
        const selectedCount = selectedLists.size;
        if (selectedCount === 0) return { checked: false, indeterminate: false };
        if (selectedCount === allListItems.length) return { checked: true, indeterminate: false };
        return { checked: false, indeterminate: true };
    }, [selectedLists, allListItems]);

    const statusMessage = useMemo(() => {
        const selectedCount = selectedLists.size;
        const totalCount = allListItems.length;
        if (selectedCount === 0) return null;
        return `Artifact will be removed from ${selectedCount} of ${totalCount} lists`;
    }, [selectedLists, allListItems]);

    useEffect(() => {
        if (open && (listIds.length > 0 || isInFavorites)) {
            fetchListDetails();
            const initialSelected = new Set();
            if (isInFavorites) {
                initialSelected.add("favorites");
            }
            listIds.forEach((id) => initialSelected.add(String(id)));
            setSelectedLists(initialSelected);
        } else {
            setLists([]);
            setSelectedLists(new Set());
        }
    }, [open, listIds, isInFavorites]);

    const fetchListDetails = async () => {
        setLoading(true);
        try {
            const allLists = await listsController.fetchLists();
            const filteredLists = allLists.filter((list) => listIds.includes(String(list._id)));
            setLists(filteredLists);
        } catch (error) {
            console.error("Error fetching list details", error);
            setLists([]);
        } finally {
            setLoading(false);
        }
    };

    const handleToggleList = (listId) => {
        setSelectedLists((prev) => {
            const newSet = new Set(prev);
            if (newSet.has(listId)) {
                newSet.delete(listId);
            } else {
                newSet.add(listId);
            }
            return newSet;
        });
    };

    const handleSelectAll = () => {
        if (selectAllState.checked) {
            setSelectedLists(new Set());
        } else {
            const allIds = allListItems.map((item) => item.id);
            setSelectedLists(new Set(allIds));
        }
    };

    const handleSubmit = async (e) => {
        e.stopPropagation();
        e.preventDefault();
        if (selectedLists.size === 0) {
            return;
        }
        setSubmitting(true);
        try {
            const listIdsArray = Array.from(selectedLists);
            await onConfirm(listIdsArray);
            onClose();
        } catch (error) {
            console.error("Error removing artifact from lists", error);
        } finally {
            setSubmitting(false);
        }
    };

    const handleClose = (e) => {
        e.stopPropagation();
        e.preventDefault();
        setSelectedLists(new Set());
        onClose();
    };

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            onClick={(e) => e.stopPropagation()}
            maxWidth="sm"
            fullWidth
            PaperProps={{
                sx: {
                    backgroundColor: theme.palette.custom.darkBlue,
                    borderRadius: "8px",
                },
            }}
        >
            <DialogTitle
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "20px 24px",
                }}
            >
                <Typography sx={{ color: "#FFFFFF", fontWeight: 600, fontSize: "18px" }}>Remove artifact from lists</Typography>
                <IconButton onClick={handleClose} sx={{ color: "#FFFFFF", padding: 0 }}>
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent sx={{ padding: "24px", paddingBottom: 0 }}>
                <Typography sx={{ color: "#FFFFFF", fontSize: "14px", marginBottom: 2 }}>
                    This artifact has been saved to multiple lists. Select which lists you want to remove it from.
                </Typography>
                {loading ? (
                    <Box sx={{ display: "flex", justifyContent: "center", padding: 2 }}>
                        <CircularProgress size={40} sx={{ color: "#FFFFFF" }} />
                    </Box>
                ) : (
                    <Stack spacing={1}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={selectAllState.checked}
                                    indeterminate={selectAllState.indeterminate}
                                    onChange={handleSelectAll}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                    }}
                                    sx={{
                                        color: theme.palette.custom.borderColor,
                                        "&.Mui-checked": {
                                            color: "#FFFFFF",
                                        },
                                        "&.MuiCheckbox-indeterminate": {
                                            color: "#FFFFFF",
                                        },
                                    }}
                                />
                            }
                            label={
                                <Typography sx={{ color: "#FFFFFF", fontSize: "14px" }}>
                                    Select all lists
                                </Typography>
                            }
                        />
                        {allListItems.map((item) => {
                            const isSelected = selectedLists.has(item.id);
                            return (
                                <FormControlLabel
                                    key={item.id}
                                    sx={{ marginLeft: "20px !important" }}
                                    control={
                                        <Checkbox
                                            checked={isSelected}
                                            onChange={() => handleToggleList(item.id)}
                                            onClick={(e) => {
                                                e.stopPropagation();
                                            }}
                                            sx={{
                                                color: theme.palette.custom.borderColor,
                                                "&.Mui-checked": {
                                                    color: "#FFFFFF",
                                                },
                                            }}
                                        />
                                    }
                                    label={
                                        <Typography sx={{ color: "#FFFFFF", fontSize: "14px" }}>
                                            {item.name}
                                        </Typography>
                                    }
                                />
                            );
                        })}
                        {statusMessage && (
                            <Typography
                                sx={{
                                    color: alpha("#FFFFFF", 0.7),
                                    fontSize: "14px",
                                    fontWeight: 100,
                                    marginTop: 2,
                                    marginBottom: 1,
                                }}
                            >
                                {statusMessage}
                            </Typography>
                        )}
                    </Stack>
                )}
            </DialogContent>
            <DialogActions
                sx={{
                    padding: "20px 24px",
                    display: "flex",
                    justifyContent: "center",
                    gap: 1,
                }}
            >
                <Button
                    onClick={handleClose}
                    variant="outlined"
                    sx={{
                        color: "#FFFFFF",
                        borderColor: theme.palette.custom.borderColor,
                        textTransform: "none",
                    }}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleSubmit}
                    variant="contained"
                    disabled={submitting || selectedLists.size === 0}
                    sx={{
                        backgroundColor: theme.palette.custom.mainBlue,
                        color: "#FFFFFF",
                        textTransform: "none",
                        "&:disabled": {
                            backgroundColor: theme.palette.custom.borderColor,
                            color: "#888",
                        },
                    }}
                >
                    {submitting ? "Removing..." : "Remove"}
                </Button>
            </DialogActions>
        </Dialog>
    );
}

