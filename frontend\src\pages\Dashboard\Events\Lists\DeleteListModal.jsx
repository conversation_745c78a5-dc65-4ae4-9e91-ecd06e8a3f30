import { <PERSON>alog, DialogTitle, DialogContent, DialogActions, Button, IconButton, Typography, Box } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useState } from "react";
import theme from "../../../../theme";
import listsController from "../../../../controllers/Lists.controller";

export default function DeleteListModal({ open, onClose, list = null, isSharedList = false, user = null, onSuccess }) {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState("");

    const handleDelete = async () => {
        if (!list) return;
        setLoading(true);
        setError("");
        try {
            if (isSharedList) {
                await listsController.removeFromSharedList({ listId: list._id, userId: user?._id });
            } else {
                await listsController.deleteList({ listId: list._id });
            }
            onSuccess?.();
            onClose();
        } catch (err) {
            setError(err?.message || `Failed to ${isSharedList ? "remove" : "delete"} list`);
        } finally {
            setLoading(false);
        }
    };

    const handleClose = () => {
        setError("");
        onClose();
    };

    if (!list) return null;

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            fullWidth
            PaperProps={{
                sx: {
                    backgroundColor: theme.palette.custom.darkBlue,
                    borderRadius: "12px",
                    border: `1px solid ${theme.palette.custom.borderColor}`,
                    maxWidth: { md: "480px" },
                },
            }}
        >
            <DialogTitle sx={{ color: "#FFFFFF", fontWeight: 600, fontSize: "18px", display: "flex", justifyContent: "space-between", alignItems: "center" }}>
                {isSharedList ? "Remove shared list" : "Delete list"}
                <IconButton onClick={handleClose} size="small" sx={{ color: "#FFFFFF" }}>
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent>
                <Box sx={{ mt: 1 }}>
                    <Typography sx={{ color: "#FFFFFF", fontSize: "18px", mb: isSharedList ? 0 : 2, textAlign: "center" }}>
                        {isSharedList
                            ? `Are you sure you want to remove yourself from shared list "${list.name}"?`
                            : `Are you sure you want to delete list "${list.name}"?`}
                    </Typography>
                    {!isSharedList && (
                        <Typography sx={{ color: "#9AA4BF", fontSize: "14px", mt: 1, textAlign: "center" }}>
                            This list will be removed from other users. Artifacts will not be deleted from system.
                        </Typography>
                    )}
                    {error && (
                        <Typography sx={{ color: "#E60000", fontSize: "12px", mt: 1 }}>
                            {error}
                        </Typography>
                    )}
                </Box>
            </DialogContent>
            <DialogActions sx={{ p: 2, gap: 1, display: "flex", justifyContent: "center" }}>
                <Button
                    onClick={handleClose}
                    variant="outlined"
                    disabled={loading}
                    sx={{
                        color: "#FFFFFF",
                        borderColor: theme.palette.custom.borderColor,
                        textTransform: "none",
                        "&:hover": {
                            borderColor: theme.palette.custom.borderColor,
                        },
                    }}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleDelete}
                    variant="contained"
                    disabled={loading}
                    sx={{
                        backgroundColor: "#E60000",
                        color: "#FFFFFF",
                        textTransform: "none",
                        "&:hover": {
                            backgroundColor: "#CC0000",
                        },
                    }}
                >
                    {isSharedList ? "Remove" : "Delete"}
                </Button>
            </DialogActions>
        </Dialog>
    );
}

