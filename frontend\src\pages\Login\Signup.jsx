import { useLocation } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";
import { TextField, Button, Typography, Link, Grid, CircularProgress, InputAdornment, IconButton } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import axiosInstance from "../../axios";
import { Formik, Field, Form } from "formik";
import { signupUserSchema } from "../../validation-schemas";

function Signup() {
    const location = useLocation();
    const params = new URLSearchParams(location.search);
    const email = params.get("email");
    const role_id = parseInt(params.get("role_id"));
    const role = params.get("role");
    const organization_id = params.get("organization_id");
    const organization = params.get("organization_name");
    const token = params.get("token");

    const navigate = useNavigate();

    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const [error, setError] = useState("");
    const [submitting, setSubmitting] = useState(false);

    const timeout = useRef();

    useEffect(() => {
        if (!email || !role_id || !token || !organization || !organization_id) {
            console.error("Invalid signup link: Missing required parameters.");
            navigate("/login");
        }
    }, [email, role_id, token, organization, organization_id]);

    useEffect(() => {
        if (error) {
            clearTimeout(timeout.current);
            timeout.current = setTimeout(() => setError(""), 3000);
        }
    }, [error]);

    const handleSubmit = async (values) => {
        const data = {
            name: `${values.firstName.trim()} ${values.lastName.trim()}`,
            username: values.username.trim(),
            email,
            password: values.password,
            role_id,
            organization_id,
            token,
        };

        setSubmitting(true);

        try {
            await axiosInstance.post("/users", data, { meta: { showSnackbar: true } }).then(() => {
                setSubmitting(false);
                navigate("/login");
            });
        } catch (err) {
            setError("SignUp failed: " + (err.response?.data?.message || err.message || JSON.stringify(err)));
            setSubmitting(false);
        }
    };

    return (
        <Grid container flexDirection={"column"} gap={1}>
            <Grid flexDirection={"column"} color={"#FFFFFF"}>
                <Grid>
                    <Typography variant="h3" fontWeight={"600"} textAlign={"center"} sx={{ whiteSpace: "pre-line" }}>
                        {`SmartMast \n Dashboard Signup`}
                    </Typography>
                </Grid>
            </Grid>
            <Formik
                initialValues={{
                    firstName: "",
                    lastName: "",
                    username: "",
                    password: "",
                    confirmPassword: "",
                    role_id: role_id,
                    organization_id: organization_id,
                }}
                validationSchema={signupUserSchema}
                onSubmit={handleSubmit}
            >
                {({ errors, touched, values, handleChange, isSubmitting }) => {
                    return (
                        <Form>
                            <Grid container flexDirection={"column"} gap={4}>
                                <Grid>
                                    <Field
                                        name="email"
                                        as={TextField}
                                        className="input-signup"
                                        type="text"
                                        inputProps={{
                                            autoComplete: "on",
                                        }}
                                        autoComplete="on"
                                        placeholder="Email"
                                        variant="outlined"
                                        fullWidth
                                        value={email}
                                        required
                                        disabled
                                    />
                                </Grid>
                                <Grid>
                                    <Field
                                        name="role"
                                        as={TextField}
                                        className="input-signup"
                                        type="text"
                                        placeholder="Role"
                                        variant="outlined"
                                        fullWidth
                                        value={role}
                                        disabled
                                    />
                                </Grid>
                                <Grid>
                                    <Field
                                        name="organization"
                                        as={TextField}
                                        className="input-signup"
                                        type="text"
                                        placeholder="Organization"
                                        variant="outlined"
                                        fullWidth
                                        value={organization}
                                        disabled
                                    />
                                </Grid>
                                <Grid container gap={2} alignItems="center" justifyContent="center">
                                    <Grid size="grow">
                                        <Field
                                            name="firstName"
                                            as={TextField}
                                            fullWidth
                                            className="input-signup"
                                            placeholder="First Name"
                                            variant="outlined"
                                            onChange={handleChange}
                                            value={values.firstName}
                                            required
                                            error={touched.firstName && Boolean(errors.firstName)}
                                            helperText={touched.firstName && errors.firstName}
                                        />
                                    </Grid>
                                    <Grid size="grow">
                                        <Field
                                            name="lastName"
                                            as={TextField}
                                            fullWidth
                                            className="input-signup"
                                            placeholder="Last Name"
                                            variant="outlined"
                                            onChange={handleChange}
                                            value={values.lastName}
                                            required
                                            error={touched.lastName && Boolean(errors.lastName)}
                                            helperText={touched.lastName && errors.lastName}
                                        />
                                    </Grid>
                                </Grid>
                                <Grid>
                                    <Field
                                        name="username"
                                        as={TextField}
                                        fullWidth
                                        className="input-signup"
                                        type="text"
                                        inputProps={{ autoComplete: "on" }}
                                        autoComplete="on"
                                        placeholder="Username"
                                        variant="outlined"
                                        onChange={handleChange}
                                        value={values.username}
                                        required={!values.username}
                                        error={touched.username && Boolean(errors.username)}
                                        helperText={touched.username && errors.username}
                                    />
                                </Grid>
                                <Grid>
                                    <Field
                                        name="password"
                                        as={TextField}
                                        fullWidth
                                        className="input-signup"
                                        placeholder="Password"
                                        variant="outlined"
                                        value={values.password}
                                        onChange={handleChange}
                                        required
                                        type={showPassword ? "text" : "password"}
                                        InputProps={{
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <IconButton
                                                        onClick={() => setShowPassword((v) => !v)}
                                                        sx={{ color: "primary.contrastText" }}
                                                        aria-label="toggle password visibility"
                                                    >
                                                        {showPassword ? <VisibilityOff /> : <Visibility />}
                                                    </IconButton>
                                                </InputAdornment>
                                            ),
                                        }}
                                        error={touched.password && Boolean(errors.password)}
                                        helperText={touched.password && errors.password}
                                    />
                                </Grid>
                                <Grid>
                                    <Field
                                        name="confirmPassword"
                                        as={TextField}
                                        fullWidth
                                        className="input-signup"
                                        placeholder="Confirm Password"
                                        variant="outlined"
                                        value={values.confirmPassword}
                                        onChange={handleChange}
                                        required
                                        type={showConfirmPassword ? "text" : "password"}
                                        InputProps={{
                                            endAdornment: (
                                                <InputAdornment position="end">
                                                    <IconButton
                                                        onClick={() => setShowConfirmPassword((v) => !v)}
                                                        sx={{ color: "primary.contrastText" }}
                                                        aria-label="toggle confirm password visibility"
                                                        data-testid="toggle-password-visibility"
                                                    >
                                                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                                                    </IconButton>
                                                </InputAdornment>
                                            ),
                                        }}
                                        error={touched.confirmPassword && Boolean(errors.confirmPassword)}
                                        helperText={touched.confirmPassword && errors.confirmPassword}
                                    />
                                </Grid>
                                <Grid display={error ? "block" : "none"}>
                                    <Typography color="error">{error}</Typography>
                                </Grid>
                                <Grid>
                                    <Button
                                        className="btn-login"
                                        type="submit"
                                        variant="contained"
                                        color="primary"
                                        fullWidth
                                        disabled={submitting || isSubmitting}
                                        endIcon={submitting && <CircularProgress />}
                                    >
                                        Signup
                                    </Button>
                                </Grid>
                            </Grid>
                        </Form>
                    );
                }}
            </Formik>
            {/* <Grid color={"#FFFFFF"}>
                <Typography fontSize="18px" lineHeight="30px" fontWeight={"400"}>
                    Request an Account:{" "}
                    <Link
                        href="mailto:<EMAIL>"
                        color="#FFFFFF"
                        fontWeight="bold"
                        sx={{ textDecoration: "none", ":hover": { textDecoration: "underline" } }}
                    >
                        <EMAIL>
                    </Link>
                </Typography>
            </Grid> */}
        </Grid>
    );
}

export default Signup;
