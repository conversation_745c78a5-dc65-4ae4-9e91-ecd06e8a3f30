import { Dialog, DialogTitle, DialogContent, DialogActions, Button, FormControl, Select, MenuItem, Typography, IconButton, CircularProgress, Box, ListItemText } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useState, useEffect, useMemo } from "react";
import theme from "../../../../theme";
import { useUser } from "../../../../hooks/UserHook";
import useStore from "../../../../hooks/Store";

const MAX_DISPLAY_LENGTH = 20;

const truncateName = (name, maxLength = MAX_DISPLAY_LENGTH) => {
    if (!name) return "";
    if (name.length <= maxLength) return name;
    return name.substring(0, maxLength) + "...";
};

export default function CopyFromModal({ open, onClose, lists = [], sourceListId, onCopy }) {
    const { user } = useUser();
    const { selectedSaveList } = useStore();
    const [selectedTargetListId, setSelectedTargetListId] = useState("");
    const [copying, setCopying] = useState(false);

    const availableLists = useMemo(() => {
        if (sourceListId === "favorites") {
            return lists;
        }
        return lists.filter((l) => String(l._id) !== String(sourceListId));
    }, [lists, sourceListId]);

    const yourLists = useMemo(() => availableLists.filter((l) => String(l.owner_id) === String(user?._id)), [availableLists, user]);
    const sharedWithYou = useMemo(() => availableLists.filter((l) => String(l.owner_id) !== String(user?._id)), [availableLists, user]);

    useEffect(() => {
        if (open) {
            setSelectedTargetListId("");
            setCopying(false);
        }
    }, [open]);

    const handleSave = async () => {
        if (!selectedTargetListId) return;

        setCopying(true);

        try {
            await onCopy(selectedTargetListId);
            onClose();
        } catch (error) {
            console.error("Error copying artifacts", error);
        } finally {
            setCopying(false);
        }
    };

    const handleClose = () => {
        if (!copying) {
            setSelectedTargetListId("");
            onClose();
        }
    };

    const canCopy = selectedTargetListId && !copying && (sourceListId === "favorites" || selectedTargetListId !== sourceListId);

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            maxWidth="sm"
            fullWidth
            PaperProps={{
                sx: {
                    backgroundColor: theme.palette.custom.darkBlue,
                    borderRadius: "8px",
                },
            }}
        >
            <DialogTitle
                sx={{
                    display: "flex",
                    justifyContent: "space-between",
                    alignItems: "center",
                    padding: "20px 24px",
                }}
            >
                <Typography sx={{ color: "#FFFFFF", fontWeight: 600, fontSize: "18px" }}>Select list to copy to</Typography>
                <IconButton onClick={handleClose} disabled={copying} sx={{ color: "#FFFFFF", padding: 0 }}>
                    <CloseIcon />
                </IconButton>
            </DialogTitle>
            <DialogContent sx={{ padding: "24px", paddingBottom: 0 }}>
                <Typography sx={{ color: "#FFFFFF", mb: 2 }}>
                    The artifacts from this list will be copied to the list selected below.
                </Typography>
                <FormControl fullWidth size="small" variant="outlined" sx={{ mb: 2 }}>
                    <Select
                        value={selectedTargetListId}
                        onChange={(e) => setSelectedTargetListId(e.target.value)}
                        displayEmpty
                        disabled={copying}
                        renderValue={() => {
                            if (!selectedTargetListId) {
                                return <Typography sx={{ color: "#9AA4BF" }}>Select list</Typography>;
                            }
                            if (selectedTargetListId === "favorites") {
                                return <Typography sx={{ color: "#FFFFFF" }}>Favorites (private)</Typography>;
                            }
                            const selected = lists.find((l) => String(l._id) === String(selectedTargetListId));
                            if (!selected) return null;
                            const suffix = selected.shared_with_organization || selected.owner_id !== user?._id ? "(shared)" : "(private)";
                            return <Typography sx={{ color: "#FFFFFF" }}>{truncateName(selected.name)} {suffix}</Typography>;
                        }}
                        sx={{
                            "& .MuiOutlinedInput-notchedOutline": {
                                borderColor: theme.palette.custom.borderColor,
                            },
                            "& .MuiSelect-select": {
                                padding: "10px",
                                backgroundColor: theme.palette.custom.cardBackground,
                                borderRadius: "5px",
                                color: "#FFFFFF",
                            },
                            "&:hover .MuiOutlinedInput-notchedOutline": {
                                borderColor: theme.palette.custom.borderColor,
                            },
                            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                                borderColor: theme.palette.custom.mainBlue,
                            },
                        }}
                    >
                        <MenuItem value="favorites">
                            <ListItemText
                                primary={
                                    <Typography sx={{ color: "#FFFFFF" }}>
                                        Favorites (private) {selectedSaveList?.id === "favorites" ? "★" : ""}
                                    </Typography>
                                }
                            />
                        </MenuItem>
                        {yourLists.map((l) => {
                            const suffix = l.shared_with_organization ? "(shared)" : "(private)";
                            const displayName = truncateName(l.name);
                            const fullText = `${l.name} ${suffix} ${selectedSaveList?.id === l._id ? "★" : ""}`;
                            return (
                                <MenuItem key={l._id} value={l._id}>
                                    <ListItemText
                                        primary={
                                            <Typography
                                                sx={{
                                                    color: "#FFFFFF",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    whiteSpace: "nowrap",
                                                }}
                                                title={fullText}
                                            >
                                                {displayName} {suffix} {selectedSaveList?.id === l._id ? "★" : ""}
                                            </Typography>
                                        }
                                    />
                                </MenuItem>
                            );
                        })}
                        {sharedWithYou.map((l) => {
                            const displayName = truncateName(l.name);
                            const fullText = `${l.name} (shared) ${selectedSaveList?.id === l._id ? "★" : ""}`;
                            return (
                                <MenuItem key={l._id} value={l._id}>
                                    <ListItemText
                                        primary={
                                            <Typography
                                                sx={{
                                                    color: "#FFFFFF",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                    whiteSpace: "nowrap",
                                                }}
                                                title={fullText}
                                            >
                                                {displayName} (shared) {selectedSaveList?.id === l._id ? "★" : ""}
                                            </Typography>
                                        }
                                    />
                                </MenuItem>
                            );
                        })}
                    </Select>
                </FormControl>
            </DialogContent>
            <DialogActions sx={{ p: 2, gap: 1, justifyContent: "center" }}>
                <Button
                    onClick={handleClose}
                    variant="outlined"
                    disabled={copying}
                    sx={{
                        color: "#FFFFFF",
                        borderColor: theme.palette.custom.borderColor,
                        textTransform: "none",
                        "&:hover": {
                            borderColor: theme.palette.custom.borderColor,
                        },
                        "&:disabled": {
                            opacity: 0.5,
                            borderColor: theme.palette.custom.borderColor,
                        },
                    }}
                >
                    Cancel
                </Button>
                <Button
                    onClick={handleSave}
                    variant="contained"
                    disabled={!canCopy || copying}
                    sx={{
                        backgroundColor: canCopy && !copying ? theme.palette.custom.mainBlue : theme.palette.custom.borderColor,
                        color: canCopy && !copying ? "#FFFFFF" : "#9AA4BF",
                        textTransform: "none",
                        "&:hover": {
                            backgroundColor: canCopy && !copying ? theme.palette.custom.mainBlue : theme.palette.custom.borderColor,
                        },
                        "&:disabled": {
                            opacity: 0.5,
                            backgroundColor: theme.palette.custom.borderColor,
                        },
                    }}
                >
                    {copying ? (
                        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                            <CircularProgress size={16} sx={{ color: "#FFFFFF" }} />
                            <Typography>Saving...</Typography>
                        </Box>
                    ) : (
                        "Save"
                    )}
                </Button>
            </DialogActions>
        </Dialog>
    );
}

