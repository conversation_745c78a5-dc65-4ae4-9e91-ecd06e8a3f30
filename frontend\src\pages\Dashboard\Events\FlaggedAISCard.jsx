import { memo, useState } from "react";
import { <PERSON>rid, Typo<PERSON>, Box, Card, CardContent, Divider, IconButton, Tooltip } from "@mui/material";
import { LocationOn, AccessTime, Flag as FlagIcon, Close } from "@mui/icons-material";
import theme from "../../../theme";
import dayjs from "dayjs";
import { displayCoordinates, userValues } from "../../../utils";
import { useUser } from "../../../hooks/UserHook";
import aisVesselFlagController from "../../../controllers/AisVesselFlag.controller";
import RemoveFlagConfirmationModal from "../../../components/RemoveFlagConfirmationModal";

const FlaggedAISCard = ({ card, setShowDetailModal, setSelectedCard }) => {
    const vessel = card;
    const [showRemoveModal, setShowRemoveModal] = useState(false);
    const [isRemoving, setIsRemoving] = useState(false);

    const handleCardClick = () => {
        if (setSelectedCard && setShowDetailModal) {
            setSelectedCard(vessel);
            setShowDetailModal(true);
        }
    };

    const handleUnflagClick = (e) => {
        e.stopPropagation();
        setShowRemoveModal(true);
    };

    const handleRemoveConfirm = async (e) => {
        e.stopPropagation();
        setIsRemoving(true);
        try {
            await aisVesselFlagController.removeAllFlagsFromAisVessel(vessel.mmsi);
            setShowRemoveModal(false);
        } catch (error) {
            console.error("Error removing flags:", error);
        } finally {
            setIsRemoving(false);
        }
    };

    const handleRemoveCancel = (e) => {
        e.stopPropagation();
        setShowRemoveModal(false);
    };

    const { user } = useUser();
    const aisData = vessel.aisData;

    if (!aisData) {
        return (
            <Grid
                container
                paddingTop={"0 !important"}
                height={"100%"}
                sx={{ cursor: "pointer" }}
                onClick={handleCardClick}
            >
                <Card
                    sx={{
                        backgroundColor: theme.palette.custom.darkBlue,
                        border: `1px solid ${theme.palette.custom.borderColor}`,
                        borderRadius: "12px",
                        width: "100%",
                        height: "100%",
                        display: "flex",
                        flexDirection: "column",
                    }}
                >
                    <CardContent sx={{ padding: "12px !important", display: "flex", flexDirection: "column", height: "100%", justifyContent: "center" }}>
                        <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography fontSize={"16px"} fontWeight={600} color="#FFFFFF">
                                MMSI: {vessel.mmsi}
                            </Typography>
                            <Box display="flex" alignItems="center" gap={1}>
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 0.5,
                                        padding: "4px 12px",
                                        borderRadius: "16px",
                                        backgroundColor: "rgba(253, 191, 45, 0.15)",
                                    }}
                                >
                                    <FlagIcon sx={{ fontSize: "16px", color: "#FDBF2D" }} />
                                    <Typography fontSize={"12px"} fontWeight={500} color="#FDBF2D">
                                        {vessel.flagCount || 0} {vessel.flagCount === 1 ? "flag" : "flags"}
                                    </Typography>
                                </Box>
                                <Tooltip title={vessel.flagCount === 1 ? "Remove flag" : "Remove all flags"}>
                                    <IconButton
                                        size="small"
                                        onClick={handleUnflagClick}
                                        sx={{
                                            color: theme.palette.custom.mainBlue,
                                            padding: "4px",
                                            "&:hover": {
                                                backgroundColor: "rgba(0, 102, 255, 0.1)",
                                            },
                                        }}
                                    >
                                        <Close sx={{ fontSize: "18px" }} />
                                    </IconButton>
                                </Tooltip>
                            </Box>
                        </Box>
                    </CardContent>
                </Card>
                <RemoveFlagConfirmationModal
                    open={showRemoveModal}
                    onClose={handleRemoveCancel}
                    onConfirm={handleRemoveConfirm}
                    isLoading={isRemoving}
                    isAISVessel={true}
                />
            </Grid>
        );
    }

    const roundedCoordinates = displayCoordinates(aisData.location?.coordinates, !!user?.use_MGRS);

    return (
        <Grid
            container
            paddingTop={"0 !important"}
            height={"100%"}
            sx={{ cursor: "pointer" }}
            onClick={handleCardClick}
        >
            <Card
                sx={{
                    backgroundColor: theme.palette.custom.darkBlue,
                    border: `1px solid ${theme.palette.custom.borderColor}`,
                    borderRadius: "12px",
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                }}
            >
                <CardContent sx={{ padding: "12px !important", display: "flex", flexDirection: "column", height: "100%", justifyContent: "space-between" }}>
                    <Box display="flex" justifyContent="space-between" alignItems="flex-start" marginBottom={1}>
                        <Box>
                            <Typography fontSize={"16px"} fontWeight={600} color="#FFFFFF" marginBottom={0.25}>
                                {aisData.name || "Unknown Vessel"}
                            </Typography>
                            <Typography fontSize={"13px"} color={theme.palette.custom.mainBlue} fontWeight={500}>
                                MMSI: {vessel.mmsi}
                            </Typography>
                        </Box>
                        <Box display="flex" alignItems="center" gap={1}>
                            <Box
                                sx={{
                                    display: "flex",
                                    alignItems: "center",
                                    gap: 0.5,
                                    padding: "4px 12px",
                                    borderRadius: "20px",
                                    backgroundColor: "rgba(253, 191, 45, 0.15)",
                                }}
                            >
                                <FlagIcon sx={{ fontSize: "14px", color: "#FDBF2D" }} />
                                <Typography fontSize={"12px"} fontWeight={600} color="#FDBF2D">
                                    {vessel.flagCount || 0}
                                </Typography>
                            </Box>
                            <Tooltip title={vessel.flagCount === 1 ? "Remove flag" : "Remove all flags"}>
                                <IconButton
                                    size="small"
                                    onClick={handleUnflagClick}
                                    sx={{
                                        color: theme.palette.custom.mainBlue,
                                        padding: "4px",
                                        "&:hover": {
                                            backgroundColor: "rgba(0, 102, 255, 0.1)",
                                        },
                                    }}
                                >
                                    <Close sx={{ fontSize: "18px" }} />
                                </IconButton>
                            </Tooltip>
                        </Box>
                    </Box>

                    <Divider sx={{ backgroundColor: theme.palette.custom.borderColor, marginY: 1 }} />

                    <Grid container spacing={1}>
                        <Grid size={6}>
                            <Box display="flex" alignItems="flex-start" gap={0.75}>
                                <LocationOn sx={{ fontSize: "16px", color: theme.palette.custom.mainBlue, marginTop: 0.25, flexShrink: 0 }} />
                                <Box>
                                    <Typography fontSize={"10px"} color={theme.palette.custom.mainBlue} fontWeight={500} textTransform="uppercase">
                                        Location
                                    </Typography>
                                    <Typography fontSize={"12px"} color="#FFFFFF" fontWeight={500} marginTop={0.25}>
                                        {roundedCoordinates}
                                    </Typography>
                                </Box>
                            </Box>
                        </Grid>

                        <Grid size={6}>
                            <Box display="flex" alignItems="flex-start" gap={0.75}>
                                <AccessTime sx={{ fontSize: "16px", color: theme.palette.custom.mainBlue, marginTop: 0.25, flexShrink: 0 }} />
                                <Box>
                                    <Typography fontSize={"10px"} color={theme.palette.custom.mainBlue} fontWeight={500} textTransform="uppercase">
                                        Timestamp
                                    </Typography>
                                    <Typography fontSize={"12px"} color="#FFFFFF" fontWeight={500} marginTop={0.25}>
                                        {aisData.timestamp
                                            ? dayjs(aisData.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))
                                            : "N/A"}
                                    </Typography>
                                </Box>
                            </Box>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>
            <RemoveFlagConfirmationModal
                open={showRemoveModal}
                onClose={handleRemoveCancel}
                onConfirm={handleRemoveConfirm}
                isLoading={isRemoving}
                isAISVessel={true}
            />
        </Grid>
    );
};

export default memo(FlaggedAISCard);

