import mongoose from "mongoose";
import AisVesselFlag from "../models/AisVesselFlag";
import db from "../modules/db";
import { IAisVesselFlag, IFlaggedAisVessel } from "../interfaces/AisVesselFlag";

interface AggregationFlagData {
    _id: string;
    flags: Array<{
        _id: mongoose.Types.ObjectId;
        flaggedBy: mongoose.Types.ObjectId;
        flaggedByUser: {
            _id: mongoose.Types.ObjectId;
            name: string;
            email: string;
        };
        flaggedAt: Date;
    }>;
    flagCount: number;
    latestFlagDate: Date;
}

class AisVesselFlagService {
    async flagAisVessel(mmsi: string, userId: string): Promise<IAisVesselFlag> {
        if (!mmsi || mmsi.trim() === "") {
            throw new Error("MMSI is required");
        }

        const existingFlag = await AisVesselFlag.findOne({
            mmsi: mmsi.trim(),
            flaggedBy: new mongoose.Types.ObjectId(userId),
        });
        if (existingFlag) {
            throw new Error("You have already flagged this AIS vessel");
        }

        const flag = new AisVesselFlag({
            mmsi: mmsi.trim(),
            flaggedBy: new mongoose.Types.ObjectId(userId),
        });

        await flag.save();
        return flag;
    }

    async getFlaggedAisVessels(
        page: number,
        pageSize: number,
    ): Promise<{ vessels: IFlaggedAisVessel[]; totalCount: number; page: number; pageSize: number }> {
        const skip = (page - 1) * pageSize;

        const allFlaggedMmsis = await AisVesselFlag.distinct("mmsi");
        const totalCount = allFlaggedMmsis.length;

        if (totalCount === 0) {
            return { vessels: [], totalCount: 0, page, pageSize };
        }

        const aggregationPipeline: any[] = [
            {
                $lookup: {
                    from: "users",
                    let: { userId: "$flaggedBy" },
                    pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$userId"] } } }, { $project: { _id: 1, name: 1, email: 1 } }],
                    as: "flaggedByUser",
                },
            },
            {
                $group: {
                    _id: "$mmsi",
                    flags: {
                        $push: {
                            _id: "$_id",
                            flaggedBy: "$flaggedBy",
                            flaggedByUser: { $arrayElemAt: ["$flaggedByUser", 0] },
                            flaggedAt: "$flaggedAt",
                        },
                    },
                    flagCount: { $sum: 1 },
                    latestFlagDate: { $max: "$flaggedAt" },
                },
            },
            { $sort: { latestFlagDate: -1 } },
            { $skip: skip },
            { $limit: pageSize },
        ];

        const flaggedVessels = await AisVesselFlag.aggregate(aggregationPipeline);

        const mmsis = flaggedVessels.map((item: AggregationFlagData) => item._id);
        if (mmsis.length === 0) {
            return { vessels: [], totalCount, page, pageSize };
        }

        // Fetch latest AIS data for each MMSI
        const aisLookupCollection = db.lookups.collection("ais_mmsi_lookup");
        const aisDataMap = new Map<string, any>();

        for (const mmsi of mmsis) {
            try {
                const latestAis = await aisLookupCollection.findOne(
                    { mmsi: mmsi },
                    {
                        projection: {
                            "data.name": 1,
                            "data.location": 1,
                            "data.timestamp": 1,
                            "data.metadata": 1,
                            "data.details.message": 1,
                        },
                        sort: { "data.last_message_timestamp": -1 },
                    },
                );
                if (latestAis?.data) {
                    aisDataMap.set(mmsi, latestAis.data);
                }
            } catch (error) {
                console.error(`Error fetching AIS data for MMSI ${mmsi}:`, error);
            }
        }

        const results: IFlaggedAisVessel[] = [];
        for (const flagData of flaggedVessels) {
            const typedFlagData: AggregationFlagData = {
                _id: flagData._id,
                flags: flagData.flags,
                flagCount: flagData.flagCount,
                latestFlagDate: flagData.latestFlagDate,
            };
            const aisData = aisDataMap.get(typedFlagData._id);
            results.push({
                _id: new mongoose.Types.ObjectId(),
                mmsi: typedFlagData._id,
                flags: typedFlagData.flags.map((flag) => ({
                    ...flag,
                    user: flag.flaggedByUser,
                    created_at: flag.flaggedAt,
                })),
                flagCount: typedFlagData.flagCount,
                latestFlagDate: typedFlagData.latestFlagDate,
                aisData: aisData || undefined,
            });
        }

        return {
            vessels: results,
            totalCount,
            page,
            pageSize,
        };
    }

    async unflagAisVessel(mmsi: string, userId: string): Promise<IAisVesselFlag | null> {
        const flag = await AisVesselFlag.findOneAndDelete({
            mmsi: mmsi.trim(),
            flaggedBy: new mongoose.Types.ObjectId(userId),
        });
        if (!flag) {
            throw new Error("Flag not found");
        }
        return flag;
    }

    async getUserFlaggedAisVesselMmsis(userId: string): Promise<string[]> {
        const flags = await AisVesselFlag.find({ flaggedBy: new mongoose.Types.ObjectId(userId) }, { mmsi: 1, _id: 0 });
        return flags.map((flag: IAisVesselFlag) => flag.mmsi);
    }

    async removeAllFlagsFromAisVessel(mmsi: string): Promise<{ deletedCount: number }> {
        const result = await AisVesselFlag.deleteMany({
            mmsi: mmsi.trim(),
        });
        return result;
    }
}

export default new AisVesselFlagService();
