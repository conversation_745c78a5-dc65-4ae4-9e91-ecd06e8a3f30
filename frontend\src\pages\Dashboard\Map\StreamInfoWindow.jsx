import { Grid, IconButton, Tabs, Tab, Typography } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import React from "react";
import InsetStreamPlayer from "../../../components/InsetStreamPlayer";

const StreamInfoWindow = ({
    onClose,
    vessel,
    streamMode,
    startTimestamp,
}) => {
    return (
        <Grid
            container
            direction="column"
            width="320px"
            padding="8px"
            backgroundColor="#282C39"
        >
            <style>
                {`
                .gm-style-iw-chr, .gm-style-iw-tc {
                display: none !important;
                }
                .gm-style .gm-style-iw-c {
                background-color: #343B44 !important;
                outline: none;
                padding: 0;
                }
                .gm-style .gm-style-iw-d {
                overflow: auto !important;
                }
                .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                background-color: #fff !important;
                }
                .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                background: #343B44 !important;
                }
                `}
            </style>
            <Grid
                container
                width="100%"
                justifyContent="space-between"
                alignItems="center"
            >
                <Tabs
                    value={0}
                    sx={{
                        "& .MuiButtonBase-root.Mui-selected": {
                            backgroundColor: "#3B4251",
                            borderTopLeftRadius: "8px"
                        },
                    }}>
                    <Tab label={
                        <Typography noWrap variant="button" maxWidth="232px">
                            {vessel.name}
                        </Typography>
                    } sx={{ borderTopRightRadius: "8px" }} />
                </Tabs>
                <IconButton
                    onClick={onClose}
                    sx={{
                        border: "1px solid white",
                        "&:hover": {
                            backgroundColor: "white",
                            color: "#4F5968",
                        },
                    }}
                >
                    <CloseIcon sx={{ fontSize: "16px" }} />
                </IconButton>
            </Grid>
            <Grid
                container
                height="214px"
                backgroundColor="#3B4251"
                padding="8px"
                sx={{
                    borderBottomLeftRadius: "10px",
                    borderBottomRightRadius: "10px",
                    borderTopRightRadius: "10px",
                }}
            >
                <Grid container width="100%" overflow="hidden" borderRadius="8px">
                    <InsetStreamPlayer
                        vessel={vessel}
                        streamMode={streamMode}
                        startTimestamp={startTimestamp}
                    />
                </Grid>
            </Grid>
        </Grid>
    );
}

export default StreamInfoWindow;
