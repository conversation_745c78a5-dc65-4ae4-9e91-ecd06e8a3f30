import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('ListArtifact Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create ListArtifact model with proper schema definition and database registration', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ListArtifact')];

        const ListArtifactModule = await import('../../models/ListArtifact');
        const ListArtifact = ListArtifactModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('list_artifact', expect.any(Object));
        expect(ListArtifact).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];
        expect(schemaArg.paths).toBeDefined();

        expect(schemaArg.paths.list_id).toBeDefined();
        expect(schemaArg.paths.list_id.required).toBe(true);
        expect(schemaArg.paths.list_id.index).toBe(true);

        expect(schemaArg.paths.artifact_id).toBeDefined();
        expect(schemaArg.paths.artifact_id.required).toBe(true);
        expect(schemaArg.paths.artifact_id.index).toBe(true);

        expect(schemaArg.paths.added_by).toBeDefined();
        expect(schemaArg.paths.added_by.required).toBe(true);

        expect(schemaArg.paths.created_at).toBeDefined();
        expect(schemaArg.paths.created_at.type).toBe(Date);
        expect(schemaArg.paths.created_at.required).toBe(true);
        expect(schemaArg.paths.created_at.default).toBeDefined();
        const createdAtDefault = schemaArg.paths.created_at.default();
        expect(createdAtDefault).toBeInstanceOf(Date);

        expect(schemaArg.index).toHaveBeenCalledWith({ list_id: 1, artifact_id: 1 }, { unique: true });
        expect(schemaArg.index).toHaveBeenCalledWith({ list_id: 1, created_at: -1 });
    });
});