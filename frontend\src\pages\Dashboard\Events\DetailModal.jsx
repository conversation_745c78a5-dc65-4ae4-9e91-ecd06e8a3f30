import { Grid, Modal, Typography, Tooltip, Tabs, Tab, Box, alpha, IconButton, CircularProgress } from "@mui/material";
import React, { useEffect, useState } from "react";
import ModalContainer from "../../../components/ModalContainer";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import { useNavigate } from "react-router-dom";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
import FlagIcon from "@mui/icons-material/Flag";
import OutlinedFlagIcon from "@mui/icons-material/OutlinedFlag";
import aisVesselFlagController from "../../../controllers/AisVesselFlag.controller";
import artifactFlagController from "../../../controllers/ArtifactFlag.controller";
import RemoveFlagConfirmationModal from "../../../components/RemoveFlagConfirmationModal";

export default function DetailModal({ showDetailModal, setShowDetailModal, selectedCard, setSelectedCard, id, list }) {
    const { screenSize } = useApp();
    const [src, setSrc] = useState(null);
    const { user } = useUser();
    const { vesselInfo } = useVesselInfo();
    const navigate = useNavigate();
    const [activeTab, setActiveTab] = useState(0);
    const [isFlagged, setIsFlagged] = useState(false);
    const [isFlagging, setIsFlagging] = useState(false);
    const [showRemoveModal, setShowRemoveModal] = useState(false);
    const [isRemoving, setIsRemoving] = useState(false);
    const [isArtifactFlagged, setIsArtifactFlagged] = useState(false);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);
    const hasFlagMismatchAIS = user?.hasPermissions([permissions.flagMismatchAIS])
    // const [locationName, setLocationName] = useState('Loading...');
    // const location = selectedCard?.location ? { lat: selectedCard.location.coordinates[1], lng: selectedCard.location.coordinates[0] } : null;
    const key = selectedCard?.location?.coordinates && displayCoordinates(selectedCard?.location?.coordinates, !!user?.use_MGRS);

    const vessel = vesselInfo.find((v) => v.vessel_id === selectedCard?.onboard_vessel_id);
    const [isShowAISDot, setIsShownAISDot] = useState(false);
    useEffect(() => {
        setIsShownAISDot(Boolean(selectedCard && !selectedCard?.video_path && !screenSize.md));
    }, [selectedCard, screenSize.md]);

    // const fetchGeolocation = async () => {
    //     try {
    //         const name = await getLocation(location);
    //         setLocationName(name);
    //     } catch (err) {
    //         console.error('Error fetching geolocation:', err);
    //     }
    // };

    const handleClose = () => {
        setSelectedCard(null);
        setShowDetailModal(false);
        setSrc(null);
        setActiveTab(0);
    };

    const handleNavigation = () => {
        if (selectedCard?.location?.coordinates) {
            handleClose();
            setTimeout(() => {
                navigate(`/dashboard/map?artifact=${selectedCard?._id}`);
            }, 100);
        }
    };

    const details = [
        { label: "Location", value: key },
        { label: "Sub Category", value: selectedCard?.category },
        { label: "Category", value: selectedCard?.super_category || "Unspecified category" },
        { label: "Confidence", value: selectedCard?.det_conf * 100 ? `${(selectedCard.det_conf * 100).toFixed(2)}%` : undefined },
        { label: "Size", value: selectedCard?.size },
        { label: "Color", value: selectedCard?.color },
        { label: "Imo Number", value: selectedCard?.imo_number },
        { label: "Flag", value: selectedCard?.country_flag },
        { label: "Detected Country", value: selectedCard?.home_country },
        { label: "Weapons", value: selectedCard?.weapons },
        { label: "Bearing Angle", value: selectedCard?.true_bearing ? `${Number(selectedCard.true_bearing).toFixed(2)}\u00B0` : undefined },
        { label: "Orientation", value: selectedCard?.vessel_orientation },
        {
            label: "Text Detected",
            value:
                Array.isArray(selectedCard?.text_extraction) && selectedCard?.text_extraction?.length > 0
                    ? selectedCard?.text_extraction
                        .map((e) => e?.text)
                        .slice(0, 5)
                        .join(", ")
                    : null,
        },
        { label: "Features", value: selectedCard?.vessel_features },
        { label: "Description", value: selectedCard?.others },
    ];

    const fieldWithFullWidth = ["Text Detected", "Description", "Features"];

    const aisInfo = selectedCard?.portal?.ais_info?.data;
    const aisDetails = [
        { label: "AIS Confidence", value: selectedCard?.portal?.ais_info?.proximity_confidence ? `${(selectedCard?.portal?.ais_info?.proximity_confidence * 100).toFixed(2)}%` : undefined },
        { label: "MMSI", value: aisInfo?.mmsi },
        { label: "Name", value: aisInfo?.name },
        { label: "Ship Beam", value: aisInfo?.design_beam },
        { label: "Ship Length", value: aisInfo?.design_length },
        { label: "AIS Ship Message Class", value: aisInfo?.sensor_ais_class },
        { label: "AIS Ship State", value: aisInfo?.nav_state },
        { label: "AIS Ship Type", value: aisInfo?.design_ais_ship_type_name },
        { label: "Ship Length Type", value: aisInfo?.design_length_type },
        { label: "Location", value: aisInfo?.nav_longitude && aisInfo?.nav_latitude ? displayCoordinates([aisInfo.nav_longitude, aisInfo.nav_latitude], !!user?.use_MGRS) : undefined },
        { label: "Speed over ground", value: aisInfo?.nav_speed_over_ground },
        { label: "VHF Call Sign", value: aisInfo?.comm_callsign_vhf },
        { label: "Registry Country", value: aisInfo?.portal_registry_country?.country_name },
        { label: "AIS Bearing Angle", value: aisInfo?.portal_true_bearing_deg ? `${Number(aisInfo.portal_true_bearing_deg).toFixed(2)}\u00B0` : undefined },
        { label: "Timestamp", value: aisInfo?.timestamp ? dayjs(aisInfo?.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true })) : undefined },
    ];
    const AISFieldWithFullWidth = [];

    useEffect(() => {
        if (selectedCard) {
            const thumbnailUrl = selectedCard?.thumbnail_url;
            const imageUrl = selectedCard?.image_url;
            const videoUrl = selectedCard?.video_url;

            if (selectedCard?.video_path) {
                setSrc(videoUrl || imageUrl || null);
            } else {
                setSrc(imageUrl || thumbnailUrl || null);
            }
        }
    }, [selectedCard]);

    useEffect(() => {
        return () => {
            if (showDetailModal) {
                setShowDetailModal(false);
                setSelectedCard(null);
            }
        };
    }, [showDetailModal, setShowDetailModal, setSelectedCard]);

    useEffect(() => {
        const loadFlaggedStatus = async () => {
            if (aisInfo?.mmsi) {
                try {
                    const flagged = aisVesselFlagController.isAisVesselFlaggedByUser(aisInfo?.mmsi);
                    setIsFlagged(flagged);
                } catch (error) {
                    console.error("Error loading flagged status:", error);
                }
            }
        };
        loadFlaggedStatus();
    }, [aisInfo?.mmsi, aisVesselFlagController.version]);

    useEffect(() => {
        const loadArtifactFlaggedStatus = async () => {
            if (selectedCard?._id) {
                try {
                    const flagged = artifactFlagController.isArtifactFlaggedByUser(selectedCard._id);
                    setIsArtifactFlagged(flagged);
                } catch (error) {
                    console.error("Error loading artifact flagged status:", error);
                }
            }
        };
        loadArtifactFlaggedStatus();
    }, [selectedCard?._id, artifactFlagController.version]);

    const handleFlagClick = async (e) => {
        e.stopPropagation();
        if (!aisInfo?.mmsi) return;
        setIsFlagging(true);
        try {
            if (isFlagged) {
                await aisVesselFlagController.unflagAisVessel(aisInfo?.mmsi);
                setIsFlagged(false);
            } else {
                await aisVesselFlagController.flagAisVessel(aisInfo?.mmsi);
                setIsFlagged(true);
            }
        } catch (error) {
            console.error("Error flagging AIS vessel:", error);
        } finally {
            setIsFlagging(false);
        }
    };

    const handleUnflagClick = (e) => {
        e.stopPropagation();
        setShowRemoveModal(true);
    };

    const handleRemoveConfirm = async (e) => {
        e.stopPropagation();
        setIsRemoving(true);
        try {
            await artifactFlagController.removeAllFlagsFromArtifact(selectedCard?._id);
            setIsArtifactFlagged(false);
            setShowRemoveModal(false);
        } catch (error) {
            console.error("Error removing flags:", error);
        } finally {
            setIsRemoving(false);
        }
    };

    const handleRemoveCancel = (e) => {
        e.stopPropagation();
        setShowRemoveModal(false);
    };

    // useEffect(() => {
    //     if (location) {
    //         fetchGeolocation();
    //     }
    // }, [location]);

    if (!selectedCard) return null;

    return (
        <>
            <Modal open={Boolean(showDetailModal)} onClose={handleClose}>
                <ModalContainer title="Event Details" onClose={handleClose} showDivider>
                    <Grid container gap={1} minWidth={{ xs: 300, sm: 500 }} maxWidth={"80vw"} maxHeight={"80vh"} width={"80vw"}>
                        <Grid size={12} position="relative" minHeight="300px">
                            {selectedCard && (
                                <Box height={{ xs: "300px", sm: "300px", lg: isShowAISDot ? "70vh" : "40vh" }} width={isShowAISDot ? "80vw" : "80vw"}>
                                    <PreviewMedia
                                        thumbnailLink={src}
                                        originalLink={src}
                                        cardId={id || selectedCard._id}
                                        isImage={!selectedCard.video_path}
                                        style={{ borderRadius: 8, height: "100%", objectFit: "contain", backgroundColor: "#000" }}
                                        skeletonStyle={{ height: "100%", width: "100%", borderRadius: 2 }}
                                        showFullscreenIconForMap={!selectedCard.video_path}
                                        showArchiveButton={hasManageArtifacts}
                                        isArchived={selectedCard?.portal?.is_archived}
                                        vesselId={selectedCard?.onboard_vessel_id}
                                        showAISFlagButton={selectedCard?.portal?.ais_info?.data && hasFlagMismatchAIS}
                                        flaggedAISArtifact={selectedCard}
                                        list={list}
                                        isAISDot={true}
                                        totalUnifiedAISDots={[selectedCard]}
                                        aisDetails={aisInfo ? aisDetails : []}
                                        artifactDetails={details}
                                        isShowAISDot={isShowAISDot}
                                        handleUnflagClick={isArtifactFlagged ? handleUnflagClick : undefined}
                                    />
                                </Box>
                            )}
                        </Grid>
                        {
                            <Grid
                                position={"absolute"}
                                bottom={45}
                                left={20}
                                display={{ xs: "none", lg: isShowAISDot ? "flex" : "none" }}
                            >
                                <Grid
                                    container
                                    sx={{
                                        backgroundColor: (theme) => alpha(theme.palette.primary.light, 0.5),
                                        borderRadius: "20px",
                                        width: "auto",
                                        padding: "20px 20px",
                                        minWidth: "300px",
                                    }}
                                >
                                    <Grid
                                        container
                                        width={"100%"}
                                        flexDirection={"column"}
                                        gap={1}
                                    >
                                        <Grid>
                                            <Typography fontSize={"14px"}>
                                                Details
                                            </Typography>
                                        </Grid>
                                        <Grid>
                                            <Grid sx={{ display: "flex", flexDirection: "row", alignItems: "center", gap: 1, justifyContent: "space-between" }}>
                                                <Typography sx={{ fontSize: "12px" }}>
                                                    {vessel?.name || vessel?.vessel_id || "Unknown Vessel"}
                                                </Typography>
                                                <Typography sx={{ fontSize: "12px", textAlign: "right" }}>
                                                    {dayjs(selectedCard?.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                                                </Typography>
                                            </Grid>
                                            <Grid sx={{ display: "flex", flexDirection: "row", alignItems: "center", gap: 1, justifyContent: "space-between" }}>
                                                <Typography sx={{ fontSize: "12px" }}>
                                                    Location
                                                </Typography>
                                                <Typography sx={{ fontSize: "12px", textAlign: "right" }}>
                                                    {key}
                                                </Typography>
                                            </Grid>
                                        </Grid>
                                    </Grid>
                                </Grid>
                            </Grid>
                        }
                        <Grid
                            display={{ xs: "flex", sm: "flex", lg: !isShowAISDot ? "flex" : "none" }}
                            justifyContent={"space-between"}
                            alignItems={"center"}
                            paddingX={1}
                            flexDirection={"row"}
                            size={12}
                        >
                            <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                                {vessel?.name || vessel?.vessel_id || selectedCard?.vesselName || "Unknown Vessel"}
                            </Typography>
                            <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                                {dayjs(selectedCard?.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                            </Typography>
                        </Grid>

                        <Grid width={"100%"} display={{ xs: "flex", sm: "flex", lg: !isShowAISDot ? "block" : "none" }}>
                            {aisInfo ? (
                                <Tabs value={activeTab} onChange={(_, v) => setActiveTab(v)}
                                    variant="fullWidth"
                                    indicatorColor="none"
                                    sx={{
                                        width: "100%",
                                        padding: "4px",
                                        border: `2px solid ${theme.palette.custom.borderColor}`,
                                        borderRadius: "8px",
                                        backgroundColor: "transparent",
                                        "& .MuiTabs-flexContainer": {
                                            height: "100%",
                                        },
                                        "& .MuiButtonBase-root": {
                                            width: "50%",
                                            borderRadius: "8px",
                                        },
                                        "& .MuiButtonBase-root.Mui-selected": {
                                            backgroundColor: theme.palette.custom.mainBlue,
                                        },
                                    }}>
                                    <Tab label="Detection Details" />
                                    <Tooltip title={!aisInfo && "AIS information is not available"}>
                                        <Tab label="AIS Information" sx={{
                                            color: "white",
                                            "&.Mui-disabled": {
                                                color: theme.palette.custom.mediumGrey,
                                                opacity: 1,
                                                cursor: "not-allowed",
                                                pointerEvents: "fill",
                                            },
                                        }}
                                            disabled={!aisInfo} />
                                    </Tooltip>
                                </Tabs>
                            ) : null}
                        </Grid>
                        <Grid
                            container
                            sx={{
                                maxHeight: { xs: "200px", md: "280px", lg: "19vh" },
                                overflow: "auto",
                            }}
                            display={{ xs: "flex", sm: "flex", lg: !isShowAISDot ? "block" : "none" }}
                        >
                            {(!aisInfo || activeTab === 0) && (
                                <Grid container height={{ xs: "200px", md: "350px", lg: "350px" }} gap={1}>
                                    {details.map(({ label, value }, index) => (
                                        <React.Fragment key={index}>
                                            <Grid
                                                display="flex"
                                                alignItems={{
                                                    xs: "flex-start",
                                                    sm: index % 2 == 0 || fieldWithFullWidth.includes(label) ? "flex-start" : "flex-center",
                                                    md: "center"
                                                }}
                                                paddingX={1}
                                                flexDirection="column"
                                                size={{ xs: 12, sm: fieldWithFullWidth.includes(label) ? 12 : 3.9, md: 2.9 }}
                                            >
                                                <Typography
                                                    fontSize="16px"
                                                    fontWeight={500}
                                                    color={theme.palette.custom.mainBlue}
                                                    sx={{ display: "flex", alignItems: "center", gap: "6px", textAlign: { xs: "left", sm: "left", md: "center" } }}
                                                >
                                                    {label}
                                                    {label === "Bearing Angle" && (
                                                        <Tooltip title="Angle measured clockwise between the True North and the Target as observed from own vessel">
                                                            <img src="/icons/info_icon.svg" />
                                                        </Tooltip>
                                                    )}
                                                </Typography>
                                                <Typography
                                                    fontSize="16px"
                                                    fontWeight={500}
                                                    onClick={() => (label === "Location" ? handleNavigation() : null)}
                                                    sx={{
                                                        cursor: label === "Location" ? "pointer" : "default",
                                                        color: label === "Location" ? "#007bff" : "inherit",
                                                        textDecoration: label === "Location" ? "underline" : "none",
                                                        "&:hover":
                                                            label === "Location"
                                                                ? {
                                                                    color: "#0056b3",
                                                                    textDecoration: "underline",
                                                                }
                                                                : {},
                                                        textAlign: { xs: "left", sm: "left", md: "center" }
                                                    }}
                                                    title={label === "Location" ? "Click to view on map" : ""}
                                                >
                                                    {value ?? "--"}
                                                </Typography>
                                            </Grid>
                                        </React.Fragment>
                                    ))}
                                </Grid>
                            )}
                            {aisInfo && activeTab === 1 && (
                                <Grid
                                    container
                                    height={{ xs: "200px", md: "280px", lg: "19vh" }}
                                    gap={1}
                                >
                                    {/* Flag Button */}
                                    <Grid
                                        size={12}
                                        display="flex"
                                        justifyContent="flex-end"
                                        alignItems="center"
                                        paddingX={1}
                                        paddingBottom={1}
                                    >
                                        <Tooltip
                                            title={!isFlagging ? (isFlagged ? "Remove AIS flag" : "Flag AIS") : null}
                                            arrow
                                            placement="left"
                                        >
                                            <IconButton
                                                size="small"
                                                sx={{
                                                    height: 32,
                                                    width: 32,
                                                    padding: 0,
                                                    color: "#fff",
                                                    backgroundColor: isFlagged ? "#E60000CC" : "rgba(0,0,0,0.5)",
                                                    borderRadius: "50%",
                                                    "&:hover": {
                                                        backgroundColor: isFlagged ? "#E60000" : "rgba(0,0,0,0.7)",
                                                    },
                                                }}
                                                onClick={handleFlagClick}
                                                disabled={isFlagging}
                                            >
                                                {isFlagging ? (
                                                    <CircularProgress sx={{ color: "white" }} size={18} />
                                                ) : isFlagged ? (
                                                    <FlagIcon sx={{ height: 18 }} />
                                                ) : (
                                                    <OutlinedFlagIcon sx={{ height: 18 }} />
                                                )}
                                            </IconButton>
                                        </Tooltip>
                                    </Grid>
                                    {aisDetails.map(({ label, value }, index) => (
                                        <React.Fragment key={index}>
                                            <Grid
                                                display="flex"
                                                alignItems={{
                                                    xs: "flex-start",
                                                    sx: "flex-start",
                                                    md: "center"
                                                }}
                                                paddingX={1}
                                                flexDirection="column"
                                                size={{ xs: 12, sm: AISFieldWithFullWidth.includes(label) ? 12 : 3.9, md: 2.9 }}
                                            >
                                                <Typography
                                                    fontSize="16px"
                                                    fontWeight={500}
                                                    color={theme.palette.custom.mainBlue}
                                                    sx={{ display: "flex", alignItems: "center", gap: "6px", textAlign: { xs: "left", sm: "left", md: "center" } }}
                                                >
                                                    {label}
                                                </Typography>
                                                <Typography
                                                    fontSize="16px"
                                                    fontWeight={500}
                                                    sx={{
                                                        cursor: "default",
                                                        color: "inherit",
                                                        textAlign: { xs: "left", sm: "left", md: "center" }
                                                    }}
                                                    title={label === "Location" ? "Click to view on map" : ""}
                                                >
                                                    {value ?? "--"}
                                                </Typography>
                                            </Grid>
                                        </React.Fragment>
                                    ))}
                                </Grid>
                            )}
                        </Grid>
                    </Grid>
                </ModalContainer>
            </Modal>
            <RemoveFlagConfirmationModal open={showRemoveModal} onClose={handleRemoveCancel} onConfirm={handleRemoveConfirm} isLoading={isRemoving} />
        </>
    );
}
