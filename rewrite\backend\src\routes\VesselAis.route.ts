import express, { NextFunction, Request, Response } from "express";
import { validateData } from "../middlewares/validator";
import { query, body, param } from "express-validator";
import { validateError, canAccessVessel } from "../utils/functions";
import { isValidObjectId } from "mongoose";
import isAuthenticated from "../middlewares/auth";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import db from "../modules/db";
import compression from "compression";
import vesselService from "../services/Vessel.service";
import { IVesselAis } from "../interfaces/VesselAis";
import { IQueryFilter } from "src/interfaces/Common";
import aisVesselFlagService from "../services/AisVesselFlag.service";
import hasPermission from "../middlewares/hasPermission";
import { permissions } from "../utils/permissions";
import ioEmitter from "../modules/ioEmitter";

const router = express.Router();

const lookupAisProjection = {
    _id: "$data.last_message_id",
    location: "$data.location",
    "metadata.onboard_vessel_id": "$data.metadata.onboard_vessel_id",
    "details.message": "$data.details.message",
    name: "$data.name",
    "metadata.mmsi": "$mmsi",
    timestamp: "$data.last_message_timestamp",
};

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

// const getRandomDeviation = (): number => {
//     return Math.random() * 0.001;
// };

// router.post(
//     "/:vesselId",
//     assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_AIS),
//     isAuthenticated,
//     (req: Request, res: Response, next: NextFunction) =>
//         validateData(
//             [
//                 // the below cannot be verified by test cases
//                 // param("vesselId").isMongoId().withMessage("Invalid vessel ID"),
//                 body("startTimestampISO")
//                     .isISO8601()
//                     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
//                     .optional(),
//                 body("endTimestampISO")
//                     .isISO8601()
//                     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
//                     .optional(),
//             ],
//             req,
//             res,
//             next,
//         ),
//     async (req: Request, res: Response) => {
//         const requestURL = req.get("Referer");
//         const isSwagger = requestURL ? requestURL.includes("/docs") : false;
//         let isClosed = false;

//         const onClose = () => {
//             isClosed = true;
//         };

//         res.on("close", onClose);

//         try {
//             const { vesselId } = req.params as { vesselId: string };
//             const { startTimestampISO, endTimestampISO, lastKnown }: { startTimestampISO?: string; endTimestampISO?: string; lastKnown?: boolean } =
//                 req.body;
//             console.log(`/vesselAis ${vesselId}`, startTimestampISO, endTimestampISO, lastKnown);

//             if (!vesselId || !mongoose.Types.ObjectId.isValid(vesselId)) return res.status(400).json({ message: "Invalid vessel ID" });

//             if (endTimestampISO && !startTimestampISO) {
//                 return res.status(400).json({ message: "startTimestampISO is required when endTimestampISO is provided" });
//             }

//             const vessel: IVessel | null = await vesselService.findById({ id: vesselId });
//             if (!vessel) return res.status(404).json({ message: "Vessel does not exist" });

//             if (!canAccessVessel(req, vessel)) {
//                 return res.status(403).json({ message: `Cannot access coordinates for vessel '${vesselId}'` });
//             }

//             const historicalUnitIds: string[] = getUnitIdsFromVessel(vessel);
//             if (historicalUnitIds.length === 0) return res.status(400).json({ message: "Vessel has no associated unit_id in history" });

//             console.log(`/vesselAis ${vesselId} historicalUnitIds`, historicalUnitIds);

//             const ts: number = new Date().getTime();

//             const collections = historicalUnitIds.map((unitId: string) => db.ais.collection(`${unitId}_ais`));

//             // console.log(`/vesselAis ${vesselId} collections`, collections)

//             const query: IQueryFilter = { onboard_vessel_id: new mongoose.Types.ObjectId(vesselId) };

//             if (startTimestampISO) {
//                 const endTime: string | number = endTimestampISO || Date.now();
//                 query.timestamp = { $gte: new Date(startTimestampISO), $lte: new Date(endTime) };
//             }

//             console.log(`/vesselAis ${vesselId} query`, query);

//             const ais: IVesselAis | IVesselAis[] | null = await limitPromise(async (): Promise<IVesselAis | IVesselAis[] | null> => {
//                 if (isClosed) {
//                     res.end();
//                     return null;
//                 }
//                 console.log(`/vesselAis ${vesselId} querying DB`);

//                 if (lastKnown) {
//                     const aisPromises = collections.map((collection) =>
//                         collection.findOne(
//                             { onboard_vessel_id: new mongoose.Types.ObjectId(vesselId) },
//                             {
//                                 projection: aisProjection,
//                                 sort: { timestamp: -1 },
//                             },
//                         ),
//                     );

//                     const allAis = (await Promise.all(aisPromises)).filter(Boolean) as IVesselAis[];

//                     if (allAis.length === 0) return null;

//                     return allAis.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())[0];
//                 } else {
//                     const aisPromises = collections.map((collection) => {
//                         const cursor = collection.find(query, {
//                             projection: aisProjection,
//                         });

//                         if (isSwagger) {
//                             cursor.limit(20);
//                         }

//                         return cursor.toArray();
//                     });

//                     const allAis = await Promise.all(aisPromises);
//                     const flattenedResults: IVesselAis[] = allAis
//                         .flat()
//                         // temporary randomization logic for mock data
//                         .map((ais: any) => ({
//                             ...ais,
//                             location: ais.location && {
//                                 ...ais.location,
//                                 coordinates: [ais.location.coordinates[0] + getRandomDeviation(), ais.location.coordinates[1] + getRandomDeviation()],
//                             },
//                         }));

//                     return flattenedResults.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
//                 }
//             });

//             console.log(`/vesselAis ${vesselId} time taken to query ${new Date().getTime() - ts}`);
//             console.log(`/vesselAis ${vesselId} received ${(ais && Array.isArray(ais) ? ais.length : 1) || 0} ais`);
//             console.log(`/vesselAis ${vesselId} time taken to respond ${new Date().getTime() - ts}`);

//             if (isClosed) return res.end();

//             res.json(ais);
//         } catch (err) {
//             validateError(err, res);
//         } finally {
//             res.removeListener("close", onClose);
//         }
//     },
// );

router.get(
    "/latest",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSEL_AIS_LATEST),
    isAuthenticated,
    validateData.bind(this, [
        query("vesselIds")
            .isString()
            .withMessage(`vesselIds is a required string`)
            .notEmpty()
            .withMessage(`vesselIds must be a comma-separated string`)
            .if(query("vesselIds").exists())
            .customSanitizer((v: string) => v.split(",").map((v: string) => v.trim()))
            .custom((v: string[]) => v.every((id: string) => isValidObjectId(id)))
            .withMessage(`vesselIds must be valid object IDs`),
        query("startTimestampISO")
            .isISO8601()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
            .optional(),
        // not supported at the moment
        // query("endTimestamp")
        //     .isInt()
        //     .customSanitizer((v) => Number(v))
        //     .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
        //     .optional(),
    ]),
    async (req: Request, res: Response) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const { vesselIds, startTimestampISO } = req.query as { vesselIds: string[]; startTimestampISO?: string };

            const ts: number = new Date().getTime();

            const query: IQueryFilter = {};

            if (startTimestampISO) {
                query.last_message_timestamp = { $gte: new Date(startTimestampISO) };
            }

            const vessels = await vesselService.find({ _id: { $in: vesselIds } });
            const assignedVessels = vessels.filter((vessel) => canAccessVessel(req, vessel));

            query.onboard_vessel_id = { $in: assignedVessels.map((v) => v._id) };

            console.log(`/vesselAis query`, query);

            console.log(`/vesselAis querying DB`);

            const cursor = db.lookups.collection<IVesselAis>("ais_mmsi_lookup").find(query, { projection: lookupAisProjection });

            if (isSwagger) {
                cursor.limit(20);
            }

            const aisMessages = await cursor.toArray();

            // console.log(`/vesselAis lookupCollectionIds`, lookupCollectionIds);

            // const aisMessages: IVesselAis[] = (
            //     await Promise.all(
            //         Object.entries(lookupCollectionIds).flatMap(([collection, ids]: [string, string[]]) => {
            //             return db.aisRaw
            //                 .collection<IVesselAis>(collection)
            //                 .find({ "metadata._id": { $in: ids } }, { projection: aisProjection })
            //                 .toArray();
            //         }),
            //     )
            // ).flat();

            console.log(`/vesselAis time taken to query ${new Date().getTime() - ts}`);

            // moved this logic to frontend
            // deduplicate aisMessages: keep the latest message for each mmsi
            // const deduplicatedAisMessages = aisMessages
            //     .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
            //     .filter((ais, index, self) => index === self.findIndex((t) => t.mmsi === ais.mmsi));

            // console.log('aisMessages', aisMessages);

            const groupedAis = aisMessages.reduce((acc: { [vesselId: string]: IVesselAis[] }, ais: IVesselAis) => {
                const vesselId = ais.metadata.onboard_vessel_id?.toString();
                if (!vesselId) return acc;
                if (!acc[vesselId]) {
                    acc[vesselId] = [];
                }
                acc[vesselId].push(ais);
                return acc;
            }, {});

            vesselIds.forEach((vesselId: string) => {
                if (!groupedAis[vesselId]) {
                    groupedAis[vesselId] = [];
                }
            });

            console.log(`/vesselAis received ${(aisMessages && aisMessages.length) || 0} ais`);
            console.log(`/vesselAis time taken to respond ${new Date().getTime() - ts}`);

            if (isClosed) return res.end();

            res.json(groupedAis);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

router.post(
    "/flag",
    assignEndpointId.bind(this, endpointIds.FLAG_AIS_VESSEL),
    isAuthenticated,
    validateData.bind(this, [body("mmsi").isString().notEmpty().withMessage("MMSI is required")]),
    async (req: Request, res: Response) => {
        try {
            const { mmsi } = req.body;
            const userId = req.user._id;
            const flag = await aisVesselFlagService.flagAisVessel(mmsi, userId as string);
            res.json({ message: "AIS vessel flagged successfully", flag });
        } catch (err: any) {
            if (err.message === "MMSI is required") {
                return res.status(400).json({ message: err.message });
            }
            if (err.message === "You have already flagged this AIS vessel") {
                return res.status(400).json({ message: err.message });
            }
            validateError(err, res);
        }
    },
);

router.post(
    "/unflag",
    assignEndpointId.bind(this, endpointIds.UNFLAG_AIS_VESSEL),
    isAuthenticated,
    validateData.bind(this, [body("mmsi").isString().notEmpty().withMessage("MMSI is required")]),
    async (req: Request, res: Response) => {
        try {
            const { mmsi } = req.body;
            const userId = req.user._id;
            const flag = await aisVesselFlagService.unflagAisVessel(mmsi, userId as string);
            res.json({ message: "AIS vessel unflagged successfully", flag });
        } catch (err: any) {
            if (err.message === "Flag not found") {
                return res.status(404).json({ message: err.message });
            }
            validateError(err, res);
        }
    },
);

router.get(
    "/flagged",
    assignEndpointId.bind(this, endpointIds.FETCH_FLAGGED_AIS_VESSELS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageArtifacts]),
    (req: Request, res: Response, next: NextFunction) =>
        validateData(
            [
                query("page").isInt({ min: 1 }).withMessage("Page must be a positive integer"),
                query("pageSize").isInt({ min: 1, max: 100 }).withMessage("Page size must be between 1 and 100"),
            ],
            req,
            res,
            next,
        ),
    async (req: Request, res: Response) => {
        try {
            const page = parseInt(req.query.page as string, 10);
            const pageSize = parseInt(req.query.pageSize as string, 10);
            const result = await aisVesselFlagService.getFlaggedAisVessels(page, pageSize);
            res.json(result);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get("/flagged/user", assignEndpointId.bind(this, endpointIds.FETCH_USER_FLAGGED_AIS_VESSEL_MMSIS), isAuthenticated, async (req, res) => {
    try {
        const userId = req.user._id;
        const mmsis = await aisVesselFlagService.getUserFlaggedAisVesselMmsis(userId as string);
        res.json({ flaggedMmsis: mmsis });
    } catch (err) {
        validateError(err, res);
    }
});

router.delete(
    "/flagged/:mmsi",
    assignEndpointId.bind(this, endpointIds.REMOVE_ALL_FLAGS_FROM_AIS_VESSEL),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageArtifacts]),
    validateData.bind(this, [param("mmsi").isString().notEmpty().withMessage("MMSI is required")]),
    async (req: Request, res: Response) => {
        try {
            const { mmsi } = req.params;
            const result = await aisVesselFlagService.removeAllFlagsFromAisVessel(mmsi);
            if (result.deletedCount > 0) {
                ioEmitter.emit("notifyAll", { name: "ais_vessels_flagged/changed" });
            }
            res.json({ message: "All flags removed from AIS vessel successfully", ...result });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;

/**
 * @swagger
 * /vesselAis/latest:
 *   get:
 *     summary: Fetch latest AIS data for multiple vessels
 *     description: Retrieves the latest AIS (Automatic Identification System) data for multiple vessels. AIS data includes vessel position, speed, course, and other navigational information. Rate limited to 20 requests every 5 seconds.
 *     tags: [Vessel AIS]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: vesselIds
 *         required: true
 *         schema:
 *           type: string
 *         description: Comma-separated list of vessel IDs to fetch AIS data for
 *         example: "687205ff3e3f038346b4cafc"
 *       - in: query
 *         name: startTimestampISO
 *         required: false
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Start timestamp in ISO 8601 format for filtering AIS messages
 *         example: "2025-10-01T00:00:00.000Z"
 *     responses:
 *       200:
 *         description: Successfully retrieved AIS data grouped by vessel
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 "687205ff3e3f038346b4cafc":
 *                   type: array
 *                   description: Array of AIS messages for vessel 687205ff3e3f038346b4cafc
 *                   items:
 *                     type: object
 *                     description: AIS message object with location, metadata, and detailed message data
 *                     example:
 *                       location:
 *                         type: "Point"
 *                         coordinates: [121.72900833333334, 27.743316666666665]
 *                       metadata:
 *                         onboard_vessel_id: "687205ff3e3f038346b4cafc"
 *                         mmsi: "412266698"
 *                       details:
 *                         message:
 *                           timestamp: "2025-09-20T16:50:52.369Z"
 *                           timestamp_seconds: 1758387052.369
 *                           signalk_id: "urn:mrn:imo:mmsi:412266698"
 *                           name: "66698"
 *                           mmsi: "412266698"
 *                           comm_callsign_vhf: "66698"
 *                           design_ais_ship_type_id: 30
 *                           design_ais_ship_type_name: "Fishing"
 *                           design_beam: 8
 *                           design_length: 52
 *                           design_length_type: "overall"
 *                           nav_latitude: 27.743316666666665
 *                           nav_longitude: 121.72900833333334
 *                           nav_heading_true: 0
 *                           nav_course_over_ground_true: 0.3525565089833536
 *                           nav_speed_over_ground: 4.52711225797955
 *                           nav_special_maneuver: ""
 *                           nav_state: ""
 *                           sensor_ais_class: "B"
 *                           sensor_ais_from_bow: 30
 *                           sensor_ais_from_center: 0
 *                           host_location_valid: true
 *                           host_location_latitude: 27.729080099999997
 *                           host_location_longitude: 121.7151122
 *                           host_location_altitude: 13.933
 *                           portal_valid: true
 *                       name: "66698"
 *       400:
 *         description: Bad request - invalid vessel IDs or timestamp format
 *       401:
 *         description: Unauthorized - authentication required
 *       403:
 *         description: Forbidden - insufficient permissions to access vessels
 *       500:
 *         description: Internal server error
 */
