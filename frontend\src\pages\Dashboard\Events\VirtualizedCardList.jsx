import { Grid, Typography, CircularProgress, Box, Fab } from "@mui/material";
import { memo, useRef, useCallback, useState, useEffect, forwardRef } from "react";
import VirtualizedList from "./VirtualizedList";
import { SentimentVeryDissatisfied, KeyboardArrowUp } from "@mui/icons-material";
import VirtualizedCardListRow from "./VirtualizedCardListRow";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";

// Scroll configuration
const SCROLL_CONFIG = {
    baseThreshold: 0.7, // Starting threshold (70%)
    maxThreshold: 0.99, // Maximum threshold (99%)
    incrementPer100: 0.05, // Threshold increase per 100 records (5%)
    clientHeightOffset: 10, // Offset for clientHeight calculation
};

const VirtualizedCardList = forwardRef(
    (
        {
            events,
            setShowDetailModal,
            setSelectedCard,
            isLoading,
            onLoadMore,
            hasMore,
            containerRef,
            CustomCard,
            onFlaggedByClick,
            buttonsToShow,
            list,
            itemHeight,
        },
        ref,
    ) => {
        const { screenSize } = useApp();
        const listRef = useRef();
        const prevScrollOffsetRef = useRef(0);
        const [isScrolling, setIsScrolling] = useState(false);
        const [showScrollToTop, setShowScrollToTop] = useState(false);
        const [containerHeight, setContainerHeight] = useState(0);
        const [containerWidth, setContainerWidth] = useState(0);

        useEffect(() => {
            if (ref) {
                ref.current = {
                    scrollToTop: () => {
                        if (listRef.current) {
                            listRef.current.scrollTo(0);
                        }
                    },
                };
            }
        }, [ref]);

        useEffect(() => {
            const target = containerRef?.current;
            if (!target) return;

            const updateSize = () => {
                const newHeight = target.clientHeight;
                const newWidth = target.clientWidth;
                setContainerHeight(isLoading ? newHeight - 70 : newHeight);
                setContainerWidth(newWidth);
            };

            updateSize();
            const resizeObserver = new ResizeObserver(updateSize);
            resizeObserver.observe(target);

            return () => {
                resizeObserver.unobserve(target);
            };
        }, [containerRef, isLoading]);

        useEffect(() => {
            if (listRef.current) {
                listRef.current.resetAfterIndex(0);

                if (!isLoading && onLoadMore && hasMore && events.length > 0 && containerHeight > 0) {
                    const checkAutoLoad = setTimeout(() => {
                        if (!listRef.current?._outerRef) return;

                        const { scrollHeight, clientHeight } = listRef.current._outerRef;
                        if (scrollHeight <= clientHeight + SCROLL_CONFIG.clientHeightOffset && hasMore) {
                            onLoadMore();
                        }
                    }, 300);
                    return () => clearTimeout(checkAutoLoad);
                }
            }
        }, [events, screenSize, containerWidth, isLoading, onLoadMore, hasMore, containerHeight]);

        useEffect(() => {
            if (!isLoading && onLoadMore && hasMore && listRef.current && events.length > 0 && containerHeight > 0) {
                const containerElement = containerRef?.current;
                if (containerElement) {
                    // If the container is hidden (e.g., its parent tab has display: none), it will have no client rects
                    const isHidden = containerElement.getClientRects().length === 0;
                    if (isHidden) {
                        return; // Do not auto-load when not visible
                    }
                    const computedStyle = window.getComputedStyle(containerElement);
                    const isVisible = computedStyle.display !== "none" && computedStyle.visibility !== "hidden";
                    if (!isVisible) {
                        return;
                    }
                }

                const checkAutoLoad = setTimeout(() => {
                    if (!listRef.current?._outerRef) return;

                    const { scrollHeight, clientHeight } = listRef.current._outerRef;
                    if (scrollHeight <= clientHeight + 10 && hasMore) {
                        onLoadMore();
                    }
                }, 200);

                return () => clearTimeout(checkAutoLoad);
            }
        }, [events, isLoading, onLoadMore, hasMore, containerRef, containerHeight]);

        const getColumnCount = useCallback(() => {
            // Prefer dynamic calculation by container width to avoid overly stretched cards on large screens.
            const minCardWidth = 300; // px
            const gap = 16; // approximate horizontal gap per card (spacing=2 ~ 16px)
            const width = containerWidth || 0;
            if (width <= 0) {
                // Fallback to screen size if width not yet measured
                if (screenSize.xs) return 1;
                if (screenSize.sm) return 2;
                if (screenSize.md) return 3;
                if (screenSize.lg) return 4;
                return 5;
            }
            const columns = Math.max(1, Math.floor((width + gap) / (minCardWidth + gap)));
            // Put an upper bound if needed to avoid too many columns
            return Math.min(columns, 12);
        }, [containerWidth, screenSize]);

        const getItemSize = (index) => {
            const columnCount = getColumnCount();
            const rowIndex = Math.floor(index / columnCount);
            const defaultHeight = itemHeight || 350;
            return rowIndex === 0 ? defaultHeight : defaultHeight;
        };

        const checkScrollPosition = useCallback((scrollOffset, clientHeight, scrollHeight) => {
            const itemCount = events.length;
            // Increase threshold by incrementPer100 for each 100 records
            const increments = Math.floor(itemCount / 100);
            const threshold = Math.min(
                SCROLL_CONFIG.baseThreshold + (increments * SCROLL_CONFIG.incrementPer100),
                SCROLL_CONFIG.maxThreshold
            );
            const scrollPosition = scrollOffset + clientHeight + SCROLL_CONFIG.clientHeightOffset;
            return scrollPosition >= scrollHeight * threshold;
        }, [events.length]);

        const handleScroll = useCallback(
            ({ scrollOffset, scrollUpdateWasRequested, scrollDirection }) => {
                // Show/hide scroll to top button
                setShowScrollToTop(scrollOffset > 100);

                if (!isLoading && onLoadMore && !scrollUpdateWasRequested && listRef.current) {
                    const { scrollHeight, clientHeight } = listRef.current._outerRef;

                    const isScrollingDown = scrollDirection === "forward" || scrollOffset > prevScrollOffsetRef.current;
                    prevScrollOffsetRef.current = scrollOffset;

                    if (isScrollingDown && checkScrollPosition(scrollOffset, clientHeight, scrollHeight) && hasMore && !isScrolling) {
                        setIsScrolling(true);
                        onLoadMore();
                        setTimeout(() => setIsScrolling(false), 1000);
                    }
                }
            },
            [hasMore, isLoading, onLoadMore, isScrolling, checkScrollPosition],
        );

        const scrollToTop = useCallback(() => {
            if (listRef.current) {
                listRef.current.scrollTo(0);
            }
        }, []);

        if (isLoading && events.length === 0) {
            return (
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size={12}>
                    <CircularProgress />
                </Grid>
            );
        }

        if (events.length === 0) {
            return (
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size={12}>
                    <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"}>
                        <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
                        <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                            No data available
                        </Typography>
                    </Grid>
                </Grid>
            );
        }

        const columnCount = getColumnCount();

        return (
            <>
                <VirtualizedList
                    listRef={listRef}
                    containerHeight={containerHeight}
                    getItemSize={getItemSize}
                    handleScroll={handleScroll}
                    Row={VirtualizedCardListRow}
                    items={events}
                    columnCount={columnCount}
                    rowData={{
                        CustomCard,
                        setShowDetailModal,
                        setSelectedCard,
                        onFlaggedByClick,
                        buttonsToShow,
                        list,
                    }}
                />
                {isLoading && events.length > 0 && (
                    <Box display="flex" justifyContent="center" width={"100%"} padding={2}>
                        <CircularProgress />
                    </Box>
                )}
                {showScrollToTop && (
                    <Fab
                        color="primary"
                        size="small"
                        onClick={scrollToTop}
                        sx={{
                            backgroundColor: theme.palette.custom.mainBlue,
                            position: "fixed",
                            bottom: 25,
                            right: 50,
                            borderRadius: "8px",
                            zIndex: 1000,
                            "&:hover": {
                                backgroundColor: "#FFFFFF",
                                color: theme.palette.custom.mainBlue,
                            },
                        }}
                        aria-label="scroll to top"
                    >
                        <KeyboardArrowUp />
                    </Fab>
                )}
            </>
        );
    },
);

VirtualizedCardList.displayName = "VirtualizedCardList";
export default memo(VirtualizedCardList);
