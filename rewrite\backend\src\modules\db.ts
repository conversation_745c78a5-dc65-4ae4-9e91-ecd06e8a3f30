import mongoose from "mongoose";

mongoose.set("strictQuery", false);

if (!process.env.MONGO_URI) {
    throw new Error("MONGO_URI must be set in env variables");
}

if (!process.env.MONGO_SEARCH_URI) {
    throw new Error("MONGO_SEARCH_URI must be set in env variables");
}

const NODE_ENV = process.env.NODE_ENV;

if (!NODE_ENV) {
    throw new Error("NODE_ENV must be set in env variables");
}

const db = {
    qm: mongoose.createConnection(process.env.MONGO_URI, {
        dbName: ["portal", "infra"].includes(NODE_ENV)
            ? "quartermaster"
            : ["dev"].includes(NODE_ENV)
              ? "quartermaster-dev"
              : ["staging"].includes(NODE_ENV)
                ? "quartermaster-staging"
                : "quartermaster-local",
    }),
    qmai: mongoose.createConnection(process.env.MONGO_URI, {
        dbName: ["portal", "infra"].includes(NODE_ENV)
            ? "artifact_processor"
            : ["dev"].includes(NODE_ENV)
              ? "artifact_processor-dev"
              : ["staging"].includes(NODE_ENV)
                ? "artifact_processor-staging"
                : "artifact_processor-local",
    }),
    qmShared: mongoose.createConnection(process.env.MONGO_URI, {
        dbName: ["portal", "infra"].includes(NODE_ENV)
            ? "quartermaster-shared"
            : ["dev"].includes(NODE_ENV)
              ? "quartermaster-shared-dev"
              : ["staging"].includes(NODE_ENV)
                ? "quartermaster-shared-staging"
                : "quartermaster-shared-local",
    }),
    locationsOptimized: mongoose.createConnection(process.env.MONGO_URI, { dbName: "locations_optimized" }),
    locationsRaw: mongoose.createConnection(process.env.MONGO_URI, { dbName: "locations_raw" }),
    aisRaw: mongoose.createConnection(process.env.MONGO_URI, { dbName: "ais_raw" }),
    audio: mongoose.createConnection(process.env.MONGO_URI, { dbName: "audio_processor" }),
    lookups: mongoose.createConnection(process.env.MONGO_URI, { dbName: "lookups" }),
    searchArtifacts: mongoose.createConnection(process.env.MONGO_SEARCH_URI, { dbName: "artifacts" }),
};

db.qm.on("open", () => console.log("DB connected to Quartermaster"));
db.qmai.on("open", () => console.log("DB connected to QMAI"));
db.qmShared.on("open", () => console.log("DB connected to Quartermaster-Shared"));
db.locationsOptimized.on("open", () => console.log("DB connected to Locations Optimized"));
db.locationsRaw.on("open", () => console.log("DB connected to Locations Raw"));
db.aisRaw.on("open", () => console.log("DB connected to AIS Raw"));
db.audio.on("open", () => console.log("DB connected to QMAudio"));
db.lookups.on("open", () => console.log("DB connected to Lookups"));
db.searchArtifacts.on("open", () => console.log("DB connected to Search Artifacts"));

db.qm.on("error", (err) => console.error(err));
db.qmai.on("error", (err) => console.error(err));
db.qmShared.on("error", (err) => console.error(err));
db.locationsOptimized.on("error", (err) => console.error(err));
db.locationsRaw.on("error", (err) => console.error(err));
db.aisRaw.on("error", (err) => console.error(err));
db.audio.on("error", (err) => console.error(err));
db.lookups.on("error", (err) => console.error(err));
db.searchArtifacts.on("error", (err) => console.error(err));

export default db;
