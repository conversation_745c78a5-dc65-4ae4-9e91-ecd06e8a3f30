import { jest } from '@jest/globals';

type AnyObject = Record<string, any>;

function MockList(this: AnyObject, data: AnyObject) {
    Object.assign(this, data);
}

const createChainableMock = (mockResolvedValue: any) => {
    const chainable = {
        lean: jest.fn().mockResolvedValue(mockResolvedValue as never),
        select: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
    };
    chainable.select.mockReturnValue(chainable);
    chainable.sort.mockReturnValue(chainable);
    return chainable;
};

(MockList as AnyObject).find = jest.fn().mockReturnValue(createChainableMock([]));
(MockList as AnyObject).findOne = jest.fn().mockReturnValue(createChainableMock(null));
(MockList as AnyObject).findById = jest.fn().mockReturnValue(createChainableMock(null));
(MockList as AnyObject).create = jest.fn();
(MockList as AnyObject).updateOne = jest.fn();
(MockList as AnyObject).deleteOne = jest.fn();
(MockList as AnyObject).aggregate = jest.fn();

(MockList as AnyObject).prototype = {
    save: jest.fn().mockResolvedValue(true as never),
    toObject: jest.fn().mockReturnValue({}),
};

export default MockList as any;

