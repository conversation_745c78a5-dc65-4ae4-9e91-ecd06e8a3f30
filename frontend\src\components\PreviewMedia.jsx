import React, { useEffect, useState } from "react";
import { Box, CircularProgress, Modal, IconButton, Skeleton, Tooltip } from "@mui/material";
import PlayCircleOutlineIcon from "@mui/icons-material/PlayCircleOutline";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import ShareModal from "./ShareModal";
import VideoPlayer from "../components/VideoPlayer";
import { useUser } from "../hooks/UserHook";
import useStore from "../hooks/Store";
import favouriteArtifactsController from "../controllers/FavouriteArtifacts.controller";
import listsController from "../controllers/Lists.controller";
import artifactFlagController from "../controllers/ArtifactFlag.controller";
import artifactController from "../controllers/Aritfact.controller";
import axiosInstance from "../axios.js";
import { getClampedBbox, procDownloadResponse } from "../utils.js";
import FullscreenMediaModal from "./FullscreenMediaModal";
import HelperIcons from "./HealperIcons.jsx";
import DetailVideoPlayer from "./DetailVideoPlayer.jsx";
import ArchiveConfirmModal from "./ArchiveConfirmModal";
import idb from "../indexedDB.js";
import environment from "../../environment.js";
import FlagIcon from "@mui/icons-material/Flag";
import OutlinedFlagIcon from "@mui/icons-material/OutlinedFlag";
import { helperIconModes, computeDisplayedImageRect } from "../utils";
import WarningIcon from '@mui/icons-material/Warning';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import AISEvaluationModal from "./AISEvaluationModal.jsx";
import { getSocket } from "../socket";
import RemoveFromListsModal from "./RemoveFromListsModal.jsx";
import EventPopperDetail from "./EventPopperDetail.jsx";
import EventPingDot from "./EventPingDot.jsx";
const PreviewMedia = ({
    thumbnailLink,
    originalLink,
    isImage,
    isBounding = false,
    det_nbbox,
    showVideoModal = false,
    style = {},
    skeletonStyle = { borderRadius: "8px", height: 200 },
    cardId,
    showFullscreenIcon = false,
    showVideoThumbnail = false,
    showArchiveButton = false,
    showFlagButton = true,
    showFullscreenIconForMap,
    // userTest,
    onThumbnailClick,
    isArchived = false,
    vesselId,
    direction,
    buttonsToShow,
    handleUnflagClick,
    flaggedArtifact,
    isGrouped = false,
    canArchiveAllArtifacts = true,
    groupArtifacts = [],
    isUnified = false,
    unifiedArtifacts = [],
    helperIconsPosition,
    showUnifiedNavigation = false,
    unifiedNavigation = {},
    showAISFlagButton = false,
    flaggedAISArtifact,
    isAISDot = false,
    aisDetails = [],
    artifactDetails = [],
    totalUnifiedAISDots = [],
    isShowAISDot = false,
    isImageLoading,
    list,
    onUnifiedDotClick,
}) => {
    const [aisData, setAisData] = useState(aisDetails);
    const [artifactData, setArtifactData] = useState(artifactDetails);
    const [currentFavouritesArtifacts, setCurrentFavouritesArtifacts] = useState([]);
    const fieldWithFullWidth = ["Text Detected", "Description", "Features"];
    const AISFieldWithFullWidth = ["Ship Length", "AIS Ship Message Class", "AIS Ship State", "AIS Ship Type",
        "Ship Length Type", "Location", "Speed over ground", "VHF Call Sign", "Registry Country", "AIS Bearing Angle", "Timestamp"];
    // console.log("unified artifacts", totalUnifiedAISDots)
    // Update artifactData when artifactDetails prop changes
    useEffect(() => {
        if (artifactDetails && artifactDetails.length > 0) {
            setArtifactData(artifactDetails.filter(({ label }) => label !== "Location"));
        }
    }, [artifactDetails]);
    // Update aisData when aisDetails prop changes
    useEffect(() => {
        if (aisDetails && aisDetails.length > 0) {
            setAisData(aisDetails);
        }
    }, [aisDetails]);

    // Listen for localStorage changes
    useEffect(() => {
        setCurrentFavouritesArtifacts(localStorage.getItem("favouritesArtifacts") ? JSON.parse(localStorage.getItem("favouritesArtifacts")) : []);

        const handleCustomStorageChange = (e) => {
            if (e.detail.key === "favouritesArtifacts") {
                console.log("PreviewMedia: Custom storage event for favouritesArtifacts", e.detail.newValue);
                setCurrentFavouritesArtifacts(e.detail.newValue ? JSON.parse(e.detail.newValue) : []);
                // setCurrentFavouritesArtifacts(localStorage.getItem("favouritesArtifacts") ? JSON.parse(localStorage.getItem("favouritesArtifacts")) : []);
            }
        };
        window.addEventListener("localStorageChange", handleCustomStorageChange);
        return () => {
            window.removeEventListener("localStorageChange", handleCustomStorageChange);
        };
    }, []);

    const [isShareModalOpen, setIsShareModalOpen] = useState(false);
    const [isDownloading, setIsDownloading] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [isFavouriteLoading, setIsFavouriteLoading] = useState(true);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [fullscreenOpen, setFullscreenOpen] = useState(false);
    const [isFavourite, setIsFavourite] = useState(false);
    const mediaRef = React.useRef(null);
    const { user } = useUser();
    const { selectedSaveList } = useStore();
    const [currentTime, setCurrentTime] = useState(0);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [archiveAction, setArchiveAction] = useState(null); // 'archive' or 'unarchive'
    const [isArchiving, setIsArchiving] = useState(false);
    const [archived, setArchived] = useState(false);
    const containerRef = React.useRef(null);
    const [isFlagged, setIsFlagged] = useState(false);
    const [isFlagging, setIsFlagging] = useState(false);
    const [imageDisplayRect, setImageDisplayRect] = useState({ left: 0, top: 0, width: 0, height: 0 });
    const [isAisFlagged, setIsAisFlagged] = useState(null);
    const [isAISModalOpen, setIsAISModalOpen] = useState(false);
    const [showRemoveFromListsModal, setShowRemoveFromListsModal] = useState(false);
    const [listsContainingArtifact, setListsContainingArtifact] = useState([]);
    const [isArtifactInFavorites, setIsArtifactInFavorites] = useState(false);

    useEffect(() => {
        const checkStatus = async () => {
            if (!cardId) {
                setIsFavourite(false);
                setIsFavouriteLoading(false);
                return;
            }

            setIsFavouriteLoading(true);

            const targetListId = list?.id || selectedSaveList?.id;

            if (targetListId === "favorites") {
                const isCurrentlyFavourite = currentFavouritesArtifacts?.some((id) => id === cardId);
                setIsFavourite(isCurrentlyFavourite);
                setIsFavouriteLoading(false);
                return;
            }

            if (targetListId) {
                try {
                    const isInList = await listsController.isArtifactInList(targetListId, cardId);
                    setIsFavourite(isInList);
                } catch (error) {
                    console.error("Error checking if artifact is in list", error);
                    setIsFavourite(false);
                }
            } else {
                setIsFavourite(false);
            }
            setIsFavouriteLoading(false);
        };

        checkStatus();
    }, [cardId, currentFavouritesArtifacts, selectedSaveList, list?.id]);

    useEffect(() => {
        setArchived(isArchived);
    }, [isArchived]);
    // Initialize AIS flag state from artifact data
    useEffect(() => {
        const fetchAISFlaggedStatus = async () => {
            if (!flaggedAISArtifact?._id) return;
            try {
                const response = await artifactController.getArtifactDetail(flaggedAISArtifact._id);
                // console.log("AIS result ", response);
                if (response && response.portal) {
                    // console.log("AIS data", response);
                    setIsAisFlagged(response.portal.ais_discrepancy);
                }
            } catch (error) {
                console.log("Error fetching AIS flag status:", error);
                if (flaggedAISArtifact.portal) {
                    setIsAisFlagged(flaggedAISArtifact.portal.ais_discrepancy);
                }
            }
        };

        fetchAISFlaggedStatus();
    }, [flaggedAISArtifact]);

    useEffect(() => {
        const socket = getSocket();
        const handleListChanged = () => {
            const targetListId = list?.id || selectedSaveList?.id;
            if (cardId && targetListId && targetListId !== "favorites") {
                listsController
                    .isArtifactInList(targetListId, cardId)
                    .then((isInList) => {
                        setIsFavourite(isInList);
                    })
                    .catch((error) => {
                        console.error("Error checking artifact status after list change", error);
                    });
            }
        };

        socket.on("list/changed", handleListChanged);

        return () => {
            socket.off("list/changed", handleListChanged);
        };
    }, [cardId, selectedSaveList, list?.id]);

    // useEffect(() => {
    //     const mediaElement = mediaRef.current;

    //     if (!mediaElement) return;

    //     return () => {
    //         if (isUnified) return; // do not cancel if in unified view
    //         if (isImage) {
    //             console.log("Cancel image");
    //             // mediaElement.src = ""; // Or a 1x1 transparent GIF: "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"
    //         } else {
    //             console.log("Cancel video");
    //             mediaElement.pause();
    //             mediaElement.removeAttribute("src");
    //             // mediaElement.src = ''; // Alternative
    //             mediaElement.load();
    //         }
    //     };
    // }, [cardId]);

    const toggleShare = (e) => {
        e.stopPropagation();
        setIsShareModalOpen((prev) => !prev);
    };

    const handleCurrentTimeChange = (time = 0) => {
        setCurrentTime(time);
    };

    const handleFullscreenOpen = (e) => {
        e.stopPropagation();
        setFullscreenOpen(true);
    };

    const handleFullscreenClose = () => {
        setFullscreenOpen(false);
    };

    const handleThumbnailClick = (e) => {
        e.stopPropagation();
        if (onThumbnailClick) {
            onThumbnailClick(e);
        } else {
            handleFullscreenOpen(e);
        }
    };

    const downloadArtifact = async () => {
        setIsDownloading(true);
        await axiosInstance
            .post(
                `artifacts/${cardId}/download`,
                {},
                {
                    responseType: "blob",
                    timeout: 120000,
                },
            )
            .then(procDownloadResponse)
            .catch((e) => {
                console.error(e);
            })
            .finally(() => {
                setIsDownloading(false);
            });
    };

    const addFavourite = async () => {
        if (!cardId && !user._id) return;
        setIsFavouriteLoading(true);

        try {
            const targetListId = list?.id || selectedSaveList?.id;

            if (targetListId === "favorites") {
                const data = {
                    artifact_id: cardId,
                };
                const response = await favouriteArtifactsController.addFavouriteArtifact(data);
                if (response.status === 201) {
                    setIsFavourite(true);
                }
            } else if (targetListId) {
                try {
                    await listsController.addArtifact({ listId: targetListId, artifactId: cardId });
                    setIsFavourite(true);
                    window.dispatchEvent(
                        new CustomEvent("listArtifactCountUpdate", {
                            detail: { listId: targetListId, operation: "add" },
                        })
                    );
                } catch (error) {
                    if (error?.status === 404 || error?.response?.status === 404) {
                        console.warn("Selected list no longer exists, resetting to favorites");
                        window.dispatchEvent(
                            new CustomEvent("resetSelectedSaveList", {
                                detail: { reason: "list_not_found" },
                            })
                        );
                    }
                    throw error;
                }
            }
        } catch (error) {
            console.error("Error adding to list/favorites", error);
        } finally {
            setIsFavouriteLoading(false);
        }
    };

    const removeFavourite = async () => {
        if (!cardId && !user._id) return;
        setIsFavouriteLoading(true);

        try {
            const targetListId = list?.id || selectedSaveList?.id;

            if (targetListId === "favorites") {
                const data = {
                    artifact_id: cardId,
                };
                const response = await favouriteArtifactsController.removeFavouriteArtifact(data);
                if (response.status === 200) {
                    setIsFavourite(false);
                }
            } else if (targetListId) {
                if (list?.id) {
                    await listsController.removeArtifact({ listId: targetListId, artifactId: cardId });
                    setIsFavourite(false);
                    window.dispatchEvent(
                        new CustomEvent("listArtifactCountUpdate", {
                            detail: { listId: targetListId, artifactId: cardId, operation: "remove" },
                        })
                    );
                } else {
                    const listsContaining = await listsController.getListsContainingArtifact(cardId);
                    const isInFavorites = listsContaining.includes("favorites");
                    const filteredLists = listsContaining.filter((listId) => listId !== "favorites");

                    // Show modal if there are multiple lists (including favorites) or if favorites + at least one list
                    if (filteredLists.length > 1 || (isInFavorites && filteredLists.length > 0)) {
                        setListsContainingArtifact(filteredLists);
                        setIsArtifactInFavorites(isInFavorites);
                        setShowRemoveFromListsModal(true);
                        setIsFavouriteLoading(false);
                    } else if (filteredLists.length === 1) {
                        await listsController.removeArtifact({ listId: filteredLists[0], artifactId: cardId });
                        setIsFavourite(false);
                        window.dispatchEvent(
                            new CustomEvent("listArtifactCountUpdate", {
                                detail: { listId: filteredLists[0], operation: "remove" },
                            })
                        );
                    } else {
                        await listsController.removeArtifact({ listId: targetListId, artifactId: cardId });
                        setIsFavourite(false);
                        window.dispatchEvent(
                            new CustomEvent("listArtifactCountUpdate", {
                                detail: { listId: targetListId, operation: "remove" },
                            })
                        );
                    }
                }
            }
        } catch (error) {
            console.error("Error removing from list/favorites", error);
        } finally {
            setIsFavouriteLoading(false);
        }
    };

    const handleRemoveFromSelectedLists = async (listIds) => {
        setIsFavouriteLoading(true);
        try {
            const promises = [];
            const favoritePromises = [];

            listIds.forEach((listId) => {
                if (listId === "favorites") {
                    favoritePromises.push(
                        favouriteArtifactsController.removeFavouriteArtifact({ artifact_id: cardId })
                    );
                } else {
                    promises.push(
                        listsController.removeArtifact({ listId, artifactId: cardId })
                    );
                }
            });

            await Promise.all([...promises, ...favoritePromises]);
            setIsFavourite(false);
            setShowRemoveFromListsModal(false);
            setListsContainingArtifact([]);

            listIds.forEach((listId) => {
                if (listId !== "favorites") {
                    window.dispatchEvent(
                        new CustomEvent("listArtifactCountUpdate", {
                            detail: { listId, operation: "remove" },
                        })
                    );
                }
            });
        } catch (error) {
            console.error("Error removing artifact from selected lists", error);
        } finally {
            setIsFavouriteLoading(false);
        }
    };

    const handleArchiveClick = (e) => {
        e.stopPropagation();
        if (archived) {
            setArchiveAction("unarchive");
            setConfirmOpen(true);
        } else {
            handleConfirm(e, true);
        }
    };

    const handleConfirm = async (e, skipConfirmClose = false) => {
        e.stopPropagation();
        if (!skipConfirmClose) setConfirmOpen(false);
        setIsArchiving(true);

        try {
            const isArchiving = archiveAction === "archive" || skipConfirmClose;

            if (canArchiveAllArtifacts && isGrouped && groupArtifacts?.length > 0) {
                // Archive/unarchive all artifacts in the group
                const promises = groupArtifacts.map((artifact) => {
                    const endpoint = isArchiving ? "archive" : "unarchive";
                    return axiosInstance.post(`/artifacts/${artifact._id}/${endpoint}`);
                });

                await Promise.all(promises);

                // Clean up indexedDB for all artifacts
                if (vesselId) {
                    const cleanupPromises = groupArtifacts.map((artifact) =>
                        idb.deleteItem(vesselId + "_artifact", artifact._id).catch((err) => {
                            console.error("Error deleting artifact from indexedDB", err);
                        }),
                    );
                    await Promise.all(cleanupPromises);
                }
            } else if (isUnified && unifiedArtifacts?.length > 0) {
                // Archive/unarchive all artifacts in the unified group (base + duplications)
                const promises = unifiedArtifacts.map((artifact) => {
                    const endpoint = isArchiving ? "archive" : "unarchive";
                    return axiosInstance.post(`/artifacts/${artifact._id}/${endpoint}`);
                });
                await Promise.all(promises);
            } else {
                // Single artifact archiving (original logic)
                if (isArchiving) {
                    await axiosInstance.post(`/artifacts/${cardId}/archive`);
                } else {
                    await axiosInstance.post(`/artifacts/${cardId}/unarchive`);
                }

                if (vesselId) {
                    await idb.deleteItem(vesselId + "_artifact", cardId).catch((err) => {
                        console.error("Error deleting artifact from indexedDB", err);
                    });
                }
            }
        } catch (err) {
            console.error("Error archiving artifact(s):", err);
        } finally {
            setIsArchiving(false);
        }
    };

    const handleFlagClick = async (e) => {
        e.stopPropagation();
        if (handleUnflagClick) {
            handleUnflagClick(e);
            return;
        }
        setIsFlagging(true);
        try {
            if (isFlagged) {
                await artifactFlagController.unflagArtifact(cardId);
                setIsFlagged(false);
                return;
            }
            await artifactFlagController.flagArtifact(cardId);
            setIsFlagged(true);
        } catch (error) {
            console.error("Error flagging artifact:", error);
        } finally {
            setIsFlagging(false);
        }
    };


    const handleReportAisMismatch = (e) => {
        e.stopPropagation();
        setIsAISModalOpen(true);
    };

    useEffect(() => {
        if (!isImage && showVideoModal) {
            setIsLoading(false);
        }
    }, [showVideoModal]);

    useEffect(() => {
        const loadFlaggedArtifacts = async () => {
            if (cardId) {
                try {
                    const flagged = artifactFlagController.isArtifactFlaggedByUser(cardId);
                    setIsFlagged(flagged);
                } catch (error) {
                    console.error("Error loading flagged artifacts:", error);
                }
            }
        };

        loadFlaggedArtifacts();
    }, [cardId, user, artifactFlagController.version, flaggedArtifact]);
    const [panelOpen, setPanelOpen] = useState(false);
    const [anchorEl, setAnchorEl] = React.useState(null);

    const handleImageClick = (event) => {
        const image = mediaRef.current;
        const rect = image.getBoundingClientRect();
        const x = event.clientX - rect.left; // Click X position relative to image
        const y = event.clientY - rect.top; // Click Y position relative to image
        const width = rect.width;
        const height = rect.height;
        let area = "";
        if (y < height / 2) {
            // Top half
            area = x < width / 2 ? "Top Left" : "Top Right";
        } else {
            // Bottom half
            area = x < width / 2 ? "Bottom Left" : "Bottom Right";
        }

        // setPosition(area);
        console.log("Quadrant Clicked Area:", area);
    };
    const [quadrant, setQuadrant] = useState()
    const firstDotRef = React.useRef(null);

    // Auto-open the first artifact's details when the view loads
    useEffect(() => {
        if (isAISDot && totalUnifiedAISDots.length > 0 && firstDotRef.current && !panelOpen && isShowAISDot) {
            // Small delay to ensure the DOM is fully rendered
            setTimeout(() => {
                setAnchorEl(firstDotRef.current);
                setPanelOpen(true);

                // Calculate quadrant for the first dot
                const container = containerRef.current;
                const firstDot = totalUnifiedAISDots[0];
                if (container && firstDot?.det_nbbox) {
                    const containerRect = container.getBoundingClientRect();
                    const bbox = firstDot.det_nbbox;

                    // Calculate the center position of the bounding box
                    const centerX = (bbox.x + bbox.width / 2) * containerRect.width;
                    const centerY = (bbox.y + bbox.height / 2) * containerRect.height;

                    // Determine quadrant
                    const isTop = centerY < containerRect.height / 2;
                    const isLeft = centerX < containerRect.width / 2;

                    if (isTop && isLeft) setQuadrant("TL");
                    else if (isTop && !isLeft) setQuadrant("TR");
                    else if (!isTop && isLeft) setQuadrant("BL");
                    else setQuadrant("BR");
                }
            }, 100);
        }
        return ()=> {
            setAnchorEl(null);
            setPanelOpen(false);
            
        }
    }, [isAISDot, totalUnifiedAISDots.length, isLoading, isShowAISDot, isImageLoading]);

    const { p1Placement, p2Placement, p1Offset, p2Offset } = React.useMemo(() => {
        const gapSide = 40;      // distance from the dot
        const gapBetween = 16;   // extra distance vs the first tooltip
        const crossSkid = -120;   // how far to slide sideways so they don't overlap

        switch (quadrant) {
            case "TL":  // dot on the left → slide p2 to the right
                return {
                    p1Placement: "right",
                    p2Placement: "bottom",
                    p1Offset: [0, gapSide],
                    p2Offset: [crossSkid, gapSide + gapBetween],
                };

            case "TR":  // dot on the right → slide p2 to the left
                return {
                    p1Placement: "left",
                    p2Placement: "bottom",
                    p1Offset: [0, gapSide],
                    p2Offset: [-crossSkid, gapSide + gapBetween],
                };

            case "BL":
                return {
                    p1Placement: "right",
                    p2Placement: "top",
                    p1Offset: [0, gapSide],
                    p2Offset: [crossSkid, gapSide + gapBetween],
                };

            case "BR":
                return {
                    p1Placement: "left",
                    p2Placement: "top",
                    p1Offset: [0, gapSide],
                    p2Offset: [-crossSkid, gapSide + gapBetween],
                };

            default:
                return {
                    p1Placement: "right",
                    p2Placement: "bottom",
                    p1Offset: [0, gapSide],
                    p2Offset: [crossSkid, gapSide + gapBetween],
                };
        }
    }, [quadrant]);


    return (
        <Box
            position="relative"
            sx={{
                width: "100%",
                height: "100%",
            }}
            ref={containerRef}
        >
            {isLoading && isImage && (
                <>
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            position: "absolute",
                            top: 0,
                            left: 0,
                            width: "100%",
                            height: "100%",
                            zIndex: 1,
                        }}
                    >
                        <CircularProgress />
                    </Box>
                    <Skeleton variant="rectangular" width="100%" sx={{ ...skeletonStyle }} />
                </>
            )}

            {isImage ? (
                <>
                    <img
                        src={thumbnailLink}
                        ref={mediaRef}
                        alt="media"
                        style={{ width: "100%", height: "100%", objectFit: "cover", display: isLoading ? "none" : "block", ...style }}
                        onClick={(e) => {
                            handleImageClick(e);
                        }}
                        onLoad={(e) => {
                            setIsLoading(false);
                            try {
                                const container = containerRef.current;
                                if (!container) return;
                                const rect = computeDisplayedImageRect({
                                    containerWidth: container.clientWidth,
                                    containerHeight: container.clientHeight,
                                    naturalWidth: e.target.naturalWidth,
                                    naturalHeight: e.target.naturalHeight,
                                    fitMode: (style && style.objectFit) || "cover",
                                });
                                setImageDisplayRect(rect);
                                // keep rect updated on resize
                                const handleResize = () => {
                                    const updated = computeDisplayedImageRect({
                                        containerWidth: container.clientWidth,
                                        containerHeight: container.clientHeight,
                                        naturalWidth: e.target.naturalWidth,
                                        naturalHeight: e.target.naturalHeight,
                                        fitMode: (style && style.objectFit) || "cover",
                                    });
                                    setImageDisplayRect(updated);
                                };
                                window.addEventListener("resize", handleResize);
                                // store for cleanup on unmount/change
                                e.target.__qmwHandleResize = handleResize;
                            } catch (_) { }
                        }}
                        onError={() => setIsLoading(false)}
                    />
                    {/* This is for the AIS dot overlays */}
                    {isImage && (
                        <>
                            {
                                !isLoading && !isImageLoading && isAISDot && totalUnifiedAISDots.length > 0 && totalUnifiedAISDots.map((dot, index) => {
                                    if (!dot || !dot.det_nbbox) return null;
                                    return (
                                        <EventPingDot
                                            key={index}
                                            ref={index === 0 ? firstDotRef : null}
                                            det_nbbox={dot.det_nbbox}
                                            setAnchorEl={setAnchorEl}
                                            panelOpen={panelOpen}
                                            setPanelOpen={setPanelOpen}
                                            imageDisplayRect={imageDisplayRect}
                                            setQuadrant={setQuadrant}
                                            containerRef={containerRef}
                                            artifactIndex={index}
                                            onDotClick={onUnifiedDotClick}
                                        />
                                    )
                                }
                                )
                            }
                            <Box>
                                {
                                    !isLoading && !isImageLoading && <EventPopperDetail
                                        open={panelOpen && Boolean(anchorEl)}
                                        anchorEl={anchorEl}
                                        containerRef={containerRef}
                                        setPanelOpen={setPanelOpen}

                                        placement={p1Placement}
                                        offset={p1Offset}
                                        details={artifactData}
                                        fieldWidth={fieldWithFullWidth}
                                        title={""}
                                    />
                                }
                                {
                                    !isLoading && !isImageLoading && aisData && aisData.length > 0 &&
                                    <EventPopperDetail
                                        open={panelOpen && Boolean(anchorEl)}
                                        anchorEl={anchorEl}
                                        containerRef={containerRef}
                                        setPanelOpen={setPanelOpen}

                                        placement={p2Placement}
                                        offset={p2Offset}
                                        details={aisData}
                                        fieldWidth={AISFieldWithFullWidth}
                                        title={"AIS "}
                                    />

                                }
                            </Box>
                        </>
                    )}
                    {!isLoading && isBounding && det_nbbox && !isAISDot && (
                        <Box
                            sx={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                width: "100%",
                                height: "100%",
                                pointerEvents: "none",
                                // zIndex: 2,
                            }}
                        >
                            {(() => {
                                const bbox = getClampedBbox({ containerRef, imageDisplayRect, det_nbbox });
                                return (
                                    <Box
                                        sx={{
                                            position: "absolute",
                                            border: "2px solid #00ff00",
                                            boxShadow: "0 0 0 1px rgba(0,0,0,0.4) inset",
                                            borderRadius: "2px",
                                            left: `${bbox.left}px`,
                                            top: `${bbox.top}px`,
                                            width: `${bbox.width}px`,
                                            height: `${bbox.height}px`,
                                        }}
                                    />
                                );
                            })()}
                        </Box>
                    )}
                    {!isLoading && showFullscreenIcon && !showFullscreenIconForMap && (
                        <Tooltip
                            {...(containerRef ? { slotProps: { popper: { container: containerRef.current } } } : {})}
                            title="View Full Screen"
                            arrow
                            placement="right"
                        >
                            <IconButton
                                onClick={handleFullscreenOpen}
                                sx={{
                                    position: "absolute",
                                    bottom: 8,
                                    right: 8,
                                    height: 27,
                                    width: 27,
                                    padding: 0,
                                    color: "#fff",
                                    backgroundColor: "rgba(0, 0, 0, 0.5)",
                                    borderRadius: "50%",
                                    "&:hover": {
                                        backgroundColor: "rgba(0, 0, 0, 0.7)",
                                    },
                                }}
                            >
                                <FullscreenIcon sx={{ height: 18 }} />
                            </IconButton>
                        </Tooltip>
                    )}
                </>
            ) : showVideoModal ? (
                <>
                    <VideoPlayer ref={mediaRef} src={originalLink} onFullscreen={() => setIsModalOpen(true)} />
                    <Modal open={isModalOpen} onClose={() => setIsModalOpen(false)}>
                        <Box sx={{ width: "100%", height: "100%", backgroundColor: "#000" }}>
                            <VideoPlayer src={originalLink} isFullscreen onFullscreen={() => setIsModalOpen(false)} />
                        </Box>
                    </Modal>
                </>
            ) : showVideoThumbnail ? (
                <>
                    <img
                        src={thumbnailLink}
                        alt="media"
                        style={{ width: "100%", height: "100%", objectFit: "cover", display: isLoading ? "none" : "block", ...style }}
                        onLoad={() => setIsLoading(false)}
                        onClick={handleThumbnailClick}
                    />
                    {!isLoading && (
                        <Box
                            sx={{
                                position: "absolute",
                                top: 0,
                                left: 0,
                                width: "100%",
                                height: "100%",
                                display: "flex",
                                alignItems: "center",
                                justifyContent: "center",
                                backgroundColor: "rgba(0, 0, 0, 0.3)",
                                borderRadius: "8px",
                                cursor: "pointer",
                            }}
                            onClick={handleThumbnailClick}
                        >
                            <PlayCircleOutlineIcon
                                sx={{
                                    fontSize: 50,
                                    color: "white",
                                    filter: "drop-shadow(0px 2px 3px rgba(0,0,0,0.5))",
                                }}
                            />
                        </Box>
                    )}
                </>
            ) : (
                <>
                    <DetailVideoPlayer
                        src={originalLink}
                        style={{ width: "100%", height: "100%", objectFit: "cover", ...style }}
                        onLoadedData={() => setIsLoading(false)}
                        onCurrentTimeChange={handleCurrentTimeChange}
                        currentTime={currentTime}
                        fullscreenOpen={fullscreenOpen}
                        isInFullScreen={false}
                        showFullscreenIcon={true}
                        setFullscreenOpen={setFullscreenOpen}
                    />
                </>
            )}

            {!isLoading && !isArchived && showFlagButton && handleFlagClick && (
                <Tooltip
                    {...(containerRef ? { slotProps: { popper: { container: containerRef.current } } } : {})}
                    title={!isFlagging ? (isFlagged || handleUnflagClick ? "Remove flag" : "Flag for cleanup and/or review") : null}
                    arrow
                    placement="right"
                >
                    <IconButton
                        size="small"
                        sx={{
                            position: "absolute",
                            top: 8,
                            left: 8,
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: isFlagged || handleUnflagClick ? "#E60000CC" : "rgba(0,0,0,0.5)",
                            borderRadius: "50%",
                            "&:hover": {
                                backgroundColor: "rgba(0,0,0,0.7)",
                            },
                        }}
                        onClick={handleFlagClick}
                        disabled={isFlagging}
                    >
                        {isFlagging ? (
                            <CircularProgress sx={{ color: "white" }} size={18} />
                        ) : isFlagged || handleUnflagClick ? (
                            <FlagIcon sx={{ height: 18 }} />
                        ) : (
                            <OutlinedFlagIcon sx={{ height: 18 }} />
                        )}
                    </IconButton>
                </Tooltip>
            )}

            {!isLoading && showAISFlagButton && handleReportAisMismatch && (
                <Tooltip
                    {...(containerRef ? { slotProps: { popper: { container: containerRef.current } } } : {})}
                    title={isAisFlagged ? "Review AIS evaluation" : "Evaluate AIS match"}
                    arrow
                    placement="right"
                >
                    <IconButton
                        size="small"
                        sx={{
                            position: "absolute",
                            top: 40,
                            left: 8,
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: isAisFlagged ? "#0066FF" : "rgba(0,0,0,0.5)",
                            borderRadius: "50%",
                            "&:hover": {
                                backgroundColor: "rgba(0,0,0,0.7)",
                            },
                        }}
                        onClick={handleReportAisMismatch}
                    >
                        {isAisFlagged ? (
                            <WarningIcon sx={{ height: 18 }} />
                        ) : (
                            <WarningAmberIcon sx={{ height: 18 }} />
                        )}
                    </IconButton>
                </Tooltip>
            )}

            {!isLoading && (
                <HelperIcons
                    buttonsToShow={
                        buttonsToShow || [
                            helperIconModes.FAVOURITE,
                            helperIconModes.SHARE,
                            helperIconModes.DOWNLOAD,
                            ...(showArchiveButton ? [canArchiveAllArtifacts && (isGrouped || isUnified) ? helperIconModes.GROUP_ARCHIVE : helperIconModes.ARCHIVE] : []),
                            ...(showFullscreenIconForMap ? [helperIconModes.FULLSCREEN] : []),
                        ]
                    }
                    buttonHandlers={{
                        addFavourite,
                        removeFavourite,
                        toggleShare,
                        downloadArtifact,
                        handleFullscreenOpen,
                        handleArchiveClick,
                    }}
                    buttonStates={{
                        isFavourite,
                        isFavouriteLoading,
                        isDownloading,
                        showFullscreenIconForMap,
                        archived,
                        isArchiving,
                        isUnified,
                    }}
                    direction={direction}
                    containerRef={containerRef}
                    helperIconsPosition={helperIconsPosition}
                    showUnifiedNavigation={showUnifiedNavigation}
                    unifiedNavigation={unifiedNavigation}
                    list={list}
                />
            )}
            <ShareModal
                state={isShareModalOpen}
                onClose={toggleShare}
                shareLink={originalLink || thumbnailLink}
                isImageLink={isImage}
                shareEventLink={cardId ? `${environment.VITE_API_URL}/dashboard/events/${cardId}` : ""}
            />
            <ArchiveConfirmModal
                initialState={confirmOpen}
                onClose={(e) => {
                    e.stopPropagation();
                    setConfirmOpen(false);
                }}
                onConfirm={(e) => handleConfirm(e)}
                isArchive={archiveAction === "archive"}
            />

            {fullscreenOpen && (
                <FullscreenMediaModal
                    isLoading={isLoading}
                    isFavourite={isFavourite}
                    removeFavourite={removeFavourite}
                    addFavourite={addFavourite}
                    toggleShare={toggleShare}
                    downloadArtifact={downloadArtifact}
                    open={fullscreenOpen}
                    onClose={handleFullscreenClose}
                    mediaUrl={originalLink}
                    isImage={isImage}
                    handleCurrentTimeChange={handleCurrentTimeChange}
                    currentTime={currentTime}
                    showFullscreenIcon={true}
                    setFullscreenOpen={setFullscreenOpen}
                />
            )}

            {/* AIS Evaluation Modal */}
            {showAISFlagButton && flaggedAISArtifact?._id && (
                <AISEvaluationModal
                    initialState={isAISModalOpen}
                    onClose={() => setIsAISModalOpen(false)}
                    isAisFlagged={isAisFlagged}
                    setIsAisFlagged={setIsAisFlagged}
                    flagAISArtifactId={flaggedAISArtifact?._id}
                />
            )}
            <RemoveFromListsModal
                open={showRemoveFromListsModal}
                onClose={() => {
                    setShowRemoveFromListsModal(false);
                    setListsContainingArtifact([]);
                    setIsArtifactInFavorites(false);
                }}
                artifactId={cardId}
                listIds={listsContainingArtifact}
                isInFavorites={isArtifactInFavorites}
                onConfirm={handleRemoveFromSelectedLists}
            />
        </Box>
    );
};

export default PreviewMedia;
