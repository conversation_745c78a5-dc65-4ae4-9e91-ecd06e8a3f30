
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/middlewares/auth.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/middlewares</a> auth.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">69.38% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>34/49</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.57% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>6/19</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">86.84% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>33/38</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-yes">14x</span>
<span class="cline-any cline-yes">14x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">8x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import jwt, { JwtPayload } from "jsonwebtoken";
import ApiKey from "../models/ApiKey";
import { NextFunction, Request, Response } from "express";
import { getUser } from "../queries/User";
&nbsp;
const isAuthenticated = async (req: Request, res: Response, next: NextFunction) =&gt; {
    const authHeader = req.header("authorization");
    if (!authHeader) return res.status(401).json({ message: "Unauthorized" });
    <span class="missing-if-branch" title="if path not taken" >I</span>if (!authHeader.startsWith("Bearer ")) <span class="cstat-no" title="statement not covered" >return res.status(401).json({ message: "Token type must be Bearer" });</span>
    const token = authHeader.split("Bearer ")[1];
    <span class="missing-if-branch" title="if path not taken" >I</span>if (!token) <span class="cstat-no" title="statement not covered" >return res.status(401).json({ message: "Token is invalid" });</span>
&nbsp;
    try {
        const { user_id, api_key_id } = jwt.verify(token, process.env.JWT_SECRET as string) as JwtPayload;
        if (user_id) {
            const user = await getUser({ user_id, includeUnprojected: true });
            <span class="missing-if-branch" title="if path not taken" >I</span>if (user.is_deleted) <span class="cstat-no" title="statement not covered" >return res.status(401).json({ message: "Your account has been deleted." });</span>
            <span class="missing-if-branch" title="if path not taken" >I</span>if (!user.jwt_tokens.includes(token)) <span class="cstat-no" title="statement not covered" >return res.status(401).json({ message: "Token is not available." });</span>
            req.user = user;
&nbsp;
            console.log(
                `[${req.method}: ${req.originalUrl}] user: ${JSON.stringify({ _id: user._id, name: user.name, email: user.email })}, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `,
            );
            next();
        } else if (api_key_id) {
            const apiKey = await ApiKey.findOne({ _id: api_key_id });
            <span class="missing-if-branch" title="if path not taken" >I</span>if (!apiKey) <span class="cstat-no" title="statement not covered" >return res.status(401).json({ message: "API key is invalid" });</span>
            <span class="missing-if-branch" title="if path not taken" >I</span>if (apiKey.is_deleted) <span class="cstat-no" title="statement not covered" >return res.status(400).json({ message: "Your API key has been removed. Please contact an administrator" });</span>
            <span class="missing-if-branch" title="if path not taken" >I</span>if (apiKey.is_revoked) <span class="cstat-no" title="statement not covered" >return res.status(400).json({ message: "Your access has been revoked. Please contact an administrator" });</span>
&nbsp;
            <span class="missing-if-branch" title="if path not taken" >I</span>if (!apiKey.allowed_endpoints.includes(req._endpoint_id)) <span class="cstat-no" title="statement not covered" >return res.status(403).json({ message: "You cannot access this resource" });</span>
&nbsp;
            apiKey.requests += 1;
            apiKey.requests_endpoints[req._endpoint_id] = (apiKey.requests_endpoints[req._endpoint_id] || 0) + 1;
            apiKey.last_used = new Date();
            apiKey.markModified("requests_endpoints");
&nbsp;
            await apiKey.save();
&nbsp;
            req.api_key = apiKey.toObject();
&nbsp;
            console.log(
                `[${req.method}: ${req.originalUrl}] apiKey: ${apiKey._id}, body: ${JSON.stringify(req.body)}, query: ${JSON.stringify(req.query)}, params: ${JSON.stringify(req.params)} `,
            );
            next();
        } else <span class="cstat-no" title="statement not covered" ><span class="missing-if-branch" title="else path not taken" >E</span>throw new Error("JWT token returned unexpected data");</span>
    } catch (err) {
<span class="cstat-no" title="statement not covered" >        console.error(err);</span>
<span class="cstat-no" title="statement not covered" >        if (err instanceof jwt.JsonWebTokenError) <span class="cstat-no" title="statement not covered" >res.status(401).json({ message: "Token is invalid" });</span></span>
        else <span class="cstat-no" title="statement not covered" >if (err instanceof Error) <span class="cstat-no" title="statement not covered" >res.status(500).json({ message: `Unexpected error occured: ${err.message}` });</span></span>
        else <span class="cstat-no" title="statement not covered" >res.status(500).json({ message: `Unexpected error occured: ${err}` });</span>
    }
};
&nbsp;
export default isAuthenticated;
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2026-01-11T11:02:57.155Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    