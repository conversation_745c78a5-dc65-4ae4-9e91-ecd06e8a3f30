import { Grid, Typography, Tooltip } from "@mui/material";
import { memo, useMemo, useEffect, useState, useCallback } from "react";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";

const UnifiedGroupCard = ({ card, setShowDetailModal, setSelectedCard, buttonsToShow }) => {
    const { user } = useUser();
    const { vesselInfo } = useVesselInfo();

    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    // Memoized values
    const currentArtifact = card;

    const isVideo = Boolean(currentArtifact?.video_path);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    const vessel = useMemo(
        () => vesselInfo.find((v) => v.vessel_id === currentArtifact?.onboard_vessel_id),
        [vesselInfo, currentArtifact?.onboard_vessel_id],
    );

    const vesselName = vessel?.name;

    const roundedCoordinates = useMemo(
        () => displayCoordinates(currentArtifact?.location?.coordinates, !!user?.use_MGRS),
        [currentArtifact?.location?.coordinates, user?.use_MGRS],
    );

    const handleClick = useCallback(() => {
        if (!card || !currentArtifact) return;
        setShowDetailModal(true);
        setSelectedCard({
            ...currentArtifact,
            vesselName,
            isGroup: card?.isGroup,
            groupArtifacts: card?.groupArtifacts,
            currentGroupIndex: 0,
        });
    }, [setShowDetailModal, setSelectedCard, currentArtifact, vesselName, card?.isGroup, card?.groupArtifacts]);

    useEffect(() => {
        if (!card) return;
        const thumbnailUrl = card?.thumbnail_url;
        const imageUrl = card?.image_url;
        const videoUrl = card?.video_url;

        if (isVideo) {
            setThumbnail(thumbnailUrl || imageUrl || null);
            setSrc(videoUrl || null);
        } else {
            setThumbnail(thumbnailUrl || imageUrl || null);
            setSrc(imageUrl || null);
        }
    }, [card, isVideo]);

    if (!vesselInfo) return <Typography>No vessel info</Typography>;
    if (!currentArtifact) return null;

    return (
        <Grid
            container
            paddingTop={"0 !important"}
            height={"100%"}
            maxHeight={"350px"}
            className={"events-step-2"}
            onClick={handleClick}
            sx={{ cursor: "pointer" }}
        >
            <Grid container backgroundColor={"primary.main"} borderRadius={2} padding={1} gap={1}>
                <Grid size={12} height={"200px"}>
                    <PreviewMedia
                        thumbnailLink={thumbnail}
                        originalLink={src}
                        cardId={currentArtifact._id}
                        isImage={!isVideo}
                        style={{ borderRadius: 8 }}
                        showVideoThumbnail={isVideo}
                        onThumbnailClick={handleClick}
                        showArchiveButton={hasManageArtifacts}
                        isArchived={currentArtifact?.portal?.is_archived || false}
                        vesselId={currentArtifact?.onboard_vessel_id}
                        buttonsToShow={buttonsToShow}
                        isGrouped={card.isGroup}
                        groupArtifacts={card.groupArtifacts}
                        isUnified={true}
                        unifiedArtifacts={card.duplications ? [currentArtifact, ...card.duplications] : [currentArtifact]}
                    />
                </Grid>
                <Grid container size={12}>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Tooltip title={vesselName?.length > 12 ? vesselName : ""}>
                            <Typography fontSize="14px" fontWeight={500}>
                                {vesselName?.length > 12 ? vesselName.slice(0, 12) + "..." : vesselName || "Unknown"}
                            </Typography>
                        </Tooltip>
                        <Typography fontSize="14px" fontWeight={500}>
                            {dayjs(currentArtifact.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Typography fontSize="14px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Location
                        </Typography>
                        <Typography fontSize="14px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                            Category
                        </Typography>
                    </Grid>
                    <Grid display="flex" justifyContent="space-between" alignItems="center" paddingX={1} size={12}>
                        <Typography fontSize="14px" fontWeight={500} maxWidth="50%">
                            {roundedCoordinates}
                        </Typography>
                        <Typography fontSize="14px" fontWeight={500} maxWidth="50%" textAlign="right">
                            Multiple
                        </Typography>
                    </Grid>
                </Grid>
            </Grid>
        </Grid>
    );
};

export default memo(UnifiedGroupCard);
