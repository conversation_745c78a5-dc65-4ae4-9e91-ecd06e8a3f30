import mongoose from "mongoose";
import db from "../modules/db";

export interface IListACL extends mongoose.Document {
    list_id: mongoose.Types.ObjectId | string;
    user_id: mongoose.Types.ObjectId | string;
    created_at: Date;
    updated_at: Date;
}

const listAclSchema = new mongoose.Schema<IListACL>({
    list_id: { type: mongoose.Schema.Types.ObjectId, ref: "List", required: true, index: true },
    user_id: { type: mongoose.Schema.Types.ObjectId, ref: "User", required: true, index: true },
    created_at: { type: Date, required: true, default: () => new Date() },
    updated_at: { type: Date, required: true, default: () => new Date() },
});

// Ensure one entry per (list, user)
listAclSchema.index({ list_id: 1, user_id: 1 }, { unique: true });

const ListACL = db.qm.model<IListACL>("list_acl", listAclSchema);

export default ListACL;
