import { jest } from '@jest/globals';

type AnyObject = Record<string, any>;

function MockListArtifact(this: AnyObject, data: AnyObject) {
    Object.assign(this, data);
}

const createChainableMock = (mockResolvedValue: any) => {
    const chainable = {
        lean: jest.fn().mockResolvedValue(mockResolvedValue as never),
        select: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
    };
    chainable.select.mockReturnValue(chainable);
    chainable.sort.mockReturnValue(chainable);
    return chainable;
};

(MockListArtifact as AnyObject).find = jest.fn().mockReturnValue(createChainableMock([]));
(MockListArtifact as AnyObject).findOne = jest.fn().mockReturnValue(createChainableMock(null));
(MockListArtifact as AnyObject).updateOne = jest.fn();
(MockListArtifact as AnyObject).deleteOne = jest.fn();
(MockListArtifact as AnyObject).aggregate = jest.fn();
(MockListArtifact as AnyObject).countDocuments = jest.fn();
(MockListArtifact as AnyObject).bulkWrite = jest.fn().mockResolvedValue({} as never);

(MockListArtifact as AnyObject).prototype = {
    save: jest.fn().mockResolvedValue(true as never),
    toObject: jest.fn().mockReturnValue({}),
};

export default MockListArtifact as any;

