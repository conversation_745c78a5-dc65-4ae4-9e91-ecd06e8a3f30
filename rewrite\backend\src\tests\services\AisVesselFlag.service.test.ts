import { describe, it, beforeEach, expect, jest } from '@jest/globals';
import aisVesselFlagService from '../../services/AisVesselFlag.service';
import AisVesselFlag from '../../models/AisVesselFlag';
import db from '../../modules/db';

jest.mock('../../models/AisVesselFlag', () => require('../mocks/models/aisVesselFlag.mock'));
jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));

describe('AisVesselFlagService', () => {
    beforeEach(() => {
        jest.resetAllMocks();
    });

    describe('flagAisVessel', () => {
        it('should throw error when MMSI is empty', async () => {
            await expect(aisVesselFlagService.flagAisVessel('', 'u'.repeat(24))).rejects.toThrow('MMSI is required');
        });

        it('should throw error when MMSI is whitespace only', async () => {
            await expect(aisVesselFlagService.flagAisVessel('   ', 'u'.repeat(24))).rejects.toThrow('MMSI is required');
        });

        it('should throw error when already flagged', async () => {
            (AisVesselFlag.findOne as jest.Mock).mockResolvedValue({ _id: 'F', mmsi: '123456789' } as never);
            await expect(aisVesselFlagService.flagAisVessel('123456789', 'u'.repeat(24))).rejects.toThrow(
                'You have already flagged this AIS vessel'
            );
        });

        it('should create and save flag successfully', async () => {
            (AisVesselFlag.findOne as jest.Mock).mockResolvedValue(null as never);
            const result = await aisVesselFlagService.flagAisVessel('123456789', 'user123');
            expect(result).toBeDefined();
            expect(AisVesselFlag.findOne).toHaveBeenCalled();
        });

        it('should trim MMSI before processing', async () => {
            (AisVesselFlag.findOne as jest.Mock).mockResolvedValue(null as never);
            await aisVesselFlagService.flagAisVessel('  123456789  ', 'user123');
            expect(AisVesselFlag.findOne).toHaveBeenCalledWith(
                expect.objectContaining({
                    mmsi: '123456789',
                })
            );
        });
    });

    describe('getFlaggedAisVessels', () => {
        it('should return empty array when no flagged vessels', async () => {
            (AisVesselFlag.distinct as jest.Mock).mockResolvedValue([] as never);
            const result = await aisVesselFlagService.getFlaggedAisVessels(1, 10);
            expect(result).toEqual({ vessels: [], totalCount: 0, page: 1, pageSize: 10 });
        });

        it('should return paginated flagged vessels with AIS data', async () => {
            const mockFlaggedVessels = [
                {
                    _id: '123456789',
                    flags: [
                        {
                            _id: 'flag1',
                            flaggedBy: 'user1',
                            flaggedByUser: { _id: 'user1', name: 'User One', email: '<EMAIL>' },
                            flaggedAt: new Date('2023-01-01'),
                        },
                    ],
                    flagCount: 1,
                    latestFlagDate: new Date('2023-01-01'),
                },
            ];

            const mockAisData = {
                data: {
                    name: 'Test Vessel',
                    location: { type: 'Point', coordinates: [10.0, 20.0] },
                    timestamp: new Date('2023-01-01'),
                    metadata: { mmsi: '123456789' },
                    details: { message: { speed: 10 } },
                },
            };

            (AisVesselFlag.distinct as jest.Mock).mockResolvedValue(['123456789'] as never);
            (AisVesselFlag.aggregate as jest.Mock).mockResolvedValue(mockFlaggedVessels as never);

            const mockCollection = {
                findOne: jest.fn().mockResolvedValue(mockAisData as never),
            };
            (db.lookups.collection as jest.Mock).mockReturnValue(mockCollection);

            const result = await aisVesselFlagService.getFlaggedAisVessels(1, 10);
            expect(result.vessels.length).toBe(1);
            expect(result.vessels[0].mmsi).toBe('123456789');
            expect(result.vessels[0].aisData).toBeDefined();
            expect(result.vessels[0].flags[0].user).toEqual({ _id: 'user1', name: 'User One', email: '<EMAIL>' });
        });

        it('should handle missing AIS data gracefully', async () => {
            const mockFlaggedVessels = [
                {
                    _id: '123456789',
                    flags: [
                        {
                            _id: 'flag1',
                            flaggedBy: 'user1',
                            flaggedByUser: { _id: 'user1', name: 'User One', email: '<EMAIL>' },
                            flaggedAt: new Date('2023-01-01'),
                        },
                    ],
                    flagCount: 1,
                    latestFlagDate: new Date('2023-01-01'),
                },
            ];

            (AisVesselFlag.distinct as jest.Mock).mockResolvedValue(['123456789'] as never);
            (AisVesselFlag.aggregate as jest.Mock).mockResolvedValue(mockFlaggedVessels as never);

            const mockCollection = {
                findOne: jest.fn().mockResolvedValue(null as never),
            };
            (db.lookups.collection as jest.Mock).mockReturnValue(mockCollection);

            const result = await aisVesselFlagService.getFlaggedAisVessels(1, 10);
            expect(result.vessels.length).toBe(1);
            expect(result.vessels[0].aisData).toBeUndefined();
        });

        it('should handle errors when fetching AIS data', async () => {
            const mockFlaggedVessels = [
                {
                    _id: '123456789',
                    flags: [],
                    flagCount: 1,
                    latestFlagDate: new Date('2023-01-01'),
                },
            ];

            (AisVesselFlag.distinct as jest.Mock).mockResolvedValue(['123456789'] as never);
            (AisVesselFlag.aggregate as jest.Mock).mockResolvedValue(mockFlaggedVessels as never);

            const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
            const mockCollection = {
                findOne: jest.fn().mockRejectedValue(new Error('DB Error') as never),
            };
            (db.lookups.collection as jest.Mock).mockReturnValue(mockCollection);

            const result = await aisVesselFlagService.getFlaggedAisVessels(1, 10);
            expect(result.vessels.length).toBe(1);
            expect(consoleErrorSpy).toHaveBeenCalled();
            consoleErrorSpy.mockRestore();
        });

        it('should calculate pagination correctly', async () => {
            (AisVesselFlag.distinct as jest.Mock).mockResolvedValue(['123456789', '987654321'] as never);
            (AisVesselFlag.aggregate as jest.Mock).mockResolvedValue([] as never);

            const result = await aisVesselFlagService.getFlaggedAisVessels(2, 5);
            expect(result.page).toBe(2);
            expect(result.pageSize).toBe(5);
            expect(AisVesselFlag.aggregate).toHaveBeenCalledWith(
                expect.arrayContaining([
                    expect.objectContaining({ $skip: 5 }),
                    expect.objectContaining({ $limit: 5 }),
                ])
            );
        });
    });

    describe('unflagAisVessel', () => {
        it('should throw error when flag not found', async () => {
            (AisVesselFlag.findOneAndDelete as jest.Mock).mockResolvedValue(null as never);
            await expect(aisVesselFlagService.unflagAisVessel('123456789', 'user123')).rejects.toThrow('Flag not found');
        });

        it('should successfully unflag vessel', async () => {
            const mockFlag = {
                _id: 'flag123',
                mmsi: '123456789',
                flaggedBy: 'user123',
                flaggedAt: new Date(),
            };

            (AisVesselFlag.findOneAndDelete as jest.Mock).mockResolvedValue(mockFlag as never);
            const result = await aisVesselFlagService.unflagAisVessel('123456789', 'user123');
            expect(result).toEqual(mockFlag);
            expect(AisVesselFlag.findOneAndDelete).toHaveBeenCalledWith(
                expect.objectContaining({
                    mmsi: '123456789',
                })
            );
        });

        it('should trim MMSI before unflagging', async () => {
            const mockFlag = {
                _id: 'flag123',
                mmsi: '123456789',
                flaggedBy: 'user123',
                flaggedAt: new Date(),
            };

            (AisVesselFlag.findOneAndDelete as jest.Mock).mockResolvedValue(mockFlag as never);
            await aisVesselFlagService.unflagAisVessel('  123456789  ', 'user123');
            expect(AisVesselFlag.findOneAndDelete).toHaveBeenCalledWith(
                expect.objectContaining({
                    mmsi: '123456789',
                })
            );
        });
    });

    describe('getUserFlaggedAisVesselMmsis', () => {
        it('should return empty array when user has no flagged vessels', async () => {
            (AisVesselFlag.find as jest.Mock).mockResolvedValue([] as never);
            const result = await aisVesselFlagService.getUserFlaggedAisVesselMmsis('user123');
            expect(result).toEqual([]);
        });

        it('should return list of MMSIs flagged by user', async () => {
            const mockFlags = [
                { mmsi: '123456789' },
                { mmsi: '987654321' },
            ];

            (AisVesselFlag.find as jest.Mock).mockResolvedValue(mockFlags as never);
            const result = await aisVesselFlagService.getUserFlaggedAisVesselMmsis('user123');
            expect(result).toEqual(['123456789', '987654321']);
        });
    });

    describe('removeAllFlagsFromAisVessel', () => {
        it('should remove all flags for a given MMSI', async () => {
            const mockResult = { deletedCount: 3 };
            (AisVesselFlag.deleteMany as jest.Mock).mockResolvedValue(mockResult as never);
            const result = await aisVesselFlagService.removeAllFlagsFromAisVessel('123456789');
            expect(result.deletedCount).toBe(3);
            expect(AisVesselFlag.deleteMany).toHaveBeenCalledWith({
                mmsi: '123456789',
            });
        });

        it('should trim MMSI before removing flags', async () => {
            const mockResult = { deletedCount: 2 };
            (AisVesselFlag.deleteMany as jest.Mock).mockResolvedValue(mockResult as never);
            await aisVesselFlagService.removeAllFlagsFromAisVessel('  123456789  ');
            expect(AisVesselFlag.deleteMany).toHaveBeenCalledWith({
                mmsi: '123456789',
            });
        });

        it('should return zero deletedCount when no flags exist', async () => {
            const mockResult = { deletedCount: 0 };
            (AisVesselFlag.deleteMany as jest.Mock).mockResolvedValue(mockResult as never);
            const result = await aisVesselFlagService.removeAllFlagsFromAisVessel('123456789');
            expect(result.deletedCount).toBe(0);
        });
    });
});

