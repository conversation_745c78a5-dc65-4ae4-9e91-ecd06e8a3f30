import "dotenv/config";
import Vessel from "../models/Vessel";
import db from "../modules/db";
import { fetchCountryIsoCode } from "../modules/geolocation";
import { IVessel } from "../interfaces/Vessel";

interface MigrationStats {
    total: number;
    success: number;
    skipped: number;
    errors: number;
}

interface VesselUpdate {
    vesselId: string;
    vesselName: string;
    coordinates: [number, number];
    isoCode: string | null;
    error?: string;
}

/**
 * Extract coordinates from vessel's home_port_location
 */
function extractCoordinates(vessel: IVessel): [number, number] | null {
    if (!vessel.home_port_location) {
        return null;
    }

    // Handle GeoJSON Point format: { type: "Point", coordinates: [lng, lat] }
    if (vessel.home_port_location.coordinates && Array.isArray(vessel.home_port_location.coordinates)) {
        const [lng, lat] = vessel.home_port_location.coordinates;
        if (typeof lat === "number" && typeof lng === "number" && !isNaN(lat) && !isNaN(lng)) {
            return [lat, lng]; // Return as [latitude, longitude] for Google Maps API
        }
    }

    return null;
}

/**
 * Process a single vessel to update its country ISO code
 */
async function processVessel(vessel: IVessel, dryRun: boolean): Promise<VesselUpdate> {
    const result: VesselUpdate = {
        vesselId: vessel._id.toString(),
        vesselName: vessel.name,
        coordinates: [0, 0],
        isoCode: null,
    };

    try {
        // Extract coordinates
        const coords = extractCoordinates(vessel);
        if (!coords) {
            result.error = "Invalid home_port_location structure";
            return result;
        }

        const [lat, lng] = coords;
        result.coordinates = [lat, lng];

        // Fetch country ISO code
        const isoCode = await fetchCountryIsoCode(lat, lng);

        if (!isoCode) {
            result.error = `Could not determine country ISO code for coordinates (${lat}, ${lng})`;
            return result;
        }

        result.isoCode = isoCode;

        // Update vessel if not dry run
        if (!dryRun) {
            await Vessel.updateOne({ _id: vessel._id }, { $set: { country_iso_code: isoCode } });
        }

        return result;
    } catch (error: any) {
        result.error = error.message || "Unknown error";
        return result;
    }
}

/**
 * Update country ISO codes for all vessels with home port coordinates but no country_iso_code
 */
async function updateVesselCountryIsoCodes(dryRun: boolean = false): Promise<MigrationStats> {
    const stats: MigrationStats = {
        total: 0,
        success: 0,
        skipped: 0,
        errors: 0,
    };

    try {
        // Ensure database connection
        if (db.qmShared.readyState !== 1) {
            await new Promise<void>((resolve, reject) => {
                db.qmShared.once("open", () => resolve());
                db.qmShared.once("error", reject);
            });
        }

        console.log(`\n${dryRun ? "🔍 DRY RUN MODE - No changes will be made" : "🚀 LIVE MODE - Changes will be applied"}\n`);

        // Find all vessels with home port coordinates but no country_iso_code
        const vessels = await Vessel.find({
            home_port_location: { $ne: null, $exists: true },
            $or: [{ country_iso_code: { $exists: false } }, { country_iso_code: null }],
        }).lean();

        stats.total = vessels.length;
        console.log(`Found ${vessels.length} vessels with home port coordinates but no country_iso_code.\n`);

        if (vessels.length === 0) {
            console.log("No vessels to update.");
            return stats;
        }

        const updates: VesselUpdate[] = [];

        // Process vessels with rate limiting
        for (let i = 0; i < vessels.length; i++) {
            const vessel = vessels[i] as IVessel;
            const update = await processVessel(vessel, dryRun);

            if (update.error) {
                if (update.error.includes("Could not determine")) {
                    stats.skipped++;
                    console.log(`⚠ [${i + 1}/${vessels.length}] Skipped: ${update.vesselName} (${update.vesselId}) - ${update.error}`);
                } else {
                    stats.errors++;
                    console.error(`✗ [${i + 1}/${vessels.length}] Error: ${update.vesselName} (${update.vesselId}) - ${update.error}`);
                }
            } else if (update.isoCode) {
                stats.success++;
                const action = dryRun ? "Would update" : "✓ Updated";
                console.log(
                    `${action} [${i + 1}/${vessels.length}]: ${update.vesselName} (${update.vesselId}) with country_iso_code: ${update.isoCode}`,
                );
            }

            updates.push(update);

            // Add a small delay to avoid hitting API rate limits
            if (i < vessels.length - 1) {
                await new Promise((resolve) => setTimeout(resolve, 100));
            }
        }

        // Print summary
        console.log("\n=== Migration Summary ===");
        console.log(`Total vessels processed: ${stats.total}`);
        console.log(`${dryRun ? "Would update" : "Successfully updated"}: ${stats.success}`);
        console.log(`Skipped: ${stats.skipped}`);
        console.log(`Errors: ${stats.errors}`);

        if (dryRun && stats.success > 0) {
            console.log(`\n💡 To apply these changes, run the script without --dry-run flag`);
        }

        return stats;
    } catch (error: any) {
        console.error("Error updating vessel country ISO codes:", error);
        throw error;
    }
}

// Main execution
async function main() {
    try {
        if (!process.env.MONGO_URI) {
            throw new Error("MONGO_URI must be set in env variables");
        }

        if (!process.env.GOOGLE_API_KEY) {
            throw new Error("GOOGLE_API_KEY must be set in env variables");
        }

        // Check for dry-run flag
        const dryRun = process.argv.includes("--dry-run") || process.argv.includes("-d");

        await updateVesselCountryIsoCodes(dryRun);

        console.log("\n✅ Migration completed successfully.");
        process.exit(0);
    } catch (error: any) {
        console.error("\n❌ Migration failed:", error.message || error);
        process.exit(1);
    } finally {
        // Close database connections
        await Promise.all([db.qmShared.close().catch(() => {}), db.qm.close().catch(() => {})]);
    }
}

// Run the migration
main();
