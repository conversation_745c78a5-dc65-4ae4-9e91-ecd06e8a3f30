import mongoose from "mongoose";
import db from "../modules/db";
import { IVesselAlertBroadcast } from "src/interfaces/VesselAlertBroadcast";

// string timestamp
// string reported_at
// string origin_unit_id
// string origin_unit_mmsi
// float64 latitude
// float64 longitude
// string emergency_type
// string emergency_description
// bool active_status
// string audio_url
// string video_url
// string image_url

const vesselAlertBroadcastSchema = new mongoose.Schema({
    origin_unit_id: {
        type: String,
        required: true,
    },
    origin_vessel_id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
    },
    target_unit_id: {
        type: String,
        required: true,
    },
    target_vessel_id: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
    },
    location: {
        type: {
            type: String,
            enum: ["Point"],
            default: "Point",
        },
        coordinates: {
            type: [Number],
            required: true,
        },
    },
    emergency_type: {
        type: String,
        required: true,
    },
    emergency_description: {
        type: String,
        required: true,
    },
    reported_at: {
        type: Date,
        required: true,
    },
    creation_timestamp: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
});

const VesselAlertBroadcast = db.qmShared.model<IVesselAlertBroadcast>("VesselAlertBroadcast", vesselAlertBroadcastSchema, "vessel_alerts_broadcast");

export default VesselAlertBroadcast;
