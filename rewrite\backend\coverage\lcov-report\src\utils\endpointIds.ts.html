
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/utils/endpointIds.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">src/utils</a> endpointIds.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>5/5</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/0</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>3/3</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a>
<a name='L205'></a><a href='#L205'>205</a>
<a name='L206'></a><a href='#L206'>206</a>
<a name='L207'></a><a href='#L207'>207</a>
<a name='L208'></a><a href='#L208'>208</a>
<a name='L209'></a><a href='#L209'>209</a>
<a name='L210'></a><a href='#L210'>210</a>
<a name='L211'></a><a href='#L211'>211</a>
<a name='L212'></a><a href='#L212'>212</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">136x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">1x</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// NOTE: DO NOT EDIT *endpoint_id* OF ANY EXISTING ENDPOINT.
&nbsp;
/**
 * Visibility and access semantics for endpoints:
 * - is_public: Endpoint can be called without authentication (no API key/user token required).
 * - is_accessible: An endpoint that can be accessed by an API key. Enabling this allows the admin to provide endpoint access permission to api keys in the dashboard.
 *   An endpoint may be non-public but accessible (requires auth), or public but not accessible for
 *   certain operations that are intentionally disabled.
 */
const apiEndpointsList = [
    // ===== USER MANAGEMENT =====
    { endpoint_id: 101, name: "FETCH_TOKEN", category: "USERS", is_public: true, is_accessible: true },
    { endpoint_id: 102, name: "FETCH_USER", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 103, name: "FETCH_USERS_LIST", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 104, name: "CREATE_USER", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 105, name: "UPDATE_USER_ROLE", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 106, name: "DELETE_USER", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 107, name: "FETCH_PASSWORD_RESET_TOKEN", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 108, name: "UPDATE_PASSWORD", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 109, name: "INVITE_USER", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 110, name: "VERIFY_INVITE_USER", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 111, name: "UPDATE_USER_ALLOWED_UNITS", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 112, name: "UPDATE_USER_ORGANIZATION", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 113, name: "UPDATE_USER_SETTINGS", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 114, name: "FETCH_USERS_LIST_V2", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 115, name: "UPDATE_USER_ALLOWED_VESSELS", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 116, name: "SUGGEST_USERS", category: "USERS", is_public: false, is_accessible: false },
&nbsp;
    // ===== STREAMS =====
    { endpoint_id: 201, name: "FETCH_STREAMS_LIST", category: "STREAMS", is_public: false, is_accessible: true },
    { endpoint_id: 202, name: "FETCH_STREAM_URL", category: "STREAMS", is_public: false, is_accessible: true },
    { endpoint_id: 203, name: "GET_CLIP", category: "STREAMS", is_public: false, is_accessible: false },
    { endpoint_id: 204, name: "GET_SCREENSHOT", category: "STREAMS", is_public: false, is_accessible: false },
    { endpoint_id: 205, name: "FETCH_STREAM_URL_V2", category: "STREAMS", is_public: false, is_accessible: false },
    { endpoint_id: 206, name: "FETCH_STREAM_DASH_URL", category: "STREAMS", is_public: false, is_accessible: true },
&nbsp;
    // ===== REGIONS =====
    { endpoint_id: 301, name: "FETCH_REGIONS", category: "REGIONS", is_public: false, is_accessible: true },
&nbsp;
    // ===== VESSEL LOCATIONS =====
    { endpoint_id: 401, name: "FETCH_COORDINATES", category: "VESSEL LOCATIONS", is_public: false, is_accessible: true },
    { endpoint_id: 402, name: "FETCH_COORDINATES_V2", category: "VESSEL LOCATIONS", is_public: false, is_accessible: true },
    { endpoint_id: 403, name: "FETCH_COORDINATES_BULK", category: "VESSEL LOCATIONS", is_public: false, is_accessible: true },
    { endpoint_id: 404, name: "FETCH_COORDINATES_CLOSEST", category: "VESSEL LOCATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 405, name: "FETCH_COORDINATES_ALL_LATEST", category: "VESSEL LOCATIONS", is_public: false, is_accessible: false },
&nbsp;
    // ===== VESSEL ARTIFACTS =====
    { endpoint_id: 501, name: "FETCH_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 502, name: "FETCH_PAGINATED_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 503, name: "FETCH_ARTIFACT_FILTERS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 504, name: "FETCH_NLP_SUGGESTIONS", category: "ARTIFACTS COMPLETIONS", is_public: false, is_accessible: false },
    { endpoint_id: 505, name: "DOWNLOAD_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 506, name: "FETCH_ARTIFACTS_V2", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 507, name: "FETCH_HOURS_AGGREGATED_COUNT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 508, name: "ARCHIVE_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 509, name: "UNARCHIVE_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 510, name: "FETCH_ARCHIVED_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 511, name: "FETCH_ARTIFACT_INDICATORS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 512, name: "FETCH_FLAGGED_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 513, name: "UNFLAG_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 514, name: "FETCH_USER_FLAGGED_ARTIFACT_IDS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 515, name: "REMOVE_ALL_FLAGS_FROM_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 516, name: "FLAG_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 517, name: "FETCH_ARTIFACTS_BULK", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 518, name: "FETCH_ARTIFACT_DETAIL", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 519, name: "FLAG_AIS_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 520, name: "FETCH_EVALUATED_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
&nbsp;
    // ===== ROLES =====
    { endpoint_id: 601, name: "FETCH_ROLES", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 602, name: "CREATE_ROLE", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 603, name: "UPDATE_ROLE", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 604, name: "DELETE_ROLE", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 605, name: "REORDER_ROLE", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 606, name: "PERMISSION_UPDATE", category: "ROLES", is_public: false, is_accessible: false },
&nbsp;
    // ===== PERMISSIONS =====
    { endpoint_id: 701, name: "FETCH_PERMISSIONS", category: "PERMISSIONS", is_public: false, is_accessible: false },
&nbsp;
    // ===== LOGS =====
    { endpoint_id: 801, name: "FETCH_SESSION_LOGS", category: "LOGS", is_public: false, is_accessible: false },
    { endpoint_id: 802, name: "FETCH_SESSION_LOG_BY_ID", category: "LOGS", is_public: false, is_accessible: false },
    { endpoint_id: 803, name: "FETCH_SESSION_LOGS_BY_USER", category: "LOGS", is_public: false, is_accessible: false },
&nbsp;
    // ===== STORAGE =====
    { endpoint_id: 901, name: "FETCH_FILE_URL", category: "STORAGE", is_public: false, is_accessible: true },
    { endpoint_id: 902, name: "FETCH_BATCH_URLS", category: "STORAGE", is_public: false, is_accessible: true },
    { endpoint_id: 903, name: "FETCH_CLOUDFRONT_SIGNED_URL", category: "STORAGE", is_public: false, is_accessible: false },
    { endpoint_id: 904, name: "FETCH_CLOUDFRONT_BATCH_URLS", category: "STORAGE", is_public: false, is_accessible: false },
&nbsp;
    // ===== STATISTICS =====
    { endpoint_id: 1001, name: "FETCH_STATISTICS", category: "STATISTICS", is_public: false, is_accessible: true },
&nbsp;
    // ===== VESSELS =====
    { endpoint_id: 1101, name: "FETCH_VESSELS_INFO", category: "VESSELS", is_public: false, is_accessible: true },
&nbsp;
    // ===== OTP VERIFICATION =====
    { endpoint_id: 1201, name: "SEND_OTP", category: "OTP VERIFICATION", is_public: true, is_accessible: false },
    { endpoint_id: 1202, name: "VERIFY_OTP", category: "OTP VERIFICATION", is_public: true, is_accessible: false },
&nbsp;
    // ===== GEOLOCATION =====
    { endpoint_id: 1301, name: "FETCH_LOCATION", category: "GEOLOCATION", is_public: false, is_accessible: false },
    { endpoint_id: 1302, name: "FETCH_COUNTRY_ISO_CODE", category: "GEOLOCATION", is_public: false, is_accessible: false },
&nbsp;
    // ===== TOUR GUIDE =====
    { endpoint_id: 1401, name: "FETCH_TOUR_GUIDE", category: "TOUR GUIDE", is_public: false, is_accessible: false },
    { endpoint_id: 1402, name: "CREATE_TOUR_GUIDE", category: "TOUR GUIDE", is_public: false, is_accessible: false },
    { endpoint_id: 1403, name: "UPDATE_TOUR_GUIDE", category: "TOUR GUIDE", is_public: false, is_accessible: false },
&nbsp;
    // ===== NOTIFICATION ALERTS =====
    { endpoint_id: 1501, name: "FETCH_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1502, name: "CREATE_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1503, name: "DELETE_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1504, name: "UPDATE_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1505, name: "FETCH_NOTIFICATION_ALERTS_BY_ID", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1506, name: "UNSUBSCRIBE_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: true, is_accessible: true },
    { endpoint_id: 1507, name: "FETCH_MAP_FOR_ALERT", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1508, name: "FETCH_URL_FOR_ALERT", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1509, name: "FETCH_NOTIFICATION_ALERTS_BY_USER", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1510, name: "FETCH_MAP_CLUSTER", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
&nbsp;
    // ===== IN APP NOTIFICATIONS =====
    { endpoint_id: 1601, name: "FETCH_IN_APP_NOTIFICATIONS", category: "IN APP NOTIFICATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 1602, name: "MARK_AS_READ_IN_APP_NOTIFICATIONS", category: "IN APP NOTIFICATIONS", is_public: false, is_accessible: false },
&nbsp;
    // ===== NOTIFICATION SUMMARY =====
    { endpoint_id: 1701, name: "FETCH_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1702, name: "FETCH_NOTIFICATION_SUMMARIES_BY_ID", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1703, name: "CREATE_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1704, name: "DELETE_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1705, name: "UPDATE_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1706, name: "UNSUBSCRIBE_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: true, is_accessible: false },
    { endpoint_id: 1707, name: "FETCH_MAP_FOR_SUMMARIES_V2", category: "NOTIFICATION SUMMARY", is_public: true, is_accessible: false },
&nbsp;
    // ===== FAVOURITE ARTIFACTS =====
    { endpoint_id: 1801, name: "CREATE_FAVOURITE_ARTIFACT", category: "FAVOURITE ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 1802, name: "FETCH_ALL_FAVOURITE_ARTIFACTS", category: "FAVOURITE ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 1803, name: "DELETE_FAVOURITE_ARTIFACT", category: "FAVOURITE ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 1804, name: "FETCH_USER_FAVOURITE_ARTIFACTS", category: "FAVOURITE ARTIFACTS", is_public: false, is_accessible: false },
&nbsp;
    // ===== EMAIL DOMAINS =====
    { endpoint_id: 1901, name: "FETCH_ALLOWED_EMAIL_DOMAINS", category: "EMAIL DOMAINS", is_public: false, is_accessible: false },
&nbsp;
    // ===== REGION GROUPS =====
    { endpoint_id: 2001, name: "FETCH_REGION_GROUPS", category: "REGION GROUPS", is_public: false, is_accessible: true },
    { endpoint_id: 2002, name: "CREATE_REGION_GROUP", category: "REGION GROUPS", is_public: false, is_accessible: false },
    { endpoint_id: 2003, name: "UPDATE_REGION_GROUP", category: "REGION GROUPS", is_public: false, is_accessible: false },
    { endpoint_id: 2004, name: "DELETE_REGION_GROUP", category: "REGION GROUPS", is_public: false, is_accessible: false },
&nbsp;
    // ===== ORGANIZATIONS =====
    { endpoint_id: 2101, name: "FETCH_ORGANIZATIONS", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 2102, name: "FETCH_ORGANIZATION_BY_ID", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 2103, name: "CREATE_ORGANIZATION", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 2104, name: "UPDATE_ORGANIZATION", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 2105, name: "DELETE_ORGANIZATION", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
&nbsp;
    // ===== HOME PORTS =====
    { endpoint_id: 2201, name: "FETCH_ALL_HOME_PORTS", category: "HOME PORTS", is_public: false, is_accessible: false },
&nbsp;
    // ===== VESSEL MANAGEMENT =====
    { endpoint_id: 2301, name: "FETCH_PAGINATED_VESSEL_MANAGEMENT", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2302, name: "FETCH_VESSEL_MANAGEMENT_BY_ID", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2303, name: "CREATE_VESSEL_MANAGEMENT", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2304, name: "UPDATE_VESSEL_MANAGEMENT", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2305, name: "FETCH_VESSEL_UNIT_IDS", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2306, name: "FETCH_ASSIGNED_UNIT_IDS", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2307, name: "FETCH_ALL_VESSEL_MANAGEMENT", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
&nbsp;
    // ===== VESSEL AIS =====
    { endpoint_id: 2501, name: "FETCH_VESSEL_AIS", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2502, name: "FETCH_VESSEL_AIS_LATEST", category: "VESSEL AIS", is_public: false, is_accessible: true },
    { endpoint_id: 2503, name: "FLAG_AIS_VESSEL", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2504, name: "UNFLAG_AIS_VESSEL", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2505, name: "FETCH_FLAGGED_AIS_VESSELS", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2506, name: "FETCH_USER_FLAGGED_AIS_VESSEL_MMSIS", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2507, name: "REMOVE_ALL_FLAGS_FROM_AIS_VESSEL", category: "VESSEL AIS", is_public: false, is_accessible: false },
&nbsp;
    // ===== AUDIOS =====
    { endpoint_id: 2601, name: "FETCH_AUDIOS", category: "AUDIOS", is_public: false, is_accessible: false },
&nbsp;
    // ===== LISTS =====
    { endpoint_id: 2701, name: "FETCH_LISTS", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2702, name: "FETCH_LIST_BY_ID", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2703, name: "CREATE_LIST", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2704, name: "UPDATE_LIST", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2705, name: "DELETE_LIST", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2706, name: "SHARE_LIST_ADD_USER", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id:2707, name: "SHARE_LIST_REMOVE_USER", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2708, name: "ADD_LIST_ARTIFACT", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2709, name: "REMOVE_LIST_ARTIFACT", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2710, name: "FETCH_USER_LIST_ARTIFACT_IDS", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2711, name: "SHARE_LIST_GET_USERS", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2712, name: "DOWNLOAD_LIST", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2713, name: "COPY_LIST_ARTIFACTS", category: "LISTS", is_public: false, is_accessible: false },
&nbsp;
    // ===== VESSEL ALERTS =====
    { endpoint_id: 2801, name: "FETCH_EMERGENCY_VESSELS_COUNT", category: "VESSEL ALERTS", is_public: false, is_accessible: true },
    { endpoint_id: 2802, name: "FETCH_VESSEL_ALERTS_STATUS", category: "VESSEL ALERTS", is_public: false, is_accessible: true },
    { endpoint_id: 2803, name: "FETCH_NEARBY_VESSEL_ALERT_BROADCASTS", category: "VESSEL ALERTS", is_public: false, is_accessible: true },
] as const;
&nbsp;
type ApiEndpoint = (typeof apiEndpointsList)[number];
type EndpointName = ApiEndpoint["name"];
type EndpointId = ApiEndpoint["endpoint_id"];
&nbsp;
const endpointIds: Record&lt;EndpointName, EndpointId&gt; = Object.fromEntries(apiEndpointsList.map((e) =&gt; [e.name, e.endpoint_id])) as Record&lt;
    EndpointName,
    EndpointId
&gt;;
&nbsp;
export { endpointIds, apiEndpointsList };
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2026-01-11T11:02:56.580Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    