// NOTE: DO NOT EDIT *endpoint_id* OF ANY EXISTING ENDPOINT.

/**
 * Visibility and access semantics for endpoints:
 * - is_public: Endpoint can be called without authentication (no API key/user token required).
 * - is_accessible: An endpoint that can be accessed by an API key. Enabling this allows the admin to provide endpoint access permission to api keys in the dashboard.
 *   An endpoint may be non-public but accessible (requires auth), or public but not accessible for
 *   certain operations that are intentionally disabled.
 */
const apiEndpointsList = [
    // ===== USER MANAGEMENT =====
    { endpoint_id: 101, name: "FETCH_TOKEN", category: "USERS", is_public: true, is_accessible: true },
    { endpoint_id: 102, name: "FETCH_USER", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 103, name: "FETCH_USERS_LIST", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 104, name: "CREATE_USER", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 105, name: "UPDATE_USER_ROLE", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 106, name: "DELETE_USER", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 107, name: "FETCH_PASSWORD_RESET_TOKEN", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 108, name: "UPDATE_PASSWORD", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 109, name: "INVITE_USER", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 110, name: "VERIFY_INVITE_USER", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 111, name: "UPDATE_USER_ALLOWED_UNITS", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 112, name: "UPDATE_USER_ORGANIZATION", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 113, name: "UPDATE_USER_SETTINGS", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 114, name: "FETCH_USERS_LIST_V2", category: "USERS", is_public: true, is_accessible: false },
    { endpoint_id: 115, name: "UPDATE_USER_ALLOWED_VESSELS", category: "USERS", is_public: false, is_accessible: false },
    { endpoint_id: 116, name: "SUGGEST_USERS", category: "USERS", is_public: false, is_accessible: false },

    // ===== STREAMS =====
    { endpoint_id: 201, name: "FETCH_STREAMS_LIST", category: "STREAMS", is_public: false, is_accessible: true },
    { endpoint_id: 202, name: "FETCH_STREAM_URL", category: "STREAMS", is_public: false, is_accessible: true },
    { endpoint_id: 203, name: "GET_CLIP", category: "STREAMS", is_public: false, is_accessible: false },
    { endpoint_id: 204, name: "GET_SCREENSHOT", category: "STREAMS", is_public: false, is_accessible: false },
    { endpoint_id: 205, name: "FETCH_STREAM_URL_V2", category: "STREAMS", is_public: false, is_accessible: false },
    { endpoint_id: 206, name: "FETCH_STREAM_DASH_URL", category: "STREAMS", is_public: false, is_accessible: true },

    // ===== REGIONS =====
    { endpoint_id: 301, name: "FETCH_REGIONS", category: "REGIONS", is_public: false, is_accessible: true },

    // ===== VESSEL LOCATIONS =====
    { endpoint_id: 401, name: "FETCH_COORDINATES", category: "VESSEL LOCATIONS", is_public: false, is_accessible: true },
    { endpoint_id: 402, name: "FETCH_COORDINATES_V2", category: "VESSEL LOCATIONS", is_public: false, is_accessible: true },
    { endpoint_id: 403, name: "FETCH_COORDINATES_BULK", category: "VESSEL LOCATIONS", is_public: false, is_accessible: true },
    { endpoint_id: 404, name: "FETCH_COORDINATES_CLOSEST", category: "VESSEL LOCATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 405, name: "FETCH_COORDINATES_ALL_LATEST", category: "VESSEL LOCATIONS", is_public: false, is_accessible: false },

    // ===== VESSEL ARTIFACTS =====
    { endpoint_id: 501, name: "FETCH_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 502, name: "FETCH_PAGINATED_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 503, name: "FETCH_ARTIFACT_FILTERS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 504, name: "FETCH_NLP_SUGGESTIONS", category: "ARTIFACTS COMPLETIONS", is_public: false, is_accessible: false },
    { endpoint_id: 505, name: "DOWNLOAD_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 506, name: "FETCH_ARTIFACTS_V2", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 507, name: "FETCH_HOURS_AGGREGATED_COUNT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 508, name: "ARCHIVE_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 509, name: "UNARCHIVE_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 510, name: "FETCH_ARCHIVED_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 511, name: "FETCH_ARTIFACT_INDICATORS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 512, name: "FETCH_FLAGGED_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 513, name: "UNFLAG_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 514, name: "FETCH_USER_FLAGGED_ARTIFACT_IDS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 515, name: "REMOVE_ALL_FLAGS_FROM_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 516, name: "FLAG_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 517, name: "FETCH_ARTIFACTS_BULK", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: true },
    { endpoint_id: 518, name: "FETCH_ARTIFACT_DETAIL", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 519, name: "FLAG_AIS_ARTIFACT", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 520, name: "FETCH_EVALUATED_ARTIFACTS", category: "VESSEL ARTIFACTS", is_public: false, is_accessible: false },

    // ===== ROLES =====
    { endpoint_id: 601, name: "FETCH_ROLES", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 602, name: "CREATE_ROLE", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 603, name: "UPDATE_ROLE", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 604, name: "DELETE_ROLE", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 605, name: "REORDER_ROLE", category: "ROLES", is_public: false, is_accessible: false },
    { endpoint_id: 606, name: "PERMISSION_UPDATE", category: "ROLES", is_public: false, is_accessible: false },

    // ===== PERMISSIONS =====
    { endpoint_id: 701, name: "FETCH_PERMISSIONS", category: "PERMISSIONS", is_public: false, is_accessible: false },

    // ===== LOGS =====
    { endpoint_id: 801, name: "FETCH_SESSION_LOGS", category: "LOGS", is_public: false, is_accessible: false },
    { endpoint_id: 802, name: "FETCH_SESSION_LOG_BY_ID", category: "LOGS", is_public: false, is_accessible: false },
    { endpoint_id: 803, name: "FETCH_SESSION_LOGS_BY_USER", category: "LOGS", is_public: false, is_accessible: false },

    // ===== STORAGE =====
    { endpoint_id: 901, name: "FETCH_FILE_URL", category: "STORAGE", is_public: false, is_accessible: true },
    { endpoint_id: 902, name: "FETCH_BATCH_URLS", category: "STORAGE", is_public: false, is_accessible: true },
    { endpoint_id: 903, name: "FETCH_CLOUDFRONT_SIGNED_URL", category: "STORAGE", is_public: false, is_accessible: false },
    { endpoint_id: 904, name: "FETCH_CLOUDFRONT_BATCH_URLS", category: "STORAGE", is_public: false, is_accessible: false },

    // ===== STATISTICS =====
    { endpoint_id: 1001, name: "FETCH_STATISTICS", category: "STATISTICS", is_public: false, is_accessible: true },

    // ===== VESSELS =====
    { endpoint_id: 1101, name: "FETCH_VESSELS_INFO", category: "VESSELS", is_public: false, is_accessible: true },

    // ===== OTP VERIFICATION =====
    { endpoint_id: 1201, name: "SEND_OTP", category: "OTP VERIFICATION", is_public: true, is_accessible: false },
    { endpoint_id: 1202, name: "VERIFY_OTP", category: "OTP VERIFICATION", is_public: true, is_accessible: false },

    // ===== GEOLOCATION =====
    { endpoint_id: 1301, name: "FETCH_LOCATION", category: "GEOLOCATION", is_public: false, is_accessible: false },
    { endpoint_id: 1302, name: "FETCH_COUNTRY_ISO_CODE", category: "GEOLOCATION", is_public: false, is_accessible: false },

    // ===== TOUR GUIDE =====
    { endpoint_id: 1401, name: "FETCH_TOUR_GUIDE", category: "TOUR GUIDE", is_public: false, is_accessible: false },
    { endpoint_id: 1402, name: "CREATE_TOUR_GUIDE", category: "TOUR GUIDE", is_public: false, is_accessible: false },
    { endpoint_id: 1403, name: "UPDATE_TOUR_GUIDE", category: "TOUR GUIDE", is_public: false, is_accessible: false },

    // ===== NOTIFICATION ALERTS =====
    { endpoint_id: 1501, name: "FETCH_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1502, name: "CREATE_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1503, name: "DELETE_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1504, name: "UPDATE_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1505, name: "FETCH_NOTIFICATION_ALERTS_BY_ID", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1506, name: "UNSUBSCRIBE_NOTIFICATION_ALERTS", category: "NOTIFICATION ALERTS", is_public: true, is_accessible: true },
    { endpoint_id: 1507, name: "FETCH_MAP_FOR_ALERT", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1508, name: "FETCH_URL_FOR_ALERT", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1509, name: "FETCH_NOTIFICATION_ALERTS_BY_USER", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },
    { endpoint_id: 1510, name: "FETCH_MAP_CLUSTER", category: "NOTIFICATION ALERTS", is_public: false, is_accessible: false },

    // ===== IN APP NOTIFICATIONS =====
    { endpoint_id: 1601, name: "FETCH_IN_APP_NOTIFICATIONS", category: "IN APP NOTIFICATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 1602, name: "MARK_AS_READ_IN_APP_NOTIFICATIONS", category: "IN APP NOTIFICATIONS", is_public: false, is_accessible: false },

    // ===== NOTIFICATION SUMMARY =====
    { endpoint_id: 1701, name: "FETCH_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1702, name: "FETCH_NOTIFICATION_SUMMARIES_BY_ID", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1703, name: "CREATE_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1704, name: "DELETE_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1705, name: "UPDATE_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: false, is_accessible: false },
    { endpoint_id: 1706, name: "UNSUBSCRIBE_NOTIFICATION_SUMMARIES", category: "NOTIFICATION SUMMARY", is_public: true, is_accessible: false },
    { endpoint_id: 1707, name: "FETCH_MAP_FOR_SUMMARIES_V2", category: "NOTIFICATION SUMMARY", is_public: true, is_accessible: false },

    // ===== FAVOURITE ARTIFACTS =====
    { endpoint_id: 1801, name: "CREATE_FAVOURITE_ARTIFACT", category: "FAVOURITE ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 1802, name: "FETCH_ALL_FAVOURITE_ARTIFACTS", category: "FAVOURITE ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 1803, name: "DELETE_FAVOURITE_ARTIFACT", category: "FAVOURITE ARTIFACTS", is_public: false, is_accessible: false },
    { endpoint_id: 1804, name: "FETCH_USER_FAVOURITE_ARTIFACTS", category: "FAVOURITE ARTIFACTS", is_public: false, is_accessible: false },

    // ===== EMAIL DOMAINS =====
    { endpoint_id: 1901, name: "FETCH_ALLOWED_EMAIL_DOMAINS", category: "EMAIL DOMAINS", is_public: false, is_accessible: false },

    // ===== REGION GROUPS =====
    { endpoint_id: 2001, name: "FETCH_REGION_GROUPS", category: "REGION GROUPS", is_public: false, is_accessible: true },
    { endpoint_id: 2002, name: "CREATE_REGION_GROUP", category: "REGION GROUPS", is_public: false, is_accessible: false },
    { endpoint_id: 2003, name: "UPDATE_REGION_GROUP", category: "REGION GROUPS", is_public: false, is_accessible: false },
    { endpoint_id: 2004, name: "DELETE_REGION_GROUP", category: "REGION GROUPS", is_public: false, is_accessible: false },

    // ===== ORGANIZATIONS =====
    { endpoint_id: 2101, name: "FETCH_ORGANIZATIONS", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 2102, name: "FETCH_ORGANIZATION_BY_ID", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 2103, name: "CREATE_ORGANIZATION", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 2104, name: "UPDATE_ORGANIZATION", category: "ORGANIZATIONS", is_public: false, is_accessible: false },
    { endpoint_id: 2105, name: "DELETE_ORGANIZATION", category: "ORGANIZATIONS", is_public: false, is_accessible: false },

    // ===== HOME PORTS =====
    { endpoint_id: 2201, name: "FETCH_ALL_HOME_PORTS", category: "HOME PORTS", is_public: false, is_accessible: false },

    // ===== VESSEL MANAGEMENT =====
    { endpoint_id: 2301, name: "FETCH_PAGINATED_VESSEL_MANAGEMENT", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2302, name: "FETCH_VESSEL_MANAGEMENT_BY_ID", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2303, name: "CREATE_VESSEL_MANAGEMENT", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2304, name: "UPDATE_VESSEL_MANAGEMENT", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2305, name: "FETCH_VESSEL_UNIT_IDS", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2306, name: "FETCH_ASSIGNED_UNIT_IDS", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },
    { endpoint_id: 2307, name: "FETCH_ALL_VESSEL_MANAGEMENT", category: "VESSEL MANAGEMENT", is_public: false, is_accessible: false },

    // ===== VESSEL AIS =====
    { endpoint_id: 2501, name: "FETCH_VESSEL_AIS", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2502, name: "FETCH_VESSEL_AIS_LATEST", category: "VESSEL AIS", is_public: false, is_accessible: true },
    { endpoint_id: 2503, name: "FLAG_AIS_VESSEL", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2504, name: "UNFLAG_AIS_VESSEL", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2505, name: "FETCH_FLAGGED_AIS_VESSELS", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2506, name: "FETCH_USER_FLAGGED_AIS_VESSEL_MMSIS", category: "VESSEL AIS", is_public: false, is_accessible: false },
    { endpoint_id: 2507, name: "REMOVE_ALL_FLAGS_FROM_AIS_VESSEL", category: "VESSEL AIS", is_public: false, is_accessible: false },

    // ===== AUDIOS =====
    { endpoint_id: 2601, name: "FETCH_AUDIOS", category: "AUDIOS", is_public: false, is_accessible: false },

    // ===== LISTS =====
    { endpoint_id: 2701, name: "FETCH_LISTS", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2702, name: "FETCH_LIST_BY_ID", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2703, name: "CREATE_LIST", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2704, name: "UPDATE_LIST", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2705, name: "DELETE_LIST", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2706, name: "SHARE_LIST_ADD_USER", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2707, name: "SHARE_LIST_REMOVE_USER", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2708, name: "ADD_LIST_ARTIFACT", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2709, name: "REMOVE_LIST_ARTIFACT", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2710, name: "FETCH_USER_LIST_ARTIFACT_IDS", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2711, name: "SHARE_LIST_GET_USERS", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2712, name: "DOWNLOAD_LIST", category: "LISTS", is_public: false, is_accessible: false },
    { endpoint_id: 2713, name: "COPY_LIST_ARTIFACTS", category: "LISTS", is_public: false, is_accessible: false },
] as const;

type ApiEndpoint = (typeof apiEndpointsList)[number];
type EndpointName = ApiEndpoint["name"];
type EndpointId = ApiEndpoint["endpoint_id"];

const endpointIds: Record<EndpointName, EndpointId> = Object.fromEntries(apiEndpointsList.map((e) => [e.name, e.endpoint_id])) as Record<
    EndpointName,
    EndpointId
>;

export { endpointIds, apiEndpointsList };
