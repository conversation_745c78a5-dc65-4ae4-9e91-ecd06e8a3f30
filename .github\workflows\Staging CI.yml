name: Staging CI

on:
  push:
    branches: ["staging"]

jobs:
  build:
    runs-on: quartermaster-ec2-staging

    outputs:
      build_id: ${{ steps.buildid.outputs.BUILD_ID }}

    steps:
      - name: Navigate to Directory, Reset and Pull Changes
        run: |
          cd ~/staging.quartermaster.us/
          git restore .
          git pull

      - name: Set BUILD_ID
        id: buildid
        run: echo "BUILD_ID=$(date +%Y%m%d-%H%M%S)" >> $GITHUB_OUTPUT

      - name: Clean and Create backend .env File
        run: |
          cd ~/staging.quartermaster.us/rewrite/backend/
          rm -rf .env
          touch .env

      - name: Populate Backend .env File with Secrets
        run: |
          cd ~/staging.quartermaster.us/rewrite/backend/
          echo "${{ secrets.STAGING_QUARTERMASTER_US }}" > .env

      - name: Install Backend Dependencies
        run: |
          cd ~/staging.quartermaster.us/rewrite/backend/
          npm ci

      - name: Build Backend (TypeScript)
        run: |
          cd ~/staging.quartermaster.us/rewrite/backend/
          BUILD_ID=${{ steps.buildid.outputs.BUILD_ID }} npm run build:ci

      - name: Prune backend node_modules
        run: |
          cd ~/staging.quartermaster.us/rewrite/backend/
          npm prune --production

      - name: Clean and Create frontend .env File
        run: |
          cd ~/staging.quartermaster.us/frontend/
          rm -rf .env
          touch .env

      - name: Populate Frontend .env File with Secrets
        run: |
          cd ~/staging.quartermaster.us/frontend/
          echo "${{ secrets.STAGING_QUARTERMASTER_US_FRONTEND }}" > .env

      - name: Install Frontend Dependencies
        run: |
          cd ~/staging.quartermaster.us/frontend/
          npm ci

      - name: Build Frontend
        run: |
          cd ~/staging.quartermaster.us/frontend/
          BUILD_ID=${{ steps.buildid.outputs.BUILD_ID }} npm run build

      - name: Prune frontend node_modules
        run: |
          cd ~/staging.quartermaster.us/frontend/
          npm prune --production

  deploy:
    runs-on: quartermaster-ec2-staging
    needs: build

    steps:
      - name: Atomic Link Switch
        run: |
          cd ~/staging.quartermaster.us/
          BUILD_ID="${{ needs.build.outputs.build_id }}"

          # Link frontend
          cd frontend/
          ln -sfn "dist-${BUILD_ID}" dist

          # Link backend
          cd ../rewrite/backend/
          ln -sfn "dist-${BUILD_ID}" dist

      - name: Cleanup Old Builds
        run: |
          cd ~/staging.quartermaster.us/

          # Cleanup old frontend builds
          cd frontend/
          ls -dt dist-* | tail -n +4 | xargs -r rm -rf

          # Cleanup old backend builds
          cd ../rewrite/backend/
          ls -dt dist-* | tail -n +4 | xargs -r rm -rf

      - name: Restart PM2 Process
        run: |
          pm2 restart 'Quartermaster Staging'
          
  notify:
    runs-on: quartermaster-ec2-staging
    needs: [build, deploy]
    if: always()

    steps:
      - name: Set URL
        run: echo "URL=https://staging.quartermaster.us" >> $GITHUB_ENV

      - name: Build Slack payload once
        shell: bash
        run: |
          set -euo pipefail
  
          if [[ "${{ needs.build.result }}" == "success" && "${{ needs.deploy.result }}" == "success" ]]; then
            COLOR="good"
          else
            COLOR="danger"
          fi
  
          AUTHOR="${{ github.event.head_commit.author.name }}"
          if [[ -z "$AUTHOR" ]]; then AUTHOR="${{ github.actor }}"; fi
  
          # IMPORTANT: multiline env var syntax
          {
            echo "SLACK_PAYLOAD<<EOF"
            cat <<EOF
          {"attachments":[{"color":"${COLOR}","text":"Staging CI\nRepo: ${{ github.repository }}\nRef: ${{ github.ref_name }}\nCommit: ${{ github.sha }}\nAuthor: ${AUTHOR}\nURL: ${{ env.URL }}\nBuild: ${{ needs.build.result }}\nDeploy: ${{ needs.deploy.result }}"}]}
          EOF
            echo "EOF"
          } >> "$GITHUB_ENV"

      - name: Notify Test Slack
        uses: slackapi/slack-github-action@v2.1.1
        with:
          webhook: ${{ secrets.SLACK_WEBHOOK_URL }}
          webhook-type: incoming-webhook
          payload: ${{ env.SLACK_PAYLOAD }}
    
      - name: Notify Official Slack
        uses: slackapi/slack-github-action@v2.1.1
        with:
          webhook: ${{ secrets.SLACK_WEBHOOK_URL_CHAT }}
          webhook-type: incoming-webhook
          payload: ${{ env.SLACK_PAYLOAD }}