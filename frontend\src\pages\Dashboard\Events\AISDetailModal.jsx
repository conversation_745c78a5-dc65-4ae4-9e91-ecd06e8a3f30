import { Grid, Modal, Typography, Box, alpha } from "@mui/material";
import React from "react";
import ModalContainer from "../../../components/ModalContainer";
import dayjs from "dayjs";
import { displayCoordinates, userValues } from "../../../utils";
import { useUser } from "../../../hooks/UserHook.jsx";
import theme from "../../../theme";

export default function AISDetailModal({ showDetailModal, setShowDetailModal, selectedVessel }) {
    const { user } = useUser();

    const handleClose = () => {
        setShowDetailModal(false);
    };

    if (!selectedVessel) return null;

    const aisData = selectedVessel.aisData;
    const AISFieldWithFullWidth = [];

    const aisDetails = [
        { label: "MMSI", value: selectedVessel.mmsi },
        { label: "Name", value: aisData?.name },
        { label: "Ship Beam", value: aisData?.details?.message?.design_beam },
        { label: "Ship Length", value: aisData?.details?.message?.design_length },
        { label: "AIS Ship Message Class", value: aisData?.details?.message?.sensor_ais_class },
        { label: "AIS Ship State", value: aisData?.details?.message?.nav_state },
        { label: "AIS Ship Type", value: aisData?.details?.message?.design_ais_ship_type_name },
        { label: "Ship Length Type", value: aisData?.details?.message?.design_length_type },
        {
            label: "Location",
            value:
                aisData?.location?.coordinates
                    ? displayCoordinates(aisData.location.coordinates, !!user?.use_MGRS)
                    : undefined,
        },
        { label: "Speed over ground", value: aisData?.details?.message?.nav_speed_over_ground },
        { label: "VHF Call Sign", value: aisData?.details?.message?.comm_callsign_vhf },
        { label: "Registry Country", value: aisData?.details?.message?.portal_registry_country?.country_name },
        {
            label: "AIS Bearing Angle",
            value: aisData?.details?.message?.portal_true_bearing_deg
                ? `${Number(aisData.details.message.portal_true_bearing_deg).toFixed(2)}\u00B0`
                : undefined,
        },
        {
            label: "Timestamp",
            value: aisData?.timestamp
                ? dayjs(aisData.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))
                : undefined,
        },
    ];

    return (
        <Modal open={Boolean(showDetailModal)} onClose={handleClose}>
            <ModalContainer title="AIS Vessel Details" onClose={handleClose} showDivider>
                <Grid container gap={1} minWidth={{ xs: 300, sm: 500 }} maxWidth={"80vw"} maxHeight={"80vh"} width={"80vw"}>
                    <Grid container height={{ xs: "200px", md: "350px", lg: "30vh" }} gap={1} overflow="auto">
                        {aisDetails.map(({ label, value }, index) => (
                            <React.Fragment key={index}>
                                <Grid
                                    display="flex"
                                    alignItems={{
                                        xs: "flex-start",
                                        sx: "flex-start",
                                        md: "center",
                                    }}
                                    paddingX={1}
                                    flexDirection="column"
                                    size={{ xs: 12, sm: AISFieldWithFullWidth.includes(label) ? 12 : 3.9, md: 2.9 }}
                                >
                                    <Typography
                                        fontSize="16px"
                                        fontWeight={500}
                                        color={theme.palette.custom.mainBlue}
                                        sx={{ display: "flex", alignItems: "center", gap: "6px", textAlign: { xs: "left", sm: "left", md: "center" } }}
                                    >
                                        {label}
                                    </Typography>
                                    <Typography
                                        fontSize="16px"
                                        fontWeight={500}
                                        sx={{
                                            cursor: "default",
                                            color: "inherit",
                                            textAlign: { xs: "left", sm: "left", md: "center" },
                                        }}
                                    >
                                        {value ?? "--"}
                                    </Typography>
                                </Grid>
                            </React.Fragment>
                        ))}
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
}

