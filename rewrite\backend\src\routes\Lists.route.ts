import express, { Request, Response } from "express";
import { body, param, query } from "express-validator";
import isAuthenticated from "../middlewares/auth";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import { validateError, canAccessVessel } from "../utils/functions";
import { validateData } from "../middlewares/validator";
import listService from "../services/List.service";
import ListArtifact from "../models/ListArtifact";
import db from "../modules/db";
import mongoose from "mongoose";
import List from "../models/List";
import { processBatchItem, s3Config } from "../modules/awsS3";
import Vessel from "../models/Vessel";

const validateListNameFormat = (value: string) => {
    const MAX_LENGTH = 50;

    if (value.length > MAX_LENGTH) {
        throw new Error(`List name must be ${MAX_LENGTH} characters or less`);
    }

    if (/\s{2,}/.test(value)) {
        throw new Error("List name cannot contain multiple consecutive spaces");
    }

    if (value !== value.trim()) {
        throw new Error("List name cannot have leading or trailing spaces");
    }

    const allowedPattern = /^[a-zA-Z0-9\s\-_'()[\].,!?:;]+$/;
    const emojiPattern =
        /[\u{1F300}-\u{1F9FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F600}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{1F900}-\u{1F9FF}]|[\u{1FA00}-\u{1FA6F}]|[\u{1FA70}-\u{1FAFF}]|[\u{200D}]|[\u{20D0}-\u{20FF}]|[\u{FE00}-\u{FE0F}]|[\u{FE20}-\u{FE2F}]/gu;

    if (emojiPattern.test(value)) {
        throw new Error("Emojis are not allowed in list names");
    }

    if (!allowedPattern.test(value)) {
        throw new Error("List name contains invalid characters. Only letters, numbers, spaces, and common punctuation are allowed.");
    }

    return true;
};

const router = express.Router();

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_LISTS), isAuthenticated, async (req: Request, res: Response) => {
    try {
        const lists = await listService.getVisibleListsForUser(req.user!);
        res.json(lists);
    } catch (err) {
        validateError(err, res);
    }
});

router.get(
    "/user/artifacts",
    assignEndpointId.bind(this, endpointIds.FETCH_USER_LIST_ARTIFACT_IDS),
    isAuthenticated,
    async (req: Request, res: Response) => {
        try {
            const artifactIds = await listService.getUserListsArtifacts({ user: req.user! });
            res.json(artifactIds);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_LIST),
    isAuthenticated,
    validateData.bind(this, [
        body("name")
            .isString()
            .trim()
            .customSanitizer((value: string) => value.replace(/\s+/g, " "))
            .notEmpty()
            .custom(validateListNameFormat),
        body("sharedWithOrganization").optional().isBoolean(),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { name, sharedWithOrganization } = req.body as { name: string; sharedWithOrganization?: boolean };
            const list = await listService.createList({ name, sharedWithOrganization, user: req.user! });
            res.status(201).json(list);
        } catch (err: any) {
            if (err?.code === 11000) return res.status(409).json({ message: "Name is already taken" });
            validateError(err, res);
        }
    },
);

router.get(
    "/:listId",
    assignEndpointId.bind(this, endpointIds.FETCH_LIST_BY_ID),
    isAuthenticated,
    validateData.bind(this, [param("listId").isMongoId()]),
    async (req: Request, res: Response) => {
        try {
            const listId = req.params.listId;
            const list = await List.findById(listId);
            if (!list) return res.status(404).json({ message: "List not found" });
            if (list.is_deleted) return res.status(404).json({ message: "List not found" });
            const canRead = await listService.canReadList(list, req.user!);
            if (!canRead) return res.status(403).json({ message: "Forbidden" });
            res.json(list.toObject());
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:listId",
    assignEndpointId.bind(this, endpointIds.UPDATE_LIST),
    isAuthenticated,
    validateData.bind(this, [
        param("listId").isMongoId(),
        body("name")
            .optional()
            .isString()
            .trim()
            .customSanitizer((value: string) => value.replace(/\s+/g, " "))
            .notEmpty()
            .custom(validateListNameFormat),
        body("sharedWithOrganization").optional().isBoolean(),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { name, sharedWithOrganization } = req.body as { name?: string; sharedWithOrganization?: boolean };
            const listId = req.params.listId;
            let updated;
            if (typeof name === "string") {
                updated = await listService.renameList({ listId, name, user: req.user! });
            }
            if (typeof sharedWithOrganization === "boolean") {
                updated = await listService.toggleOrgShare({ listId, sharedWithOrganization, user: req.user! });
            }
            res.json(updated ?? { ok: true });
        } catch (err: any) {
            if (err?.code === 11000) return res.status(409).json({ message: "Name is already taken" });
            validateError(err, res);
        }
    },
);

router.delete(
    "/:listId",
    assignEndpointId.bind(this, endpointIds.DELETE_LIST),
    isAuthenticated,
    validateData.bind(this, [param("listId").isMongoId()]),
    async (req: Request, res: Response) => {
        try {
            const listId = req.params.listId;
            const result = await listService.deleteList({ listId, user: req.user! });
            res.json(result);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:listId/share/users",
    assignEndpointId.bind(this, endpointIds.SHARE_LIST_ADD_USER),
    isAuthenticated,
    validateData.bind(this, [
        param("listId").isMongoId(),
        body().custom((value) => {
            if (!value.userId && !value.email) {
                throw new Error("Either userId or email must be provided");
            }
            if (value.userId && value.email) {
                throw new Error("Only one of userId or email should be provided");
            }
            if (value.userId && !value.userId.match(/^[0-9a-fA-F]{24}$/)) {
                throw new Error("Invalid userId format");
            }
            if (value.email && !value.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
                throw new Error("Invalid email format");
            }
            return true;
        }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { userId, email } = req.body as { userId?: string; email?: string };
            if (userId) {
                await listService.shareWithUser({ listId: req.params.listId, targetUserId: userId, user: req.user! });
            } else if (email) {
                await listService.shareWithEmail({ listId: req.params.listId, email: email.trim(), user: req.user! });
            }
            res.status(204).send();
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/:listId/share/users",
    assignEndpointId.bind(this, endpointIds.SHARE_LIST_GET_USERS),
    isAuthenticated,
    validateData.bind(this, [param("listId").isMongoId()]),
    async (req: Request, res: Response) => {
        try {
            const users = await listService.getSharedUsers({ listId: req.params.listId, user: req.user! });
            res.json(users);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:listId/share/users/:userId",
    assignEndpointId.bind(this, endpointIds.SHARE_LIST_REMOVE_USER),
    isAuthenticated,
    validateData.bind(this, [param("listId").isMongoId(), param("userId").isMongoId()]),
    async (req: Request, res: Response) => {
        try {
            await listService.unshareWithUser({ listId: req.params.listId, targetUserId: req.params.userId });
            res.status(204).send();
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/:listId/artifacts",
    assignEndpointId.bind(this, endpointIds.ADD_LIST_ARTIFACT),
    isAuthenticated,
    validateData.bind(this, [param("listId").isMongoId(), body("artifactId").isMongoId()]),
    async (req: Request, res: Response) => {
        try {
            const { artifactId } = req.body as { artifactId: string };
            const result = await listService.addArtifact({ listId: req.params.listId, artifactId, user: req.user! });
            res.status(200).json({ message: `Artifact added to "${result.listName}"` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:listId/artifacts/:artifactId",
    assignEndpointId.bind(this, endpointIds.REMOVE_LIST_ARTIFACT),
    isAuthenticated,
    validateData.bind(this, [param("listId").isMongoId(), param("artifactId").isMongoId()]),
    async (req: Request, res: Response) => {
        try {
            const result = await listService.removeArtifact({ listId: req.params.listId, artifactId: req.params.artifactId, user: req.user! });
            res.status(200).json({ message: `Artifact removed from "${result.listName}"` });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/copy-from",
    assignEndpointId.bind(this, endpointIds.COPY_LIST_ARTIFACTS),
    isAuthenticated,
    validateData.bind(this, [
        body("targetType").isIn(["list", "favorites"]),
        body("targetListId").optional().isMongoId(),
        body("sourceType").isIn(["list", "favorites"]),
        body("sourceListId").optional().isMongoId(),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { targetType, targetListId, sourceType, sourceListId } = req.body as {
                targetType: "list" | "favorites";
                targetListId?: string;
                sourceType: "list" | "favorites";
                sourceListId?: string;
            };
            const result = await listService.copyArtifactsFromList({
                targetType,
                ...(targetListId && { targetListId }),
                ...(sourceListId && { sourceListId }),
                sourceType,
                user: req.user!,
            });
            res.status(200).json(result);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/:listId/artifacts",
    assignEndpointId.bind(this, endpointIds.FETCH_LIST_BY_ID),
    isAuthenticated,
    validateData.bind(this, [
        param("listId").isMongoId(),
        query("page").optional().isInt({ min: 1 }),
        query("limit").optional().isInt({ min: 1, max: 100 }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { listId } = req.params;
            const page = parseInt((req.query.page as string) || "1", 10);
            const limit = parseInt((req.query.limit as string) || "20", 10);

            const list = await List.findById(listId);
            if (!list) return res.status(404).json({ message: "List not found" });
            if (list.is_deleted) return res.status(404).json({ message: "List not found" });
            const canRead = await listService.canReadList(list, req.user!);
            if (!canRead) return res.status(403).json({ message: "Forbidden" });

            const skip = (page - 1) * limit;
            const pipeline: any[] = [
                { $match: { list_id: list._id } },
                { $sort: { created_at: -1 } },
                { $facet: { total: [{ $count: "count" }], data: [{ $skip: skip }, { $limit: limit }] } },
                { $unwind: { path: "$total", preserveNullAndEmptyArrays: true } },
                { $project: { total: "$total.count", data: "$data" } },
            ];
            const faceted = await ListArtifact.aggregate(pipeline);
            const total = faceted[0]?.total || 0;
            const items = faceted[0]?.data || [];

            const artifactIds = items.map((i: any) => new mongoose.Types.ObjectId(String(i.artifact_id)));
            const rawArtifacts = await db.qmai
                .collection("analysis_results")
                .find({ _id: { $in: artifactIds } })
                .project({
                    _id: 1,
                    unit_id: 1,
                    bucket_name: 1,
                    image_path: 1,
                    video_path: 1,
                    thumbnail_image_path: 1,
                    location: 1,
                    category: 1,
                    super_category: 1,
                    size: 1,
                    color: 1,
                    weapons: 1,
                    others: 1,
                    timestamp: 1,
                    onboard_vessel_name: 1,
                    onboard_vessel_id: 1,
                    portal: 1,
                    country_flag: 1,
                    aws_region: 1,
                    text_extraction: 1,
                    imo_number: 1,
                    vessel_features: 1,
                    home_country: 1,
                    vessel_orientation: 1,
                    true_bearing: 1,
                    det_nbbox: 1,
                })
                .toArray();

            const vesselIds = new Set<mongoose.Types.ObjectId>();
            rawArtifacts.forEach((a: any) => {
                if (a.onboard_vessel_id) {
                    vesselIds.add(new mongoose.Types.ObjectId(a.onboard_vessel_id));
                }
            });

            const vessels = await Vessel.find({ _id: { $in: Array.from(vesselIds) } }, { _id: 1, is_active: 1, region_group_id: 1 }).lean();

            const vesselMap = new Map(vessels.map((v: any) => [v._id.toString(), v]));

            const annotated = rawArtifacts.map((a: any) => {
                let canAccess = false;
                let vessel: any = null;

                if (a.onboard_vessel_id) {
                    vessel = vesselMap.get(new mongoose.Types.ObjectId(a.onboard_vessel_id).toString());
                    if (vessel) {
                        canAccess = canAccessVessel({ user: req.user! }, vessel as any);
                    }
                }

                return canAccess
                    ? { ...a, canAccess: true }
                    : {
                          _id: a._id,
                          timestamp: a.timestamp,
                          canAccess: false,
                      };
            });

            const artifactsWithUrls = annotated.map((artifact: any) => {
                const withUrls: any = { ...artifact };
                if (artifact?.image_path) {
                    withUrls.image_url = processBatchItem({
                        bucketName: artifact.bucket_name,
                        key: artifact.image_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                if (artifact?.video_path) {
                    withUrls.video_url = processBatchItem({
                        bucketName: artifact.bucket_name,
                        key: artifact.video_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                if (artifact?.thumbnail_image_path) {
                    withUrls.thumbnail_url = processBatchItem({
                        bucketName: s3Config.buckets.compressedItems.name as string,
                        key: artifact.thumbnail_image_path,
                        region: artifact.aws_region,
                    }).signedUrl;
                }
                return withUrls;
            });

            res.json({ artifacts: artifactsWithUrls, total, page, pages: Math.ceil(total / limit) });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/:listId/download",
    assignEndpointId.bind(this, endpointIds.DOWNLOAD_LIST),
    isAuthenticated,
    validateData.bind(this, [
        param("listId")
            .custom((value) => value === "favorite" || mongoose.Types.ObjectId.isValid(value))
            .withMessage("Invalid list ID"),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { listId } = req.params;
            if (listId === "favorite") {
                await listService.downloadFavorites({ user: req.user! });
            } else {
                await listService.downloadList({ listId, user: req.user! });
            }
            res.json({
                message: "Download started! You will receive an email shortly.",
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;
