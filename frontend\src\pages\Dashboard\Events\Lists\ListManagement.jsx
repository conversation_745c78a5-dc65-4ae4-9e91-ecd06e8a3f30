import { Grid, Box, CircularProgress, FormControl, Select, MenuItem, Typography, ListItemText, Button, Stack, IconButton } from "@mui/material";
import { useEffect, useMemo, useState, memo, useCallback } from "react";
import { useUser } from "../../../../hooks/UserHook";
import theme from "../../../../theme";
import Favourites from "../Favourite/Favourites.jsx";
import List from "./List";
import listsController from "../../../../controllers/Lists.controller";
import ManageLists from "./ManageLists";
import ListModal from "./ListModal";
import CopyFromModal from "./CopyFromModal";
import { Add, ArrowBackIosNew, ListAlt, Download, ContentCopy } from "@mui/icons-material";
import { getSocket } from "../../../../socket";
import useStore from "../../../../hooks/Store";
import { useApp } from "../../../../hooks/AppHook";
import { useToaster } from "../../../../hooks/ToasterHook";

const COOLDOWN_SECONDS = 15;
const COOKIE_NAME = "list_download_timestamps";

// Cookie helpers
const getCookie = (name) => {
    try {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            const cookieValue = parts.pop().split(';').shift();
            return JSON.parse(decodeURIComponent(cookieValue));
        }
    } catch (e) {
        console.warn("Failed to parse cookie:", e);
    }
    return {};
};

const setCookie = (name, value, days = 1) => {
    const expires = new Date(Date.now() + days * 24 * 60 * 60 * 1000).toUTCString();
    document.cookie = `${name}=${encodeURIComponent(JSON.stringify(value))};expires=${expires};path=/`;
};

const MAX_DISPLAY_LENGTH = 20;

const truncateName = (name, maxLength = MAX_DISPLAY_LENGTH) => {
    if (!name) return "";
    if (name.length <= maxLength) return name;
    return name.substring(0, maxLength) + "...";
};

const ListManagement = ({ vessels }) => {
    const { user } = useUser();
    const { isMobile } = useApp();
    const { selectedSaveList, setSelectedSaveList, artifactsFavourites } = useStore();
    const [lists, setLists] = useState([]);
    const [selectedListId, setSelectedListId] = useState("favorites");
    const [loading, setLoading] = useState(false);
    const [showAddModal, setShowAddModal] = useState(false);
    const [downloadingListId, setDownloadingListId] = useState(null);
    const [remainingSeconds, setRemainingSeconds] = useState(0);
    const [downloadTrigger, setDownloadTrigger] = useState(0); // Force countdown update
    const [showCopyFromModal, setShowCopyFromModal] = useState(false);
    const toaster = useToaster();

    // Check if current list/favorites is empty
    const isListEmpty = useMemo(() => {
        if (!selectedListId || selectedListId === "manage-lists") return false;
        if (selectedListId === "favorites") {
            return !artifactsFavourites || artifactsFavourites.length === 0;
        }
        const selectedList = lists.find((l) => String(l._id) === String(selectedListId));
        return !selectedList || (selectedList.artifact_count || 0) === 0;
    }, [selectedListId, lists, artifactsFavourites]);

    // Update countdown timer
    useEffect(() => {
        if (!selectedListId || selectedListId === "manage-lists") {
            setRemainingSeconds(0);
            return;
        }

        const listKey = selectedListId === "favorites" ? "favorites" : selectedListId;
        const COOLDOWN_MS = COOLDOWN_SECONDS * 1000;

        const updateCountdown = () => {
            const timestamps = getCookie(COOKIE_NAME);
            const lastDownloadTime = timestamps[listKey];
            if (!lastDownloadTime) {
                setRemainingSeconds(0);
                return false;
            }
            const now = Date.now();
            const elapsed = now - lastDownloadTime;
            if (elapsed >= COOLDOWN_MS) {
                setRemainingSeconds(0);
                return false;
            }
            const remaining = Math.ceil((COOLDOWN_MS - elapsed) / 1000);
            setRemainingSeconds(remaining);
            return true;
        };

        // Update immediately
        updateCountdown();

        // Set up interval to update every second
        const interval = setInterval(updateCountdown, 1000);
        return () => clearInterval(interval);
    }, [selectedListId, downloadTrigger]);

    const fetchLists = useCallback(async () => {
        setLoading(true);
        try {
            const data = await listsController.fetchLists();
            const safe = Array.isArray(data) ? data : [];
            setLists(safe);
            setSelectedSaveList((current) => {
                if (current?.id && current.id !== "favorites") {
                    const listExists = safe.find((l) => String(l._id) === String(current.id));
                    if (!listExists) {
                        const defaultList = { id: "favorites", name: "Favorites" };
                        if (typeof window !== "undefined") {
                            localStorage.setItem("selectedSaveList", JSON.stringify(defaultList));
                            window.dispatchEvent(
                                new CustomEvent("localStorageChange", {
                                    detail: {
                                        key: "selectedSaveList",
                                        newValue: JSON.stringify(defaultList),
                                    },
                                }),
                            );
                        }
                        return defaultList;
                    }
                    if (listExists.name !== current.name) {
                        const updatedSaveList = { ...current, name: listExists.name };
                        if (typeof window !== "undefined") {
                            localStorage.setItem("selectedSaveList", JSON.stringify(updatedSaveList));
                            window.dispatchEvent(
                                new CustomEvent("localStorageChange", {
                                    detail: {
                                        key: "selectedSaveList",
                                        newValue: JSON.stringify(updatedSaveList),
                                    },
                                }),
                            );
                        }
                        return updatedSaveList;
                    }
                }
                return current;
            });
        } catch (err) {
            console.error("Error fetching lists", err);
            setLists([]);
        } finally {
            setLoading(false);
        }
    }, [setSelectedSaveList]);

    useEffect(() => {
        fetchLists();
    }, [fetchLists]);

    useEffect(() => {
        const socket = getSocket();
        const handleListChanged = () => {
            fetchLists();
        };

        socket.on("list/changed", handleListChanged);

        return () => {
            socket.off("list/changed", handleListChanged);
        };
    }, []);

    useEffect(() => {
        const handleArtifactCountUpdate = async () => {
            try {
                const cache = await listsController.getUserListsArtifacts();
                setLists((prev) => {
                    return prev.map((list) => {
                        const listId = String(list._id);
                        const artifactCount = cache[listId]?.length || 0;
                        return {
                            ...list,
                            artifact_count: artifactCount,
                        };
                    });
                });
            } catch (error) {
                console.error("Error updating artifact counts from cache", error);
            }
        };

        const handleShowManageLists = () => {
            setSelectedListId("manage-lists");
        };

        window.addEventListener("listArtifactCountUpdate", handleArtifactCountUpdate);
        window.addEventListener("showManageLists", handleShowManageLists);

        return () => {
            window.removeEventListener("listArtifactCountUpdate", handleArtifactCountUpdate);
            window.removeEventListener("showManageLists", handleShowManageLists);
        };
    }, []);

    // Categorize lists
    const yourLists = useMemo(() => lists.filter((l) => String(l.owner_id) === String(user?._id)), [lists, user]);
    const sharedWithYou = useMemo(() => lists.filter((l) => String(l.owner_id) !== String(user?._id)), [lists, user]);

    const renderSelectedLabel = () => {
        if (selectedListId === "favorites") {
            return <Typography sx={{ color: "#FFFFFF", overflow: "hidden", textOverflow: "ellipsis", whiteSpace: "nowrap" }}>Favorites (private) {selectedSaveList?.id === "favorites" ? "★" : ""}</Typography>;
        }
        const selected = lists.find((l) => String(l._id) === String(selectedListId));
        if (!selected) return null;
        const suffix = selected.shared_with_organization || selected.owner_id !== user?._id ? "(shared)" : "(private)";
        const displayName = truncateName(selected.name);
        const fullText = `${selected.name} ${suffix} ${selectedSaveList?.id === selected._id ? "★" : ""}`;
        return (
            <Typography
                sx={{
                    color: "#FFFFFF",
                    overflow: "hidden",
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    maxWidth: "100%"
                }}
                title={fullText}
            >
                {`${displayName} ${suffix} ${selectedSaveList?.id === selected._id ? "★" : ""}`}
            </Typography>
        );
    };

    const handleSelectChange = (event) => {
        setSelectedListId(event.target.value);
    };

    const handleDownloadList = async () => {
        if (!selectedListId || selectedListId === "manage-lists" || remainingSeconds > 0 || isListEmpty) {
            if (remainingSeconds > 0) {
                toaster(`Server is processing download. Please wait ${remainingSeconds} second${remainingSeconds !== 1 ? 's' : ''} before retrying`, {
                    variant: "warning",
                });
            } else if (isListEmpty) {
                toaster(selectedListId === "favorites" ? "Favorites list is empty" : "List is empty", {
                    variant: "warning",
                });
            }
            return;
        }

        const listKey = selectedListId === "favorites" ? "favorites" : selectedListId;
        const timestamps = getCookie(COOKIE_NAME);
        timestamps[listKey] = Date.now();
        setCookie(COOKIE_NAME, timestamps);
        setRemainingSeconds(COOLDOWN_SECONDS);
        setDownloadTrigger(prev => prev + 1); // Trigger countdown update

        setDownloadingListId(selectedListId);
        try {
            await (selectedListId === "favorites" ? listsController.downloadFavorites() : listsController.downloadList({ listId: selectedListId }));
        } catch (error) {
            console.error("Error downloading list", error);
            const errorTimestamps = getCookie(COOKIE_NAME);
            delete errorTimestamps[listKey];
            setCookie(COOKIE_NAME, errorTimestamps);
            setRemainingSeconds(0);
        } finally {
            setDownloadingListId(null);
        }
    };

    const handleCopyFrom = async (targetListId) => {
        if (!selectedListId || selectedListId === "manage-lists") {
            throw new Error("No source list selected");
        }

        const sourceType = selectedListId === "favorites" ? "favorites" : "list";
        const actualSourceListId = sourceType === "list" ? selectedListId : undefined;

        const targetType = targetListId === "favorites" ? "favorites" : "list";
        const actualTargetListId = targetType === "list" ? targetListId : undefined;

        try {
            const result = await listsController.copyFromList({
                targetType,
                targetListId: actualTargetListId,
                sourceListId: actualSourceListId,
                sourceType,
            });

            fetchLists();
            window.dispatchEvent(new CustomEvent("listArtifactCountUpdate"));

            toaster(result.message || `Copied ${result.added || 0} artifact${result.added !== 1 ? "s" : ""}`, {
                variant: "success",
            });
        } catch (error) {
            console.error("Error copying artifacts", error);
            const errorMessage = error?.message || error?.response?.data?.message || "Failed to copy artifacts";
            throw new Error(errorMessage);
        }
    };

    if (loading) {
        return (
            <Grid
                container
                display={"flex"}
                justifyContent={"center"}
                alignItems={"center"}
                height={{ xs: "90%", sm: "90%" }}
                overflow={"auto"}
                marginBottom={2}
                size="grow"
            >
                <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={60} />
            </Grid>
        );
    }

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                size="grow"
            >
                <Box sx={{ mb: 2 }}>
                    {selectedListId !== "manage-lists" ? (
                        <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
                            <FormControl size="small" variant="outlined" sx={{ minWidth: { xs: "auto", lg: "300px" } }}>
                                <Select
                                    value={selectedListId}
                                    onChange={handleSelectChange}
                                    displayEmpty
                                    renderValue={() => renderSelectedLabel()}
                                    sx={{
                                        "& .MuiOutlinedInput-notchedOutline": {
                                            border: "none",
                                        },
                                        "& .MuiSelect-select": {
                                            padding: "10px",
                                            backgroundColor: theme.palette.custom.borderColor,
                                            borderRadius: "5px",
                                            color: "#FFFFFF",
                                        },
                                    }}
                                >
                                    <MenuItem value="favorites">
                                        <ListItemText
                                            primary={
                                                <Typography sx={{ color: "#FFFFFF" }}>
                                                    Favorites (private) {selectedSaveList?.id === "favorites" ? "★" : ""}
                                                </Typography>
                                            }
                                        />
                                    </MenuItem>
                                    {yourLists.map((l) => {
                                        const suffix = l.shared_with_organization ? "(shared)" : "(private)";
                                        const displayName = truncateName(l.name);
                                        const fullText = `${l.name} ${suffix} ${selectedSaveList?.id === l._id ? "★" : ""}`;
                                        return (
                                            <MenuItem key={l._id} value={l._id}>
                                                <ListItemText
                                                    primary={
                                                        <Typography
                                                            sx={{
                                                                color: "#FFFFFF",
                                                                overflow: "hidden",
                                                                textOverflow: "ellipsis",
                                                                whiteSpace: "nowrap"
                                                            }}
                                                            title={fullText}
                                                        >
                                                            {displayName} {suffix} {selectedSaveList?.id === l._id ? "★" : ""}
                                                        </Typography>
                                                    }
                                                />
                                            </MenuItem>
                                        );
                                    })}
                                    {sharedWithYou.map((l) => {
                                        const displayName = truncateName(l.name);
                                        const fullText = `${l.name} (shared) ${selectedSaveList?.id === l._id ? "★" : ""}`;
                                        return (
                                            <MenuItem key={l._id} value={l._id}>
                                                <ListItemText
                                                    primary={
                                                        <Typography
                                                            sx={{
                                                                color: "#FFFFFF",
                                                                overflow: "hidden",
                                                                textOverflow: "ellipsis",
                                                                whiteSpace: "nowrap"
                                                            }}
                                                            title={fullText}
                                                        >
                                                            {displayName} (shared) {selectedSaveList?.id === l._id ? "★" : ""}
                                                        </Typography>
                                                    }
                                                />
                                            </MenuItem>
                                        );
                                    })}
                                </Select>
                            </FormControl>
                            <Stack direction="row" spacing={1}>
                                <Button
                                    variant="outlined"
                                    size="small"
                                    onClick={() => setShowCopyFromModal(true)}
                                    disabled={selectedListId === "manage-lists" || isListEmpty}
                                    sx={{
                                        color: "#FFFFFF",
                                        borderColor: theme.palette.custom.borderColor,
                                        textTransform: "none",
                                        padding: { xs: "20px", lg: "8px 16px" },
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                        "&:disabled": {
                                            opacity: 0.5,
                                            borderColor: theme.palette.custom.borderColor,
                                        }
                                    }}
                                >
                                    <ContentCopy fontSize="small" sx={{ fontSize: "16px", color: "#FFFFFF" }} />
                                    {!isMobile && <Typography sx={{ color: "#FFFFFF", textTransform: "uppercase" }}>Copy List</Typography>}
                                </Button>
                                <Button
                                    variant="outlined"
                                    size="small"
                                    onClick={handleDownloadList}
                                    disabled={downloadingListId === selectedListId || remainingSeconds > 0 || isListEmpty}
                                    sx={{
                                        color: "#FFFFFF",
                                        borderColor: theme.palette.custom.borderColor,
                                        textTransform: "none",
                                        padding: { xs: "20px", lg: "8px 16px" },
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1,
                                        "&:disabled": {
                                            opacity: 0.5,
                                            borderColor: theme.palette.custom.borderColor,
                                        }
                                    }}
                                >
                                    <Download fontSize="small" sx={{ fontSize: "16px", color: "#FFFFFF" }} />
                                    {!isMobile && (
                                        <Typography sx={{ color: "#FFFFFF", textTransform: "uppercase" }}>
                                            {remainingSeconds > 0 ? `Wait ${remainingSeconds}s` : "Download"}
                                        </Typography>
                                    )}
                                </Button>
                                <Button
                                    variant="outlined"
                                    size="small"
                                    onClick={() => setSelectedListId("manage-lists")}
                                    sx={{
                                        color: "#FFFFFF",
                                        borderColor: theme.palette.custom.borderColor,
                                        textTransform: "none",
                                        padding: { xs: "20px", lg: "8px 16px" },
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 1
                                    }}
                                >
                                    <ListAlt fontSize="small" sx={{ fontSize: "16px", color: "#FFFFFF" }} />
                                    {!isMobile && <Typography sx={{ color: "#FFFFFF", textTransform: "uppercase" }}>Manage lists</Typography>}
                                </Button>
                            </Stack>
                        </Stack>
                    ) : (
                        <Stack direction="row" spacing={2} alignItems="center" justifyContent="space-between">
                            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                                <IconButton onClick={() => setSelectedListId("favorites")} sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px", padding: "8px" }}>
                                    <ArrowBackIosNew fontSize="small" sx={{ fontSize: "14px" }} />
                                </IconButton>
                                <Typography sx={{ color: "#FFFFFF", fontWeight: 600, fontSize: "18px" }}>Manage lists</Typography>
                            </Box>
                            <Box sx={{ ml: "auto", display: "flex", gap: 1 }}>
                                <IconButton onClick={() => setShowAddModal(true)} sx={{ border: `1px solid ${theme.palette.custom.borderColor}`, borderRadius: "5px", padding: { xs: "8px", lg: "8px 16px" }, display: "flex", alignItems: "center", gap: 1, backgroundColor: theme.palette.custom.mainBlue }}>
                                    <Add sx={{ fontSize: "18px", color: "#FFFFFF" }} />
                                    {!isMobile && <Typography sx={{ color: "#FFFFFF", fontSize: "14px", textTransform: "uppercase" }}>Add list</Typography>}
                                </IconButton>
                            </Box>
                        </Stack>
                    )}

                </Box>
                {selectedListId === "favorites" ? (
                    <Favourites vessels={vessels} />
                ) : selectedListId === "manage-lists" ? (
                    <ManageLists
                        lists={lists}
                        user={user}
                        onSelect={(id) => setSelectedListId(id)}
                        onListUpdate={(updatedList) => {
                            setLists((prev) => {
                                const index = prev.findIndex((l) => String(l._id) === String(updatedList._id));
                                if (index >= 0) {
                                    const updated = [...prev];
                                    updated[index] = { ...updated[index], ...updatedList };
                                    return updated;
                                }
                                return prev;
                            });
                            if (selectedSaveList?.id && String(selectedSaveList.id) === String(updatedList._id)) {
                                const updatedSaveList = { ...selectedSaveList, name: updatedList.name };
                                setSelectedSaveList(updatedSaveList);
                                if (typeof window !== "undefined") {
                                    localStorage.setItem("selectedSaveList", JSON.stringify(updatedSaveList));
                                    window.dispatchEvent(
                                        new CustomEvent("localStorageChange", {
                                            detail: {
                                                key: "selectedSaveList",
                                                newValue: JSON.stringify(updatedSaveList),
                                            },
                                        }),
                                    );
                                }
                            }
                        }}
                        onListDelete={(listId) => {
                            setLists((prev) => prev.filter((l) => String(l._id) !== String(listId)));
                            if (selectedListId === listId) {
                                setSelectedListId("favorites");
                            }
                            if (selectedSaveList?.id && String(selectedSaveList.id) === String(listId)) {
                                const defaultList = { id: "favorites", name: "Favorites" };
                                setSelectedSaveList(defaultList);
                                if (typeof window !== "undefined") {
                                    localStorage.setItem("selectedSaveList", JSON.stringify(defaultList));
                                    window.dispatchEvent(
                                        new CustomEvent("localStorageChange", {
                                            detail: {
                                                key: "selectedSaveList",
                                                newValue: JSON.stringify(defaultList),
                                            },
                                        }),
                                    );
                                }
                            }
                        }}
                        onListShare={(listId, sharedCount) => {
                            setLists((prev) => {
                                const index = prev.findIndex((l) => String(l._id) === String(listId));
                                if (index >= 0) {
                                    const updated = [...prev];
                                    updated[index] = { ...updated[index], shared_users_count: sharedCount };
                                    return updated;
                                }
                                return prev;
                            });
                        }}
                    />
                ) : (
                    <List list={selectedListId === "favorites" ? { id: "favorites", name: "Favorites" } : (() => { const found = lists.find((l) => String(l._id) === String(selectedListId)); return found ? { id: selectedListId, name: found.name } : null; })()} vessels={vessels} />
                )}
                <ListModal
                    open={showAddModal}
                    onClose={() => setShowAddModal(false)}
                    onSuccess={(newList) => {
                        setLists((prev) => [newList, ...prev]);
                        setSelectedListId("manage-lists");
                    }}
                />
                <CopyFromModal
                    open={showCopyFromModal}
                    onClose={() => setShowCopyFromModal(false)}
                    lists={lists}
                    sourceListId={selectedListId}
                    onCopy={handleCopyFrom}
                />
            </Grid>
        </Grid>
    );
};

export default memo(ListManagement);
