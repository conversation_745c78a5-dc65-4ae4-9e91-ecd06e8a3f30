import OpenAI from "openai";

const openaiClient = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });

export async function generateOpenAIEmbedding({
    input,
    model = "text-embedding-3-small",
}: {
    input: string;
    model?: OpenAI.Embeddings.EmbeddingModel;
}) {
    const embedding = await openaiClient.embeddings.create({
        model,
        input,
    });

    return embedding.data[0].embedding;
}
