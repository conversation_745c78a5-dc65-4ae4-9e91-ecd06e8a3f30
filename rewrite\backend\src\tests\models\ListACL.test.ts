import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('ListACL Model', () => {
    let mockMongoose: any;
    let mockDb: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create ListACL model with proper schema definition and database registration', async () => {
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/ListACL')];

        const ListACLModule = await import('../../models/ListACL');
        const ListACL = ListACLModule.default;

        expect(mockDb.qm.model).toHaveBeenCalledWith('list_acl', expect.any(Object));
        expect(ListACL).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];
        expect(schemaArg.paths).toBeDefined();

        expect(schemaArg.paths.list_id).toBeDefined();
        expect(schemaArg.paths.list_id.required).toBe(true);
        expect(schemaArg.paths.list_id.index).toBe(true);

        expect(schemaArg.paths.user_id).toBeDefined();
        expect(schemaArg.paths.user_id.required).toBe(true);
        expect(schemaArg.paths.user_id.index).toBe(true);

        expect(schemaArg.paths.created_at).toBeDefined();
        expect(schemaArg.paths.created_at.type).toBe(Date);
        expect(schemaArg.paths.created_at.required).toBe(true);
        expect(schemaArg.paths.created_at.default).toBeDefined();
        const createdAtDefault = schemaArg.paths.created_at.default();
        expect(createdAtDefault).toBeInstanceOf(Date);

        expect(schemaArg.paths.updated_at).toBeDefined();
        expect(schemaArg.paths.updated_at.type).toBe(Date);
        expect(schemaArg.paths.updated_at.required).toBe(true);
        expect(schemaArg.paths.updated_at.default).toBeDefined();
        const updatedAtDefault = schemaArg.paths.updated_at.default();
        expect(updatedAtDefault).toBeInstanceOf(Date);

        expect(schemaArg.index).toHaveBeenCalledWith({ list_id: 1, user_id: 1 }, { unique: true });
    });
});