import { Grid, CircularProgress, Box, Typography } from "@mui/material";
import { useEffect, useState, memo, useRef, useCallback } from "react";
import DetailModal from "../DetailModal";
import { useParams } from "react-router-dom";
import theme from "../../../../theme";
import VirtualizedCardList from "../VirtualizedCardList";
import listsController from "../../../../controllers/Lists.controller";

const List = ({ list, vessels }) => {
    const { id } = useParams();
    const [events, setEvents] = useState([]);
    const [filteredEvents, setFilteredEvents] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [showDetailModal, setShowDetailModal] = useState(false);
    const [selectedCard, setSelectedCard] = useState(null);
    const virtualizedListContainerRef = useRef();
    const listRef = useRef();
    const loadMoreTimeoutRef = useRef();
    const hasMore = useRef(true);
    const pageRef = useRef(1);
    const currentListIdRef = useRef(list?.id);

    const fetchArtifacts = useCallback(async (isLoadMore = false) => {
        const targetListId = currentListIdRef.current;
        if (!targetListId) return;
        
        if (!isLoadMore) {
            setIsLoading(true);
            pageRef.current = 1;
        }

        try {
            const currentPage = isLoadMore ? pageRef.current + 1 : 1;
            const res = await listsController.fetchListArtifacts({ listId: targetListId, page: currentPage, limit: 20 });
            const artifacts = res.artifacts || [];
            const total = res.total || 0;

            // Only update state if this is still the current listId
            if (targetListId !== currentListIdRef.current) {
                return;
            }

            if (artifacts.length === 0 && total === 0) {
                setEvents([]);
                setFilteredEvents([]);
                pageRef.current = 1;
                setIsLoading(false);
                hasMore.current = false;
                return;
            }

            if (isLoadMore) {
                setEvents((prev) => {
                    const newEvents = [...prev, ...artifacts];
                    hasMore.current = newEvents.length < total;
                    return newEvents;
                });
                pageRef.current = currentPage;
            } else {
                setEvents(artifacts);
                setFilteredEvents(artifacts);
                pageRef.current = 1;
                hasMore.current = artifacts.length < total;
            }
            setIsLoading(false);
        } catch (err) {
            // Only update state if this is still the current listId
            if (targetListId !== currentListIdRef.current) {
                return;
            }
            console.error("Error fetching list artifacts", err);
            setEvents([]);
            setFilteredEvents([]);
            setIsLoading(false);
            hasMore.current = false;
        }
    }, []);

    useEffect(() => {
        setEvents([]);
        setFilteredEvents([]);

        if (!list?.id) {
            currentListIdRef.current = null;
            return;
        }

        currentListIdRef.current = list.id;
        hasMore.current = true;
        pageRef.current = 1;
        fetchArtifacts(false);
    }, [list?.id, fetchArtifacts]);

    const handleLoadMore = useCallback(() => {
        if (!isLoading && hasMore.current && !id) {
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }

            loadMoreTimeoutRef.current = setTimeout(() => {
                fetchArtifacts(true);
            }, 500);
        }
    }, [isLoading, hasMore.current, id, fetchArtifacts]);

    useEffect(() => {
        setFilteredEvents(events);
    }, [events]);

    useEffect(() => {
        return () => {
            if (loadMoreTimeoutRef.current) {
                clearTimeout(loadMoreTimeoutRef.current);
            }
        };
    }, []);

    useEffect(() => {
        const handleArtifactRemoved = (event) => {
            const { listId, artifactId, operation } = event.detail || {};
            if (listId === list?.id && operation === "remove" && artifactId) {
                setEvents((prev) => prev.filter((event) => event._id !== artifactId));
            }
        };

        window.addEventListener("listArtifactCountUpdate", handleArtifactRemoved);
        return () => window.removeEventListener("listArtifactCountUpdate", handleArtifactRemoved);
    }, [list?.id]);

    if (!list?.id) {
        return null;
    }

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"90%"}>
            <Grid
                container
                overflow={"auto"}
                display={"block"}
                border={`1px solid ${theme.palette.custom.borderColor}`}
                borderRadius={"10px"}
                padding={"10px 24px"}
                size="grow"
            >
                {isLoading ? (
                    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "200px" }}>
                        <CircularProgress />
                    </Box>
                ) : filteredEvents.length === 0 ? (
                    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "200px" }}>
                        <Typography sx={{ color: "#FFFFFF" }}>No artifacts in this list</Typography>
                    </Box>
                ) : (
                    <Grid container height={"100%"} overflow={"hidden"} ref={virtualizedListContainerRef}>
                        <VirtualizedCardList
                            ref={listRef}
                            events={filteredEvents}
                            setShowDetailModal={setShowDetailModal}
                            setSelectedCard={setSelectedCard}
                            isLoading={isLoading}
                            onLoadMore={handleLoadMore}
                            hasMore={hasMore.current && !id}
                            containerRef={virtualizedListContainerRef}
                            list={list}
                        />
                    </Grid>
                )}
            </Grid>
            <DetailModal
                showDetailModal={showDetailModal}
                setShowDetailModal={setShowDetailModal}
                selectedCard={selectedCard}
                setSelectedCard={setSelectedCard}
                id={id}
                list={list}
            />
        </Grid>
    );
};

export default memo(List);
