import { Grid, Typography, Tooltip, Link, Box } from "@mui/material";
import SentimentDissatisfiedOutlinedIcon from "@mui/icons-material/SentimentDissatisfiedOutlined";
import { memo, useMemo, useEffect, useState } from "react";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";

const Card = ({ card, setShowDetailModal, setSelectedCard, buttonsToShow, list }) => {
    const [src, setSrc] = useState(null);
    const { user } = useUser();
    const [thumbnail, setThumbnail] = useState(null);
    const { vesselInfo } = useVesselInfo();
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    const roundedCoordinates = useMemo(
        () => displayCoordinates(card?.location?.coordinates, !!user?.use_MGRS),
        [card?.location?.coordinates, user?.use_MGRS],
    );

    const vessel = vesselInfo.find((v) => v.vessel_id === card?.onboard_vessel_id);
    const vesselName = vessel?.name || "Unknown";
    const isVideo = Boolean(card?.video_path);

    const handleClick = () => {
        const cannotAccess = card?.canAccess === false || card?.portal?.canAccess === false;
        if (cannotAccess) {
            return;
        }
        setShowDetailModal(true);
        setSelectedCard({
            ...card,
            vesselName,
        });
    };

    useEffect(() => {
        if (!card) return;
        const thumbnailUrl = card?.thumbnail_url;
        const imageUrl = card?.image_url;
        const videoUrl = card?.video_url;

        if (isVideo) {
            setThumbnail(thumbnailUrl || imageUrl || null);
            setSrc(null); // Don't pass video URL to prevent video preloading in cards
        } else {
            setThumbnail(thumbnailUrl || imageUrl || null);
            setSrc(imageUrl || null);
        }
    }, [card, isVideo]);

    if (!vesselInfo) return <Typography>No vessel info</Typography>;
    if (!card) return null;

    const cannotAccess = card?.canAccess === false || card?.portal?.canAccess === false;

    return (
        <Grid
            container
            paddingTop={"0 !important"}
            height={"100%"}
            maxHeight={"350px"}
            className={"events-step-2"}
            onClick={handleClick}
            sx={{ cursor: cannotAccess ? "default" : "pointer" }}
        >
            <Grid container backgroundColor={"primary.main"} borderRadius={2} padding={1} gap={1}>
                {cannotAccess ? (
                    <Grid size={12} display={"flex"} alignItems={"center"} justifyContent={"center"}>
                        <Box
                            sx={{
                                width: "100%",
                                height: "100%",
                                borderRadius: 2,
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                justifyContent: "center",
                                gap: 1,
                            }}
                        >
                            <SentimentDissatisfiedOutlinedIcon sx={{ color: "#8A8F98", fontSize: 56 }} />
                            <Typography sx={{ color: "#AAB0B8", fontSize: 16, fontWeight: 600, mt: 1 }}>
                                You cannot access this artifact.
                            </Typography>
                            <Typography sx={{ color: "#8A8F98", fontSize: 14, textAlign:"center" }}>
                                Contact{' '}
                                <Link
                                    href="mailto:<EMAIL>"
                                    underline="always"
                                    sx={{ color: "#69A7FF", fontWeight: 600, textDecoration: "underline" }}
                                    onClick={(e) => e.stopPropagation()}
                                >
                                    <EMAIL>
                                </Link>{' '}
                                for more information
                            </Typography>
                        </Box>
                    </Grid>
                ) : (
                    <>
                        <Grid size={12} height={"200px"}>
                            <PreviewMedia
                                thumbnailLink={thumbnail}
                                originalLink={src}
                                cardId={card._id}
                                isImage={!isVideo}
                                style={{ borderRadius: 8 }}
                                showVideoThumbnail={isVideo}
                                onThumbnailClick={handleClick}
                                showArchiveButton={hasManageArtifacts}
                                isArchived={card?.portal?.is_archived || false}
                                vesselId={card?.onboard_vessel_id}
                                buttonsToShow={buttonsToShow}
                                list={list}
                            />
                        </Grid>

                        <Grid container size={12}>
                            <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                                <Tooltip title={vesselName.length > 12 ? vesselName : ""}>
                                    <Typography fontSize={"14px"} fontWeight={500}>
                                        {vesselName && (vesselName.length > 12 ? vesselName.slice(0, 12) + "..." : vesselName)}
                                    </Typography>
                                </Tooltip>
                                <Typography fontSize={"14px"} fontWeight={500}>
                                    {dayjs(card.timestamp)
                                        // .tz(timezone)
                                        .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                                </Typography>
                            </Grid>
                            <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                                <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                                    Location
                                </Typography>
                                <Typography fontSize={"14px"} fontWeight={500} color={theme.palette.custom.mainBlue}>
                                    Category
                                </Typography>
                            </Grid>
                            <Grid display={"flex"} justifyContent={"space-between"} alignItems={"center"} paddingX={1} size={12}>
                                <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"}>
                                    {roundedCoordinates}
                                </Typography>
                                <Typography fontSize={"14px"} fontWeight={500} maxWidth={"50%"} textAlign={"right"}>
                                    {card.super_category
                                        ? card.super_category.length > 12
                                            ? card.super_category.slice(0, 12) + "..."
                                            : card.super_category
                                        : "Unspecified category"}
                                </Typography>
                            </Grid>
                        </Grid>
                    </>
                )}
            </Grid>
        </Grid>
    );
};

export default memo(Card);
