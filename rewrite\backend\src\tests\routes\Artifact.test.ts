import User from '../../models/User';
import { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } from '../data/Auth';
import ApiKey from '../../models/ApiKey';
import request from "supertest";
import app from '../../server';
import { artifactsList } from '../data/Artifacts';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import { qm, qmai } from '../mocks/modules/db.mock';
import vesselService from '../../services/Vessel.service';
import artifactService from '../../services/Artifact.service';
import { vesselsList } from '../data/Vessels';
import { canAccessVessel, groupByImage, validateError, generateTimeSeries, userHasPermissions, generateZip } from '../../utils/functions';
import Vessel from '../../models/Vessel';
import { getObjectStream, processBatchItem } from '../../modules/awsS3';
import ioEmitter from '../../modules/ioEmitter';
import artifactFlagService from '../../services/ArtifactFlag.service';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/Vessel', () => require('../mocks/models/vessel.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../models/ArtifactFlag', () => require('../mocks/models/artifactFlag.mock'));
jest.mock('../../services/Vessel.service', () => require('../mocks/services/vessel.mock'));
jest.mock('../../services/ArtifactFlag.service', () => require('../mocks/services/artifactFlag.mock'));
jest.mock('../../utils/functions', () => require('../mocks/utils/functions.mock'));
jest.mock('../../modules/awsS3', () => require('../mocks/modules/awsS3.mock'));
jest.mock('../../services/Artifact.service', () => require('../mocks/services/artifact.mock'));
jest.mock('../../modules/ioEmitter', () => ({
    emit: jest.fn()
}));

describe('Artifacts API', () => {

    describe('POST /api/artifacts/', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (vesselService.find as jest.Mock).mockResolvedValue(vesselsList as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                    (artifactService.buildArtifactsQuery as jest.Mock).mockReturnValue({ query: {} });
                    (artifactService.fetchArtifacts as jest.Mock).mockResolvedValue({ artifacts: artifactsList.slice(0, 10), totalCount: 50 });
                    (artifactService.unifyArtifacts as jest.Mock).mockReturnValue(artifactsList.slice(0, 10));
                    (artifactService.groupArtifacts as jest.Mock).mockReturnValue([]);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                // Authentication tests
                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts').send({});
                    expect(res.status).toBe(401);
                });

                // Validation tests
                it('should return 400 if excludeIds is not an array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ excludeIds: 'invalid', page: 1, pageSize: 10 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if page is less than 1', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 0, pageSize: 10 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if pageSize is less than 1', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 0 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if page is not an integer', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1.5, pageSize: 10 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if pageSize is not an integer', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10.5 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if filters is not an object', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if group is not 0 or 1', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, group: 2 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if group is not an integer', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, group: 0.5 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if projection is not an object', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, projection: 'invalid' });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if projection contains invalid fields', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, projection: { invalidField: 1 } });
                    expect(res.status).toBe(400);
                });

                it('should accept empty projection object', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, projection: {} });
                    expect(res.status).not.toBe(400);
                });

                it('should accept undefined projection', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });
                    expect(res.status).not.toBe(400);
                });

                it('should accept valid single field projection', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, projection: { _id: 1 } });
                    expect(res.status).not.toBe(400);
                });

                it('should accept valid multiple fields projection', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ 
                            page: 1, 
                            pageSize: 10, 
                            projection: { 
                                _id: 1, 
                                unit_id: 1, 
                                timestamp: 1,
                                category: 1 
                            } 
                        });
                    expect(res.status).not.toBe(400);
                });

                it('should return 400 if projection contains multiple invalid fields', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ 
                            page: 1, 
                            pageSize: 10, 
                            projection: { 
                                invalidField1: 1, 
                                invalidField2: 1 
                            } 
                        });
                    expect(res.status).toBe(400);
                    expect(res.body.message || res.body.error || JSON.stringify(res.body)).toContain('Invalid projection fields');
                    expect(res.body.message || res.body.error || JSON.stringify(res.body)).toContain('invalidField1');
                    expect(res.body.message || res.body.error || JSON.stringify(res.body)).toContain('invalidField2');
                });

                it('should return 400 if projection contains mix of valid and invalid fields', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ 
                            page: 1, 
                            pageSize: 10, 
                            projection: { 
                                _id: 1,
                                unit_id: 1,
                                invalidField: 1 
                            } 
                        });
                    expect(res.status).toBe(400);
                    expect(res.body.message || res.body.error || JSON.stringify(res.body)).toContain('Invalid projection fields');
                    expect(res.body.message || res.body.error || JSON.stringify(res.body)).toContain('invalidField');
                });

                it('should return 400 with error message listing allowed fields', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ 
                            page: 1, 
                            pageSize: 10, 
                            projection: { 
                                invalidField: 1 
                            } 
                        });
                    expect(res.status).toBe(400);
                    const responseText = JSON.stringify(res.body);
                    expect(responseText).toContain('Invalid projection fields');
                    expect(responseText).toContain('Allowed fields');
                    expect(responseText).toContain('_id'); // At least one valid field should be mentioned
                });

                it('should accept all valid projection fields', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const validProjection = {
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        image_path: 1,
                        video_path: 1,
                        location: 1,
                        depth_estimation: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        weapons: 1,
                        others: 1,
                        timestamp: 1,
                        onboard_vessel_name: 1,
                        onboard_vessel_id: 1,
                        portal: 1,
                        country_flag: 1,
                        aws_region: 1,
                        text_extraction: 1,
                        imo_number: 1,
                        thumbnail_image_path: 1,
                        vessel_features: 1,
                        home_country: 1,
                        vessel_orientation: 1,
                        true_bearing: 1,
                        det_nbbox: 1,
                        det_conf: 1
                    };
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ 
                            page: 1, 
                            pageSize: 10, 
                            projection: validProjection
                        });
                    expect(res.status).not.toBe(400);
                });

                it('should return 400 if searchQuery is not a string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, searchQuery: 123 });
                    expect(res.status).toBe(400);
                });

                it('should return 200 with custom page and pageSize', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 2, pageSize: 15 });
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('page', 2);
                    expect(res.body).toHaveProperty('pageSize', 15);
                    expect(artifactService.fetchArtifacts).toHaveBeenCalledWith({
                        query: {},
                        page: 2,
                        pageSize: 15,
                        projection: undefined,
                        searchQuery: undefined,
                    });
                });

                it('should return 200 with excludeIds', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const excludeIds = ['507f1f77bcf86cd799439011', '507f1f77bcf86cd799439012'];
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, excludeIds });
                    expect(res.status).toBe(200);
                    expect(artifactService.buildArtifactsQuery).toHaveBeenCalledWith({
                        filters: undefined,
                        allowedVessels: expect.any(Array),
                        excludeIds: expect.arrayContaining(excludeIds.map(() => expect.any(Object))),
                        searchQuery: undefined,
                    });
                });

                it('should return 200 with filters.id', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { id: '507f1f77bcf86cd799439011' } });
                    expect(res.status).toBe(200);
                    expect(artifactService.buildArtifactsQuery).toHaveBeenCalledWith({
                        filters: { id: '507f1f77bcf86cd799439011' },
                        allowedVessels: expect.any(Array),
                        excludeIds: undefined,
                        searchQuery: undefined,
                    });
                });

                it('should return 200 with filters.start_time and end_time', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            page: 1,
                            pageSize: 10,
                            filters: { start_time: 1622547800, end_time: 1622547900 }
                        });
                    expect(res.status).toBe(200);
                    expect(artifactService.buildArtifactsQuery).toHaveBeenCalledWith({
                        filters: { start_time: 1622547800, end_time: 1622547900 },
                        allowedVessels: expect.any(Array),
                        excludeIds: undefined,
                        searchQuery: undefined,
                    });
                });

                it('should return 200 with filters.categories', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { categories: ['metal', 'plastic'] } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.colors (without searchQuery)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { colors: ['red', 'blue'] } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.sizes (without searchQuery)', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { sizes: ['small', 'large'] } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.type video', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { type: 'video' } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.type image', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { type: 'image' } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.vessel_ids', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const vesselId = vesselsList[0]._id.toString();
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { vessel_ids: [vesselId] } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.country_flags', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { country_flags: ['US', 'UK'] } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.weapons', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { weapons: ['gun', 'knife'] } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.host_vessel true', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { host_vessel: true } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.host_vessel false', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { host_vessel: false } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.ais_filter true', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { ais_filter: true } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.ais_filter false', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { ais_filter: false } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with filters.weaponized true', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { weaponized: true } });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with all filters combined', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            page: 1,
                            pageSize: 10,
                            filters: {
                                categories: ['metal'],
                                start_time: 1622547800,
                                end_time: 1622547900,
                                colors: ['red'],
                                sizes: ['small'],
                                type: 'image',
                                vessel_ids: [vesselsList[0]._id.toString()],
                                country_flags: ['US'],
                                weapons: ['gun'],
                                host_vessel: false,
                                ais_filter: true,
                                weaponized: true,
                            }
                        });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with searchQuery', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, searchQuery: 'test query' });
                    expect(res.status).toBe(200);
                    expect(artifactService.buildArtifactsQuery).toHaveBeenCalledWith({
                        filters: undefined,
                        allowedVessels: expect.any(Array),
                        excludeIds: undefined,
                        searchQuery: 'test query',
                    });
                    expect(artifactService.fetchArtifacts).toHaveBeenCalledWith({
                        query: {},
                        page: 1,
                        pageSize: 10,
                        projection: undefined,
                        searchQuery: 'test query',
                    });
                });

                it('should return 200 with searchQuery and filters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({
                            page: 1,
                            pageSize: 10,
                            searchQuery: 'test query',
                            filters: { categories: ['metal'] }
                        });
                    expect(res.status).toBe(200);
                });

                it('should return 200 with group=1 and no searchQuery', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (artifactService.unifyArtifacts as jest.Mock).mockReturnValue(artifactsList.slice(0, 5));
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, group: 1 });
                    expect(res.status).toBe(200);
                    expect(artifactService.groupArtifacts).toHaveBeenCalledWith(artifactsList.slice(0, 5), 0.7);
                    expect(res.body).toHaveProperty('groupedArtifacts');
                });

                it('should not group artifacts when group=1 but searchQuery is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, group: 1, searchQuery: 'test' });
                    expect(res.status).toBe(200);
                    expect(artifactService.groupArtifacts).not.toHaveBeenCalled();
                    expect(res.body).not.toHaveProperty('groupedArtifacts');
                });

                it('should not group artifacts when group=0', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, group: 0 });
                    expect(res.status).toBe(200);
                    expect(artifactService.groupArtifacts).not.toHaveBeenCalled();
                    expect(res.body).not.toHaveProperty('groupedArtifacts');
                });

                it('should not group artifacts when unified artifacts is empty', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (artifactService.unifyArtifacts as jest.Mock).mockReturnValue([]);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, group: 1 });
                    expect(res.status).toBe(200);
                    expect(artifactService.groupArtifacts).not.toHaveBeenCalled();
                });

                it('should return 200 with empty artifacts array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (artifactService.fetchArtifacts as jest.Mock).mockResolvedValue({ artifacts: [], totalCount: 0 });
                    (artifactService.unifyArtifacts as jest.Mock).mockReturnValue([]);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(200);
                    expect(res.body.artifacts).toHaveLength(0);
                    expect(res.body.totalCount).toBe(0);
                });

                it('should return 200 with totalCount null when searchQuery is provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (artifactService.fetchArtifacts as jest.Mock).mockResolvedValue({ artifacts: artifactsList.slice(0, 5), totalCount: null });
                    (artifactService.unifyArtifacts as jest.Mock).mockReturnValue(artifactsList.slice(0, 5));
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, searchQuery: 'test' });
                    expect(res.status).toBe(200);
                    expect(res.body.totalCount).toBeNull();
                });

                // Error handling tests
                it('should return 403 when buildArtifactsQuery returns error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (artifactService.buildArtifactsQuery as jest.Mock).mockReturnValue({
                        query: {},
                        error: { status: 403, message: 'Cannot access artifacts' }
                    });
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, filters: { vessel_ids: ['unauthorized'] } });
                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe('Cannot access artifacts');
                });

                it('should return 500 when vesselService.find throws error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.find as jest.Mock).mockRejectedValue(new Error('Database error') as never);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(500);
                });

                it('should return 500 when fetchArtifacts throws error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (artifactService.fetchArtifacts as jest.Mock).mockRejectedValue(new Error('Fetch error') as never);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(500);
                });

                it('should return 500 when unifyArtifacts throws error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (artifactService.unifyArtifacts as jest.Mock).mockImplementation(() => {
                        throw new Error('Unify error');
                    });
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(500);
                });

                it('should return 500 when groupArtifacts throws error', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (artifactService.unifyArtifacts as jest.Mock).mockReturnValue(artifactsList.slice(0, 5));
                    (artifactService.groupArtifacts as jest.Mock).mockImplementation(() => {
                        throw new Error('Group error');
                    });
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10, group: 1 });
                    expect(res.status).toBe(500);
                });

                it('should handle response close event', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    let resolveFetch: any;
                    const fetchPromise = new Promise((resolve) => {
                        resolveFetch = resolve;
                    });
                    (artifactService.fetchArtifacts as jest.Mock).mockReturnValue(fetchPromise);
                    
                    const reqPromise = request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });
                    
                    // Wait a bit then resolve fetch
                    setTimeout(() => {
                        resolveFetch({ artifacts: [], totalCount: 0 });
                    }, 10);
                    
                    const res = await reqPromise;
                    expect(res.status).toBe(200);
                });

                it('should filter allowed vessels correctly', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (canAccessVessel as jest.Mock).mockImplementation((_req, vessel) => {
                        return vessel._id.toString() === vesselsList[0]._id.toString();
                    });
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(200);
                    expect(artifactService.buildArtifactsQuery).toHaveBeenCalledWith({
                        filters: undefined,
                        allowedVessels: [vesselsList[0]._id.toString()],
                        excludeIds: undefined,
                        searchQuery: undefined,
                    });
                });

                it('should handle empty allowedVessels array', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app)
                        .post('/api/artifacts')
                        .set('Authorization', authToken)
                        .send({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(200);
                    expect(artifactService.buildArtifactsQuery).toHaveBeenCalledWith({
                        filters: undefined,
                        allowedVessels: [],
                        excludeIds: undefined,
                        searchQuery: undefined,
                    });
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:vesselName', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                            limit: jest.fn().mockReturnValue((artifactsList.slice(0, 10) as never)),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50 as never),
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/vesselA')
                        .send({});
                    expect(res.status).toBe(401);
                });

                it('should return 400 if payload is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: 'invalid', startTimestamp: 'invalid', endTimestamp: 'invalid' });

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch artifacts successfully with valid data', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(null as never),
                            limit: jest.fn().mockReturnValue((null as never)),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(50 as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .set('Referer', '/docs')
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800, endTimestamp: 1622547900 });

                    expect(res.status).toBe(200);
                });

                it('should handle internal server errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockRejectedValue(new Error('Database error') as never),
                        }),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({});

                    expect(res.status).toBe(500);
                });

                it('should return paginated results when page and pageSize are provided', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                        }),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800 });

                    expect(res.status).toBe(200);
                    expect(res.body.length).toBe(10);
                });

                it('should return 400 if endTimestamp is provided but startTimestamp is not', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], endTimestamp: 1622547900 });

                    expect(res.status).toBe(400);
                    expect(res.body.message).toBe("startTimestamp is required when endTimestamp is provided");
                });

                it('should return 404 if vessel is not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue(null as never);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800 });

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe("Unit does not exist or is no longer active");
                });

                it('should return 403 if vessel is not accessible', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findByAssignedUnitId as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);

                    const res = await request(app)
                        .post('/api/artifacts/vesselA')
                        .set('Authorization', authToken)
                        .send({ excludeIds: ['66fd5d2f5fde87ba39de36b7'], startTimestamp: 1622547800 });

                    expect(res.status).toBe(403);
                    expect(res.body.message).toBe("Cannot access artifacts for 'vesselA'");
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });


    describe('GET /api/artifacts/filters', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/filters');
                    expect(res.status).toBe(401);
                });

                it('should return 200 and fetch filters if the user is authorized or 403 for api-key authorization', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    qm.collection.mockReturnValue(({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                            project: jest.fn().mockReturnValue({
                                toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                            }),
                        }),
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(artifactsList.slice(0, 10) as never),
                        }),
                    }));
                    qmai.collection.mockReturnValue(({
                        distinct: jest.fn().mockResolvedValue(["small", " Small ", "LARGE", null, "large"] as never),
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([{ name: 'Missile', count: 15 }, { name: 'Gun', count: 12 }] as never),
                        }),
                    }));

                    const res = await request(app)
                        .get('/api/artifacts/filters')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Object);
                    expect(res.body).toHaveProperty('superCategories');
                    expect(res.body).toHaveProperty('countryFlags');
                    expect(res.body).toHaveProperty('sizes');
                    expect(res.body).toHaveProperty('weapons');
                    expect(res.body.sizes).toEqual(["Small", "Large"]);
                    expect(res.body.weapons).toEqual(["Missile", "Gun"]);
                });

                it('should return 500 if an error occurs while fetching filters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/artifacts/filters')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/activityIndicators/bulk', () => {

        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (vesselService.findById as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                    qmai.collection.mockReturnValue({
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([{ _id: 'v1', artifacts: [] }] as never),
                        }),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/activityIndicators/bulk');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if timestamps are missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()] });
                    expect(res.status).toBe(400);
                });

                it('should return 403 if no accessible vessels', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()], startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(403);
                });

                it('should return grouped results', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()], startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(200);
                });

                it('should return 400 if vesselIds is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [] });
                    expect(res.status).toBe(400);
                });

                it('should return 400 if startTimestamp is invalid', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const originalValidateData = require('../../middlewares/validator').validateData;
                    require('../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()] });
                    expect(res.status).toBe(400);

                    require('../../middlewares/validator').validateData = originalValidateData;
                });

                it('should return 500 if an error occurs while fetching activity indicators', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (vesselService.findById as jest.Mock).mockRejectedValue(new Error('Database error') as never);
                    const res = await request(app)
                        .post('/api/artifacts/activityIndicators/bulk')
                        .set('Authorization', authToken)
                        .send({ vesselIds: [vesselsList[0]._id.toString()], startTimestamp: Date.now() - 1000, endTimestamp: Date.now() });
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/archived', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([{ _id: 'a1' }] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/archived');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid pagination', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/archived?page=0&pageSize=0')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return archived artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while fetching archived artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        countDocuments: jest.fn().mockRejectedValue(new Error('Database error') as never),
                    });

                    const originalValidateData = require('../../middlewares/validator').validateData;
                    require('../../middlewares/validator').validateData = jest.fn((_validations, _req, _res, next: any) => {
                        next();
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);

                    require('../../middlewares/validator').validateData = originalValidateData;
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/archive', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({ value: { _id: validId } } as never),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/archive`);
                    expect(res.status).toBe(401);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOneAndUpdate: jest.fn().mockResolvedValue({ value: null } as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/archive`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should archive artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post(`/api/artifacts/${validId}/archive`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while archiving artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Database error') as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/archive`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/unarchive', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({ value: { _id: validId } } as never),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/unarchive`);
                    expect(res.status).toBe(401);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOneAndUpdate: jest.fn().mockResolvedValue({ value: null } as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/unarchive`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should unarchive artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post(`/api/artifacts/${validId}/unarchive`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while unarchiving artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Database error') as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/unarchive`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/hoursAggregatedCount', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        collection: jest.fn(),
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue([
                                { timestamp: new Date(), count: 3 },
                                { timestamp: new Date(), count: 2 },
                            ] as never),
                        }),
                    } as never);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    (generateTimeSeries as jest.Mock).mockReturnValue({
                        timestamp: new Date(),
                        count: 3,
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/hoursAggregatedCount');
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid params', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/hoursAggregatedCount?startTimestamp=abc&endTimestamp=xyz&interval=0')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return aggregated counts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const now = Date.now();
                    const res = await request(app)
                        .get(`/api/artifacts/hoursAggregatedCount?startTimestamp=${now - 3600000}&endTimestamp=${now}&interval=5`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while fetching aggregated counts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const now = Date.now();
                    qmai.collection.mockReturnValue({
                        aggregate: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockRejectedValue(new Error('Database error') as never),
                        })
                    });
                    const res = await request(app)
                        .get(`/api/artifacts/hoursAggregatedCount?startTimestamp=${now - 3600000}&endTimestamp=${now}&interval=5`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/bulk', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    const vesselIdsForTest = vesselsList.slice(0, 2).map((v: any) => v._id.toString());
                    const mockArtifacts = artifactsList.slice(0, 5).map((a, idx) => ({
                        ...a,
                        onboard_vessel_id: vesselIdsForTest[idx % vesselIdsForTest.length],
                        image_path: a.image_path || `artifacts/${idx}.jpg`,
                    }));
                    (vesselService.find as jest.Mock).mockResolvedValue(vesselsList as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(true as never);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockResolvedValue(mockArtifacts as never),
                            limit: jest.fn().mockResolvedValue(mockArtifacts as never),
                        }),
                    });
                    const actualUtils: any = jest.requireActual('../../utils/functions');
                    (groupByImage as jest.Mock).mockImplementation(actualUtils.groupByImage);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/bulk?vesselIds=507f1f77bcf86cd799439011');
                    expect(res.status).toBe(401);
                });

                it('should return 400 when endTimestampISO is provided without startTimestampISO', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get(`/api/artifacts/bulk?vesselIds=507f1f77bcf86cd799439011&endTimestampISO=2024-09-21T08:41:43.736Z`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 200 with artifacts list', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const vesselIdsParam = vesselsList.slice(0, 2).map((v: any) => v._id.toString()).join(',');
                    const res = await request(app)
                        .get(`/api/artifacts/bulk?vesselIds=${vesselIdsParam}&startTimestampISO=2024-09-21T08:41:43.736Z`)
                        .set('Authorization', authToken)
                        .set('Referer', '/docs');
                    expect(res.status).toBe(200);
                });

                it('should return 500 if an error occurs while fetching artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            toArray: jest.fn().mockRejectedValue(new Error('Database error') as never),
                        })
                    });
                    const res = await request(app)
                        .get(`/api/artifacts/bulk?vesselIds=507f1f77bcf86cd799439011,507f1f77bcf86cd799439013&startTimestampISO=2024-09-21T08:41:43.736Z`)
                        .set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/detail/:id', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(artifactsList[0] as never),
                    });
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    (userHasPermissions as jest.Mock).mockReturnValue(true as never);
                    (processBatchItem as jest.Mock).mockReturnValue(true as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`);
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid id', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get(`/api/artifacts/detail/not-an-id`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOne: jest.fn().mockResolvedValue(null as never) });
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return artifact detail', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 403 when user does not have permissions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue({ _id: validId, onboard_vessel_id: null } as never),
                    });
                    (userHasPermissions as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(403);
                });

                it('should return 403 when user does not have vessel permissions', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (Vessel.findById as jest.Mock).mockResolvedValue(vesselsList[0] as never);
                    (canAccessVessel as jest.Mock).mockReturnValue(false as never);
                    (userHasPermissions as jest.Mock).mockReturnValue(false as never);
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(403);
                });

                it('should return 500 if an error occurs while fetching artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOne: jest.fn().mockRejectedValue(new Error('Database error') as never) });
                    const res = await request(app).get(`/api/artifacts/detail/${validId}`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/download', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue({
                            _id: validId,
                            unit_id: 'prototype-32',
                            // onboard_vessel_name: 'Vessel A',
                            bucket_name: 'b',
                            aws_region: 'us-east-1',
                            image_path: 'path/image.jpg',
                            timestamp: new Date().toISOString(),
                        } as never),
                    });
                    (validateError as any).mockImplementation((_err: any, res: any) => {
                        res.status(500).json({ message: 'Unexpected error' });
                    });
                    (getObjectStream as jest.Mock).mockReturnValue({
                        on: jest.fn((_event, callback: any) => {
                            if (callback) callback();
                            return { on: jest.fn() };
                        }),
                    });
                    (generateZip as jest.Mock).mockReturnValueOnce({
                        generateNodeStream: jest.fn().mockReturnValue({
                            on: jest.fn((event, callback: any) => {
                                if (event === 'error') {
                                    setTimeout(() => callback(new Error('Stream error')), 10);
                                }
                            }),
                            pipe: jest.fn().mockReturnValue({
                                on: jest.fn((event, callback: any) => {
                                    if (event === 'finish') {
                                        callback();
                                    }
                                }).mockReturnValue({
                                    on: jest.fn((event, callback: any) => {
                                        if (event === 'error') {
                                            setTimeout(() => callback(new Error('Pipe error')), 10);
                                        }
                                    }),
                                } as never),
                            }),
                        }),
                    });
                    (userHasPermissions as jest.Mock).mockReturnValue(true as never);
                    (processBatchItem as jest.Mock).mockReturnValue(true as never);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/download`);
                    expect(res.status).toBe(401);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({ findOne: jest.fn().mockResolvedValue(null as never) });
                    const res = await request(app).post(`/api/artifacts/${validId}/download`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 500 and stream zip', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post(`/api/artifacts/${validId}/download`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/ais-discrepancy', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = '66fa86dc64ecc5217496b976';
            const userId = userOrApiKey.authorized._id;

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/ais-discrepancy`);
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid artifact ID', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .post('/api/artifacts/invalid-id/ais-discrepancy')
                        .set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(null as never),
                    });
                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);
                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Artifact not found');
                });

                it('should flag artifact with AIS discrepancy when not currently flagged', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false
                        }
                    };
                    const mockUpdatedArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true,
                            ais_discrepancy_reporter: userId,
                            ais_discrepancy_timestamp: expect.any(Date)
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: mockUpdatedArtifact
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });

                it('should unflag artifact when currently flagged', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true
                        }
                    };
                    const mockUpdatedArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false,
                            ais_discrepancy_reporter: userId,
                            ais_discrepancy_timestamp: expect.any(Date)
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: mockUpdatedArtifact
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });

                it('should handle artifact with no portal object', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                    };
                    const mockUpdatedArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true,
                            ais_discrepancy_reporter: userId,
                            ais_discrepancy_timestamp: expect.any(Date)
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: mockUpdatedArtifact
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });

                it('should handle artifact with portal object but no ais_discrepancy field', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {}
                    };
                    const mockUpdatedArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true,
                            ais_discrepancy_reporter: userId,
                            ais_discrepancy_timestamp: expect.any(Date)
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: mockUpdatedArtifact
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });

                it('should return 500 if database error occurs during findOne', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockRejectedValue(new Error('Database connection error') as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Database connection error');
                });

                it('should return 500 if database error occurs during findOneAndUpdate', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Update failed') as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Update failed');
                });

                it('should handle multiple flag/unflag cycles correctly', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    let mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: { ...mockArtifact, portal: { ...mockArtifact.portal, ais_discrepancy: true } }
                        } as never),
                    });

                    const res1 = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res1.status).toBe(200);
                    expect(res1.body.message).toBe('AIS flag selection updated ');
                    expect(res1.body.state).toBe('both_not_matched');

                    mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: true
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: { ...mockArtifact, portal: { ...mockArtifact.portal, ais_discrepancy: false } }
                        } as never),
                    });

                    const res2 = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_matched' })
                        .set('Authorization', authToken);

                    expect(res2.status).toBe(200);
                    expect(res2.body.message).toBe('AIS flag selection updated ');
                    expect(res2.body.state).toBe('both_matched');
                });

                it('should preserve existing portal fields when updating', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockArtifact = {
                        _id: validId,
                        portal: {
                            ais_discrepancy: false,
                            other_field: 'existing_value',
                            another_field: 123
                        }
                    };

                    qmai.collection.mockReturnValue({
                        findOne: jest.fn().mockResolvedValue(mockArtifact as never),
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                ...mockArtifact,
                                portal: {
                                    ...mockArtifact.portal,
                                    ais_discrepancy: true,
                                    ais_discrepancy_reporter: userId,
                                    ais_discrepancy_timestamp: expect.any(Date)
                                }
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post(`/api/artifacts/${validId}/ais-discrepancy`)
                        .send({ selection: 'both_not_matched' })
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('AIS flag selection updated ');
                    expect(res.body.state).toBe('both_not_matched');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/archived', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (userHasPermissions as jest.Mock).mockReturnValue(true);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([
                                                {
                                                    _id: '507f1f77bcf86cd799439011',
                                                    bucket_name: 'test-bucket',
                                                    image_path: 'test-image.jpg',
                                                    video_path: 'test-video.mp4',
                                                    thumbnail_image_path: 'test-thumbnail.jpg',
                                                    aws_region: 'us-east-1'
                                                }
                                            ] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://test-url.com' });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/archived');
                    expect(res.status).toBe(401);
                });

                it('should return 400 if user lacks manageArtifacts permission', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (userHasPermissions as jest.Mock).mockReturnValue(false);

                    const res = await request(app)
                        .get('/api/artifacts/archived')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch archived artifacts with URLs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('artifacts');
                    expect(res.body).toHaveProperty('page', 1);
                    expect(res.body).toHaveProperty('pageSize', 10);
                    expect(res.body).toHaveProperty('totalCount', 1);
                    expect(res.body.artifacts[0]).toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([
                                                {
                                                    _id: '507f1f77bcf86cd799439011',
                                                    bucket_name: 'test-bucket',
                                                    image_path: 'test-image.jpg',
                                                    aws_region: 'us-east-1'
                                                }
                                            ] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0]).toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).not.toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only video_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([
                                                {
                                                    _id: '507f1f77bcf86cd799439011',
                                                    bucket_name: 'test-bucket',
                                                    video_path: 'test-video.mp4',
                                                    aws_region: 'us-east-1'
                                                }
                                            ] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0]).not.toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only thumbnail_image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockResolvedValue([
                                                {
                                                    _id: '507f1f77bcf86cd799439011',
                                                    bucket_name: 'test-bucket',
                                                    thumbnail_image_path: 'test-thumbnail.jpg',
                                                    aws_region: 'us-east-1'
                                                }
                                            ] as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifacts[0]).not.toHaveProperty('image_url');
                    expect(res.body.artifacts[0]).not.toHaveProperty('video_url');
                    expect(res.body.artifacts[0]).toHaveProperty('thumbnail_url');
                });

                it('should handle database errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        find: jest.fn().mockReturnValue({
                            sort: jest.fn().mockReturnValue({
                                project: jest.fn().mockReturnValue({
                                    skip: jest.fn().mockReturnValue({
                                        limit: jest.fn().mockReturnValue({
                                            toArray: jest.fn().mockRejectedValue(new Error('Database error') as never),
                                        }),
                                    }),
                                }),
                            }),
                        }),
                        countDocuments: jest.fn().mockResolvedValue(1 as never),
                    });

                    const res = await request(app)
                        .get('/api/artifacts/archived?page=1&pageSize=10')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Database error');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/archive', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (userHasPermissions as jest.Mock).mockReturnValue(true);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                image_path: 'test-image.jpg',
                                video_path: 'test-video.mp4',
                                thumbnail_image_path: 'test-thumbnail.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://test-url.com' });
                    (ioEmitter.emit as jest.Mock).mockImplementation(() => { });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/507f1f77bcf86cd799439011/archive');
                    expect(res.status).toBe(401);
                });

                it('should return 200 when user has manageArtifacts permission', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (userHasPermissions as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 and archive artifact with URLs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Artifact archived successfully');
                    expect(res.body.artifact).toHaveProperty('image_url');
                    expect(res.body.artifact).toHaveProperty('video_url');
                    expect(res.body.artifact).toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                image_path: 'test-image.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).toHaveProperty('image_url');
                    expect(res.body.artifact).not.toHaveProperty('video_url');
                    expect(res.body.artifact).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only video_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                video_path: 'test-video.mp4',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).not.toHaveProperty('image_url');
                    expect(res.body.artifact).toHaveProperty('video_url');
                    expect(res.body.artifact).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only thumbnail_image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                thumbnail_image_path: 'test-thumbnail.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).not.toHaveProperty('image_url');
                    expect(res.body.artifact).not.toHaveProperty('video_url');
                    expect(res.body.artifact).toHaveProperty('thumbnail_url');
                });

                it('should return 404 if artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: null
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Artifact not found');
                });

                it('should handle database errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Database error') as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/archive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });

    describe('POST /api/artifacts/:id/unarchive', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (userHasPermissions as jest.Mock).mockReturnValue(true);
                    (validateError as any).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                image_path: 'test-image.jpg',
                                video_path: 'test-video.mp4',
                                thumbnail_image_path: 'test-thumbnail.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });
                    (processBatchItem as jest.Mock).mockReturnValue({ signedUrl: 'https://test-url.com' });
                    (ioEmitter.emit as jest.Mock).mockImplementation(() => { });
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post('/api/artifacts/507f1f77bcf86cd799439011/unarchive');
                    expect(res.status).toBe(401);
                });

                it('should return 200 when user has manageArtifacts permission', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (userHasPermissions as jest.Mock).mockReturnValue(true);

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                });

                it('should return 200 and unarchive artifact with URLs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.message).toBe('Artifact unarchived successfully');
                    expect(res.body.artifact).toHaveProperty('image_url');
                    expect(res.body.artifact).toHaveProperty('video_url');
                    expect(res.body.artifact).toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                image_path: 'test-image.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).toHaveProperty('image_url');
                    expect(res.body.artifact).not.toHaveProperty('video_url');
                    expect(res.body.artifact).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only video_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                video_path: 'test-video.mp4',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).not.toHaveProperty('image_url');
                    expect(res.body.artifact).toHaveProperty('video_url');
                    expect(res.body.artifact).not.toHaveProperty('thumbnail_url');
                });

                it('should handle artifacts with only thumbnail_image_path', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: {
                                _id: '507f1f77bcf86cd799439011',
                                bucket_name: 'test-bucket',
                                thumbnail_image_path: 'test-thumbnail.jpg',
                                aws_region: 'us-east-1'
                            }
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body.artifact).not.toHaveProperty('image_url');
                    expect(res.body.artifact).not.toHaveProperty('video_url');
                    expect(res.body.artifact).toHaveProperty('thumbnail_url');
                });

                it('should return 404 if artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockResolvedValue({
                            value: null
                        } as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(404);
                    expect(res.body.message).toBe('Artifact not found');
                });

                it('should handle database errors', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    qmai.collection.mockReturnValue({
                        findOneAndUpdate: jest.fn().mockRejectedValue(new Error('Database error') as never),
                    });

                    const res = await request(app)
                        .post('/api/artifacts/507f1f77bcf86cd799439011/unarchive')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                    expect(res.body.message).toBe('Database error');
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });

    describe('GET /api/artifacts/evaluated', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                    (userHasPermissions as jest.Mock).mockReturnValue(true);
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/artifacts/evaluated').query({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user lacks manageArtifacts permission', async () => {
                    // Setup user without manageArtifacts permission (permission_id 1300)
                    const userWithoutPermission = {
                        ...userOrApiKey.authorized,
                        permissions: userOrApiKey.authorized.permissions.filter((p: any) => p.permission_id !== 1300),
                    };
                    // Mock User.findOne to return user without permission
                    (User.findOne as jest.Mock).mockResolvedValue(userWithoutPermission as never);
                    setupAuthorizedAuthMocks(authMethod, { authorized: userWithoutPermission, nonAuthorized: userOrApiKey.nonAuthorized }, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(403);
                });

                it('should return 400 when page is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ pageSize: 10 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when pageSize is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 1 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when page is less than 1', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 0, pageSize: 10 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when pageSize is greater than 100', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 101 });
                    expect(res.status).toBe(400);
                });

                it('should return 400 when evaluationTypes contains invalid type', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 10, evaluationTypes: 'invalid_type' });
                    expect(res.status).toBe(400);
                });

                it('should return evaluated artifacts successfully without filters', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = {
                        artifacts: [
                            {
                                _id: 'artifact1',
                                artifactId: 'artifact1',
                                artifact: { _id: 'artifact1', portal: { ais_discrepancy: 'both_matched' } },
                                flags: [],
                                flagCount: 0,
                            },
                        ],
                        totalCount: 1,
                        page: 1,
                        pageSize: 10,
                    };

                    (artifactFlagService.getEvaluatedArtifacts as jest.Mock).mockResolvedValue(mockResult);

                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(200);
                    expect(res.body).toHaveProperty('artifacts');
                    expect(res.body).toHaveProperty('totalCount');
                    expect(res.body).toHaveProperty('page');
                    expect(res.body).toHaveProperty('pageSize');
                    expect(artifactFlagService.getEvaluatedArtifacts).toHaveBeenCalledWith(1, 10, undefined);
                });

                it('should return evaluated artifacts with evaluationTypes filter', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = {
                        artifacts: [
                            {
                                _id: 'artifact1',
                                artifactId: 'artifact1',
                                artifact: { _id: 'artifact1', portal: { ais_discrepancy: 'both_matched' } },
                                flags: [],
                                flagCount: 0,
                            },
                        ],
                        totalCount: 1,
                        page: 1,
                        pageSize: 10,
                    };

                    (artifactFlagService.getEvaluatedArtifacts as jest.Mock).mockResolvedValue(mockResult);

                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 10, evaluationTypes: 'both_matched,detection_not_matched' });
                    expect(res.status).toBe(200);
                    expect(artifactFlagService.getEvaluatedArtifacts).toHaveBeenCalledWith(
                        1,
                        10,
                        ['both_matched', 'detection_not_matched']
                    );
                });

                it('should handle multiple evaluation types correctly', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = {
                        artifacts: [],
                        totalCount: 0,
                        page: 1,
                        pageSize: 10,
                    };

                    (artifactFlagService.getEvaluatedArtifacts as jest.Mock).mockResolvedValue(mockResult);

                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({
                            page: 1,
                            pageSize: 10,
                            evaluationTypes: 'both_matched,detection_not_matched,ais_not_matched,both_not_matched',
                        });
                    expect(res.status).toBe(200);
                    expect(artifactFlagService.getEvaluatedArtifacts).toHaveBeenCalledWith(
                        1,
                        10,
                        ['both_matched', 'detection_not_matched', 'ais_not_matched', 'both_not_matched']
                    );
                });

                it('should handle empty evaluationTypes string', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const mockResult = {
                        artifacts: [],
                        totalCount: 0,
                        page: 1,
                        pageSize: 10,
                    };

                    (artifactFlagService.getEvaluatedArtifacts as jest.Mock).mockResolvedValue(mockResult);

                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 10, evaluationTypes: '' });
                    expect(res.status).toBe(200);
                    // Empty string is falsy, so evaluationTypesParam ? ... : undefined evaluates to undefined
                    expect(artifactFlagService.getEvaluatedArtifacts).toHaveBeenCalledWith(1, 10, undefined);
                });

                it('should handle errors when fetching evaluated artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const errorMessage = 'Database connection failed';
                    (artifactFlagService.getEvaluatedArtifacts as jest.Mock).mockRejectedValue(new Error(errorMessage));
                    (validateError as jest.Mock).mockImplementation((err: any, res: any) => {
                        res.status(500).json({ message: err.message });
                    });

                    const res = await request(app)
                        .get('/api/artifacts/evaluated')
                        .set('Authorization', authToken)
                        .query({ page: 1, pageSize: 10 });
                    expect(res.status).toBe(500);
                    expect(res.body).toHaveProperty('message', errorMessage);
                    expect(artifactFlagService.getEvaluatedArtifacts).toHaveBeenCalledWith(1, 10, undefined);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
    });
});
