import axiosInstance from "../axios";

class ArtifactFlagController {
    constructor() {
        this.userFlaggedArtifactIds = new Set();
    }

    async flagArtifact(artifactId) {
        try {
            const response = await axiosInstance.post(`/artifacts/${artifactId}/flag`);
            return response.data;
        } catch (error) {
            console.error("Error flagging artifact:", error);
            throw error;
        }
    }

    async getFlaggedArtifacts(page, pageSize) {
        try {
            const response = await axiosInstance.get(`/artifacts/flagged`, {
                params: { page, pageSize },
            });
            return response.data || { artifacts: [], totalCount: 0 };
        } catch (error) {
            console.error("Error fetching flagged artifacts:", error);
            throw error;
        }
    }

    async unflagArtifact(artifactId) {
        try {
            const response = await axiosInstance.post(`/artifacts/${artifactId}/unflag`);
            return response.data;
        } catch (error) {
            console.error("Error unflagging artifact:", error);
            throw error;
        }
    }

    isArtifactFlaggedByUser(artifactId) {
        return this.userFlaggedArtifactIds.has(artifactId);
    }

    async getUserFlaggedArtifactIds() {
        try {
            const response = await axiosInstance.get(`/artifacts/flagged/user`);
            const flaggedIds = response.data.flaggedArtifactIds || [];
            this.userFlaggedArtifactIds = new Set(flaggedIds);
            return flaggedIds;
        } catch (error) {
            console.error("Error fetching user flagged artifact IDs:", error);
            throw error;
        }
    }

    async removeAllFlagsFromArtifact(artifactId) {
        try {
            const response = await axiosInstance.delete(`/artifacts/${artifactId}/flags`);
            return response.data;
        } catch (error) {
            console.error("Error removing all flags from artifact:", error);
            throw error;
        }
    }

    async getEvaluatedArtifacts(page, pageSize, evaluationTypes) {
        try {
            const params = { page, pageSize };
            if (evaluationTypes && evaluationTypes.length > 0) {
                params.evaluationTypes = evaluationTypes.join(",");
            }
            const response = await axiosInstance.get(`/artifacts/evaluated`, {
                params,
            });
            return response.data || { artifacts: [], totalCount: 0 };
        } catch (error) {
            console.error("Error fetching evaluated artifacts:", error);
            throw error;
        }
    }
}

const artifactFlagController = new ArtifactFlagController();
export default artifactFlagController;
