import mongoose from "mongoose";
import ArtifactFlag from "../models/ArtifactFlag";
import db from "../modules/db";
import { processBatchItem, s3Config } from "../modules/awsS3";
import { IArtifactFlag, IFlaggedArtifact } from "../interfaces/ArtifactFlag";

interface AggregationFlagData {
    _id: mongoose.Types.ObjectId;
    flags: Array<{
        _id: mongoose.Types.ObjectId;
        flaggedBy: mongoose.Types.ObjectId;
        flaggedByUser: {
            _id: mongoose.Types.ObjectId;
            name: string;
            email: string;
        };
        flaggedAt: Date;
    }>;
    flagCount: number;
    latestFlagDate: Date;
}

class ArtifactFlagService {
    async flagArtifact(artifactId: string, userId: string): Promise<IArtifactFlag> {
        const artifact = await db.qmai.collection("analysis_results").findOne({
            _id: new mongoose.Types.ObjectId(artifactId),
        });
        if (!artifact) {
            throw new Error("Artifact not found");
        }

        const existingFlag = await ArtifactFlag.findOne({
            artifactId: new mongoose.Types.ObjectId(artifactId),
            flaggedBy: new mongoose.Types.ObjectId(userId),
        });
        if (existingFlag) {
            throw new Error("You have already flagged this artifact");
        }

        const flag = new ArtifactFlag({
            artifactId: new mongoose.Types.ObjectId(artifactId),
            flaggedBy: new mongoose.Types.ObjectId(userId),
        });

        await flag.save();
        return flag;
    }

    async getFlaggedArtifacts(
        page: number,
        pageSize: number,
    ): Promise<{ artifacts: IFlaggedArtifact[]; totalCount: number; page: number; pageSize: number }> {
        const skip = (page - 1) * pageSize;

        const allFlaggedArtifactIds = await ArtifactFlag.distinct("artifactId");
        let totalCount = 0;
        let archivedArtifactIds: mongoose.Types.ObjectId[] = [];

        if (allFlaggedArtifactIds.length > 0) {
            const artifacts = await db.qmai
                .collection("analysis_results")
                .find({ _id: { $in: allFlaggedArtifactIds } }, { projection: { _id: 1, "portal.is_archived": 1 } })
                .toArray();

            archivedArtifactIds = artifacts.filter((a: any) => a.portal?.is_archived === true).map((a: any) => a._id);

            totalCount = artifacts.length - archivedArtifactIds.length;
        }

        const aggregationPipeline: any[] = [];

        if (archivedArtifactIds.length > 0) {
            aggregationPipeline.push({
                $match: {
                    artifactId: { $nin: archivedArtifactIds },
                },
            });
        }

        aggregationPipeline.push(
            {
                $lookup: {
                    from: "users",
                    let: { userId: "$flaggedBy" },
                    pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$userId"] } } }, { $project: { _id: 1, name: 1, email: 1 } }],
                    as: "flaggedByUser",
                },
            },
            {
                $group: {
                    _id: "$artifactId",
                    flags: {
                        $push: {
                            _id: "$_id",
                            flaggedBy: "$flaggedBy",
                            flaggedByUser: { $arrayElemAt: ["$flaggedByUser", 0] },
                            flaggedAt: "$flaggedAt",
                        },
                    },
                    flagCount: { $sum: 1 },
                    latestFlagDate: { $max: "$flaggedAt" },
                },
            },
            { $sort: { latestFlagDate: -1 } },
            { $skip: skip },
            { $limit: pageSize },
        );

        const flaggedArtifacts = await ArtifactFlag.aggregate(aggregationPipeline);

        const artifactIds = flaggedArtifacts.map((item: IArtifactFlag) => item._id);
        if (artifactIds.length === 0) {
            return { artifacts: [], totalCount, page, pageSize };
        }

        const artifacts = await db.qmai
            .collection("analysis_results")
            .find(
                { _id: { $in: artifactIds } },
                {
                    projection: {
                        _id: 1,
                        unit_id: 1,
                        bucket_name: 1,
                        image_path: 1,
                        video_path: 1,
                        thumbnail_image_path: 1,
                        location: 1,
                        category: 1,
                        super_category: 1,
                        size: 1,
                        color: 1,
                        weapons: 1,
                        others: 1,
                        timestamp: 1,
                        created_at: 1,
                        onboard_vessel_name: 1,
                        onboard_vessel_id: 1,
                        country_flag: 1,
                        aws_region: 1,
                        text_extraction: 1,
                        imo_number: 1,
                        portal: 1,
                        det_nbbox: 1,
                    },
                },
            )
            .toArray();

        const results: IFlaggedArtifact[] = [];
        for (const flagData of flaggedArtifacts) {
            const typedFlagData: AggregationFlagData = {
                _id: flagData._id,
                flags: flagData.flags,
                flagCount: flagData.flagCount,
                latestFlagDate: flagData.latestFlagDate,
            };
            const artifact = artifacts.find((a) => a._id.toString() === typedFlagData._id.toString());
            if (!artifact) continue;
            const artifactWithUrls: any = { ...artifact };
            if (artifact.image_path) {
                artifactWithUrls.image_url = processBatchItem({
                    bucketName: artifact.bucket_name,
                    key: artifact.image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            if (artifact.video_path) {
                artifactWithUrls.video_url = processBatchItem({
                    bucketName: artifact.bucket_name,
                    key: artifact.video_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            if (artifact.thumbnail_image_path) {
                const compressedBucketName = s3Config.buckets.compressedItems.name;
                if (!compressedBucketName) {
                    throw new Error("AWS_COMPRESSED_ITEMS_BUCKET environment variable is not set");
                }
                artifactWithUrls.thumbnail_url = processBatchItem({
                    bucketName: compressedBucketName,
                    key: artifact.thumbnail_image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            results.push({
                _id: typedFlagData._id,
                artifactId: artifact._id,
                artifact: artifactWithUrls,
                flags: typedFlagData.flags.map((flag) => ({
                    ...flag,
                    user: flag.flaggedByUser,
                    created_at: flag.flaggedAt,
                })),
                flagCount: typedFlagData.flagCount,
                latestFlagDate: typedFlagData.latestFlagDate,
            });
        }

        return {
            artifacts: results,
            totalCount,
            page,
            pageSize,
        };
    }

    async unflagArtifact(artifactId: string, userId: string): Promise<IArtifactFlag | null> {
        const flag = await ArtifactFlag.findOneAndDelete({
            artifactId: new mongoose.Types.ObjectId(artifactId),
            flaggedBy: new mongoose.Types.ObjectId(userId),
        });
        if (!flag) {
            throw new Error("Flag not found");
        }
        return flag;
    }

    async getUserFlaggedArtifactIds(userId: string): Promise<string[]> {
        const flags = await ArtifactFlag.find({ flaggedBy: new mongoose.Types.ObjectId(userId) }, { artifactId: 1, _id: 0 });
        return flags.map((flag: IArtifactFlag) => flag.artifactId.toString());
    }

    async removeAllFlagsFromArtifact(artifactId: string): Promise<{ deletedCount: number }> {
        const result = await ArtifactFlag.deleteMany({
            artifactId: new mongoose.Types.ObjectId(artifactId),
        });
        return result;
    }

    async getEvaluatedArtifacts(
        page: number,
        pageSize: number,
        evaluationTypes?: string[],
    ): Promise<{ artifacts: IFlaggedArtifact[]; totalCount: number; page: number; pageSize: number }> {
        const skip = (page - 1) * pageSize;

        const query: any = {
            "portal.ais_discrepancy": { $exists: true, $ne: null },
            "portal.is_archived": { $ne: true },
        };

        if (evaluationTypes && evaluationTypes.length > 0) {
            query["portal.ais_discrepancy"] = { $in: evaluationTypes };
        }

        const totalCount = await db.qmai.collection("analysis_results").countDocuments(query);

        const artifacts = await db.qmai
            .collection("analysis_results")
            .find(query, {
                projection: {
                    _id: 1,
                    unit_id: 1,
                    bucket_name: 1,
                    image_path: 1,
                    video_path: 1,
                    thumbnail_image_path: 1,
                    location: 1,
                    category: 1,
                    super_category: 1,
                    size: 1,
                    color: 1,
                    weapons: 1,
                    others: 1,
                    timestamp: 1,
                    created_at: 1,
                    onboard_vessel_name: 1,
                    onboard_vessel_id: 1,
                    country_flag: 1,
                    aws_region: 1,
                    text_extraction: 1,
                    imo_number: 1,
                    portal: 1,
                    det_nbbox: 1,
                },
            })
            .sort({ "portal.ais_discrepancy_timestamp": -1 })
            .skip(skip)
            .limit(pageSize)
            .toArray();

        const results: IFlaggedArtifact[] = [];
        for (const artifact of artifacts) {
            const artifactWithUrls: any = { ...artifact };
            if (artifact.image_path) {
                artifactWithUrls.image_url = processBatchItem({
                    bucketName: artifact.bucket_name,
                    key: artifact.image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            if (artifact.video_path) {
                artifactWithUrls.video_url = processBatchItem({
                    bucketName: artifact.bucket_name,
                    key: artifact.video_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }
            if (artifact.thumbnail_image_path) {
                const compressedBucketName = s3Config.buckets.compressedItems.name;
                if (!compressedBucketName) {
                    throw new Error("AWS_COMPRESSED_ITEMS_BUCKET environment variable is not set");
                }
                artifactWithUrls.thumbnail_url = processBatchItem({
                    bucketName: compressedBucketName,
                    key: artifact.thumbnail_image_path,
                    region: artifact.aws_region,
                }).signedUrl;
            }

            // Get flag info for evaluated artifacts (they may also be flagged)
            const flags = await ArtifactFlag.find({ artifactId: artifact._id }).populate("flaggedBy", "name email").sort({ flaggedAt: -1 }).lean();

            results.push({
                _id: artifact._id,
                artifactId: artifact._id,
                artifact: artifactWithUrls,
                flags: flags.map((flag: any) => ({
                    _id: flag._id,
                    flaggedBy: flag.flaggedBy._id,
                    flaggedByUser: {
                        _id: flag.flaggedBy._id,
                        name: flag.flaggedBy.name,
                        email: flag.flaggedBy.email,
                    },
                    flaggedAt: flag.flaggedAt,
                    user: {
                        _id: flag.flaggedBy._id,
                        name: flag.flaggedBy.name,
                        email: flag.flaggedBy.email,
                    },
                    created_at: flag.flaggedAt,
                })),
                flagCount: flags.length,
                latestFlagDate: flags.length > 0 ? flags[0].flaggedAt : artifact.portal?.ais_discrepancy_timestamp || new Date(),
            });
        }

        return {
            artifacts: results,
            totalCount,
            page,
            pageSize,
        };
    }
}

export default new ArtifactFlagService();
