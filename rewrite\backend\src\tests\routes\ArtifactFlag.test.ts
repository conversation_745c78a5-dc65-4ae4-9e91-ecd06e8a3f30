import User from '../../models/User';
import Api<PERSON>ey from '../../models/ApiKey';
import request from 'supertest';
import app from '../../server';
import { jest, describe, it, beforeEach, expect } from '@jest/globals';
import mongoose from 'mongoose';
import { generateUserToken, authorizedUser, nonAuthorizedUser } from '../data/Auth';
import { setupAuthorizedAuthMocks } from '../mocks/auth.mock';
import { AuthRunTestsFunction } from '../type';
import flagService from '../../services/ArtifactFlag.service';

process.env.JWT_SECRET = 'test-jwt-secret';
process.env.NODE_ENV = 'test';

jest.mock('../../modules/db', () => require('../mocks/modules/db.mock'));
jest.mock('../../models/User', () => require('../mocks/models/user.mock'));
jest.mock('../../models/ApiKey', () => require('../mocks/models/apiKey.mock'));
jest.mock('../../services/ArtifactFlag.service', () => require('../mocks/services/artifactFlag.mock'));

describe('Artifact Flag API', () => {

    describe('POST /api/artifacts/:id/flag', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const _nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = new mongoose.Types.ObjectId().toString();

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/flag`);
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid id', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).post(`/api/artifacts/not-an-id/flag`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 404 when artifact not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.flagArtifact as jest.Mock).mockRejectedValueOnce(new Error('Artifact not found') as never);
                    const res = await request(app).post(`/api/artifacts/${validId}/flag`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should return 400 when already flagged', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.flagArtifact as jest.Mock).mockRejectedValueOnce(new Error('You have already flagged this artifact') as never);
                    const res = await request(app).post(`/api/artifacts/${validId}/flag`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should flag artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.flagArtifact as jest.Mock).mockResolvedValueOnce({ _id: new mongoose.Types.ObjectId() } as never);
                    const res = await request(app).post(`/api/artifacts/${validId}/flag`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 when error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.flagArtifact as jest.Mock).mockRejectedValueOnce(new Error('Error flagging artifact') as never);
                    const res = await request(app).post(`/api/artifacts/${validId}/flag`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('POST /api/artifacts/:id/unflag', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = new mongoose.Types.ObjectId().toString();

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).post(`/api/artifacts/${validId}/unflag`);
                    expect(res.status).toBe(401);
                });

                it('should return 404 when flag not found', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.unflagArtifact as jest.Mock).mockRejectedValueOnce(new Error('Flag not found') as never);
                    const res = await request(app).post(`/api/artifacts/${validId}/unflag`).set('Authorization', authToken);
                    expect(res.status).toBe(404);
                });

                it('should unflag artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.unflagArtifact as jest.Mock).mockResolvedValueOnce({ acknowledged: true } as never);
                    const res = await request(app).post(`/api/artifacts/${validId}/unflag`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 when error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.unflagArtifact as jest.Mock).mockRejectedValueOnce(new Error('Error unflagging artifact') as never);
                    const res = await request(app).post(`/api/artifacts/${validId}/unflag`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/flagged', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get(`/api/artifacts/flagged`);
                    expect(res.status).toBe(401);
                });

                it('should return 400 if page query parameter is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get(`/api/artifacts/flagged`).query({ pageSize: 10 }).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return 400 if pageSize query parameter is missing', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).get(`/api/artifacts/flagged`).query({ page: 1 }).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should return list of flagged artifacts', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.getFlaggedArtifacts as jest.Mock).mockResolvedValueOnce({
                        artifacts: [],
                        totalCount: 0,
                        page: 1,
                        pageSize: 10
                    } as never);
                    const res = await request(app).get(`/api/artifacts/flagged`).query({ page: 1, pageSize: 10 }).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 when error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.getFlaggedArtifacts as jest.Mock).mockRejectedValueOnce(new Error('Error getting flagged artifacts') as never);
                    const res = await request(app).get(`/api/artifacts/flagged`).query({ page: 1, pageSize: 10 }).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('GET /api/artifacts/flagged/user', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get(`/api/artifacts/flagged/user`);
                    expect(res.status).toBe(401);
                });

                it('should return list of user flagged artifact ids', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.getUserFlaggedArtifactIds as jest.Mock).mockResolvedValueOnce([] as never);
                    const res = await request(app).get(`/api/artifacts/flagged/user`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 when error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.getUserFlaggedArtifactIds as jest.Mock).mockRejectedValueOnce(new Error('Error getting user flagged artifact ids') as never);
                    const res = await request(app).get(`/api/artifacts/flagged/user`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });

    describe('DELETE /api/artifacts/:id/flags', () => {
        const runTests: AuthRunTestsFunction = (authMethod, generateToken, userOrApiKey) => {
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);
            const validId = new mongoose.Types.ObjectId().toString();

            describe(`${authMethod} authentication`, () => {
                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).delete(`/api/artifacts/${validId}/flags`);
                    expect(res.status).toBe(401);
                });

                it('should return 400 for invalid id', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    const res = await request(app).delete(`/api/artifacts/not-an-id/flags`).set('Authorization', authToken);
                    expect(res.status).toBe(400);
                });

                it('should remove all flags from artifact', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.removeAllFlagsFromArtifact as jest.Mock).mockResolvedValueOnce({ deletedCount: 1 } as never);
                    const res = await request(app).delete(`/api/artifacts/${validId}/flags`).set('Authorization', authToken);
                    expect(res.status).toBe(200);
                });

                it('should return 500 when error occurs', async () => {
                    setupAuthorizedAuthMocks(authMethod, userOrApiKey, authToken, User, ApiKey);
                    (flagService.removeAllFlagsFromArtifact as jest.Mock).mockRejectedValueOnce(new Error('Error removing all flags from artifact') as never);
                    const res = await request(app).delete(`/api/artifacts/${validId}/flags`).set('Authorization', authToken);
                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser });
        // runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey });
    });
});


