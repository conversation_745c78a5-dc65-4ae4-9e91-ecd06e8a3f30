import mongoose from "mongoose";
import db from "../modules/db";
import ioEmitter from "../modules/ioEmitter";
import { IAisVesselFlag } from "src/interfaces/AisVesselFlag";

const aisVesselFlaggedSchema = new mongoose.Schema({
    mmsi: {
        type: String,
        required: true,
        index: true,
    },
    flaggedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: "User",
        required: true,
    },
    flaggedAt: {
        type: Date,
        required: true,
        default: () => new Date().toISOString(),
    },
});

aisVesselFlaggedSchema.index({ mmsi: 1, flaggedBy: 1 });
aisVesselFlaggedSchema.index({ flaggedAt: -1 });

aisVesselFlaggedSchema.post("save", (flag) => {
    ioEmitter.emit("notifyAll", { name: `ais_vessels_flagged/changed`, data: flag.toObject() });
});
aisVesselFlaggedSchema.post("findOneAndDelete", (flag) => {
    if (flag?.deletedCount > 0) return ioEmitter.emit("notifyAll", { name: `ais_vessels_flagged/changed` });
    ioEmitter.emit("notifyAll", { name: `ais_vessels_flagged/changed`, data: flag.toObject() });
});
aisVesselFlaggedSchema.post("deleteMany", (flag) => {
    if (flag?.deletedCount > 0) return ioEmitter.emit("notifyAll", { name: `ais_vessels_flagged/changed` });
    ioEmitter.emit("notifyAll", { name: `ais_vessels_flagged/changed`, data: flag.toObject() });
});

const AisVesselFlagged = db.qm.model<IAisVesselFlag>("AisVesselFlagged", aisVesselFlaggedSchema, "ais_vessels_flagged");

export default AisVesselFlagged;
