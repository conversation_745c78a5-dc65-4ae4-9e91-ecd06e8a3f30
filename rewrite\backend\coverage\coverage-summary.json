{"total": {"lines": {"total": 6044, "covered": 1384, "skipped": 0, "pct": 22.89}, "statements": {"total": 6570, "covered": 1411, "skipped": 0, "pct": 21.47}, "functions": {"total": 1076, "covered": 18, "skipped": 0, "pct": 1.67}, "branches": {"total": 2157, "covered": 27, "skipped": 0, "pct": 1.25}, "branchesTrue": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\server.ts": {"lines": {"total": 97, "covered": 47, "skipped": 0, "pct": 48.45}, "functions": {"total": 20, "covered": 1, "skipped": 0, "pct": 5}, "statements": {"total": 109, "covered": 49, "skipped": 0, "pct": 44.95}, "branches": {"total": 9, "covered": 2, "skipped": 0, "pct": 22.22}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\assignEndpointId.ts": {"lines": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\auth.ts": {"lines": {"total": 38, "covered": 33, "skipped": 0, "pct": 86.84}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 49, "covered": 34, "skipped": 0, "pct": 69.38}, "branches": {"total": 19, "covered": 6, "skipped": 0, "pct": 31.57}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\hasPermission.ts": {"lines": {"total": 9, "covered": 2, "skipped": 0, "pct": 22.22}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 11, "covered": 2, "skipped": 0, "pct": 18.18}, "branches": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\multerConfig.ts": {"lines": {"total": 18, "covered": 6, "skipped": 0, "pct": 33.33}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 19, "covered": 7, "skipped": 0, "pct": 36.84}, "branches": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\restrictEndpointByUser.ts": {"lines": {"total": 9, "covered": 3, "skipped": 0, "pct": 33.33}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 9, "covered": 3, "skipped": 0, "pct": 33.33}, "branches": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\middlewares\\validator.ts": {"lines": {"total": 9, "covered": 3, "skipped": 0, "pct": 33.33}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 10, "covered": 3, "skipped": 0, "pct": 30}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\AisVesselFlag.ts": {"lines": {"total": 17, "covered": 11, "skipped": 0, "pct": 64.7}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 19, "covered": 11, "skipped": 0, "pct": 57.89}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ApiKey.ts": {"lines": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ArtifactFavourites.ts": {"lines": {"total": 14, "covered": 10, "skipped": 0, "pct": 71.42}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 16, "covered": 10, "skipped": 0, "pct": 62.5}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ArtifactFlag.ts": {"lines": {"total": 17, "covered": 11, "skipped": 0, "pct": 64.7}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 19, "covered": 11, "skipped": 0, "pct": 57.89}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ArtifactSuggestion.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ArtifactSynonym.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\EmailDomains.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Geolocation.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\InAppNotification.ts": {"lines": {"total": 7, "covered": 5, "skipped": 0, "pct": 71.42}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 7, "covered": 5, "skipped": 0, "pct": 71.42}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\InviteToken.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\List.ts": {"lines": {"total": 15, "covered": 11, "skipped": 0, "pct": 73.33}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 16, "covered": 11, "skipped": 0, "pct": 68.75}, "branches": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ListACL.ts": {"lines": {"total": 8, "covered": 6, "skipped": 0, "pct": 75}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 8, "covered": 6, "skipped": 0, "pct": 75}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\ListArtifact.ts": {"lines": {"total": 8, "covered": 7, "skipped": 0, "pct": 87.5}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 8, "covered": 7, "skipped": 0, "pct": 87.5}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\NotificationAlert.ts": {"lines": {"total": 10, "covered": 6, "skipped": 0, "pct": 60}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 12, "covered": 6, "skipped": 0, "pct": 50}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\NotificationSummary.ts": {"lines": {"total": 9, "covered": 6, "skipped": 0, "pct": 66.66}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 10, "covered": 6, "skipped": 0, "pct": 60}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Organization.ts": {"lines": {"total": 7, "covered": 6, "skipped": 0, "pct": 85.71}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 7, "covered": 6, "skipped": 0, "pct": 85.71}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Permission.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Region.ts": {"lines": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 6, "covered": 6, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\RegionGroup.ts": {"lines": {"total": 12, "covered": 8, "skipped": 0, "pct": 66.66}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 12, "covered": 8, "skipped": 0, "pct": 66.66}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Role.ts": {"lines": {"total": 22, "covered": 10, "skipped": 0, "pct": 45.45}, "functions": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 24, "covered": 10, "skipped": 0, "pct": 41.66}, "branches": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\SessionLog.ts": {"lines": {"total": 12, "covered": 8, "skipped": 0, "pct": 66.66}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 12, "covered": 8, "skipped": 0, "pct": 66.66}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Statistics.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\TourGuide.ts": {"lines": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\User.ts": {"lines": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\UserCompletionLogs.ts": {"lines": {"total": 8, "covered": 6, "skipped": 0, "pct": 75}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 8, "covered": 6, "skipped": 0, "pct": 75}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\Vessel.ts": {"lines": {"total": 34, "covered": 14, "skipped": 0, "pct": 41.17}, "functions": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 34, "covered": 14, "skipped": 0, "pct": 41.17}, "branches": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\VesselAlert.ts": {"lines": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\VesselAlertBroadcast.ts": {"lines": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\models\\VesselOnlineLookup.ts": {"lines": {"total": 7, "covered": 6, "skipped": 0, "pct": 85.71}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 7, "covered": 6, "skipped": 0, "pct": 85.71}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\awsKinesis.ts": {"lines": {"total": 289, "covered": 20, "skipped": 0, "pct": 6.92}, "functions": {"total": 22, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 291, "covered": 20, "skipped": 0, "pct": 6.87}, "branches": {"total": 120, "covered": 2, "skipped": 0, "pct": 1.66}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\awsS3.ts": {"lines": {"total": 224, "covered": 39, "skipped": 0, "pct": 17.41}, "functions": {"total": 17, "covered": 2, "skipped": 0, "pct": 11.76}, "statements": {"total": 228, "covered": 40, "skipped": 0, "pct": 17.54}, "branches": {"total": 101, "covered": 5, "skipped": 0, "pct": 4.95}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\db.ts": {"lines": {"total": 29, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 47, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\email.ts": {"lines": {"total": 13, "covered": 5, "skipped": 0, "pct": 38.46}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 13, "covered": 5, "skipped": 0, "pct": 38.46}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\geolocation.ts": {"lines": {"total": 23, "covered": 4, "skipped": 0, "pct": 17.39}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 27, "covered": 6, "skipped": 0, "pct": 22.22}, "branches": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\ioEmitter.ts": {"lines": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\microservice_socket.ts": {"lines": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\notifyLog.ts": {"lines": {"total": 13, "covered": 7, "skipped": 0, "pct": 53.84}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 19, "covered": 8, "skipped": 0, "pct": 42.1}, "branches": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\openai.ts": {"lines": {"total": 5, "covered": 3, "skipped": 0, "pct": 60}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 5, "covered": 3, "skipped": 0, "pct": 60}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\otpService.ts": {"lines": {"total": 35, "covered": 13, "skipped": 0, "pct": 37.14}, "functions": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 43, "covered": 19, "skipped": 0, "pct": 44.18}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\pLimit.ts": {"lines": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "branches": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\processLogs.ts": {"lines": {"total": 15, "covered": 5, "skipped": 0, "pct": 33.33}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 16, "covered": 5, "skipped": 0, "pct": 31.25}, "branches": {"total": 5, "covered": 2, "skipped": 0, "pct": 40}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\sharedRedis.ts": {"lines": {"total": 17, "covered": 4, "skipped": 0, "pct": 23.52}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 20, "covered": 6, "skipped": 0, "pct": 30}, "branches": {"total": 7, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\spellingCorrector.ts": {"lines": {"total": 27, "covered": 15, "skipped": 0, "pct": 55.55}, "functions": {"total": 3, "covered": 2, "skipped": 0, "pct": 66.66}, "statements": {"total": 27, "covered": 15, "skipped": 0, "pct": 55.55}, "branches": {"total": 10, "covered": 4, "skipped": 0, "pct": 40}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\modules\\swagger.ts": {"lines": {"total": 14, "covered": 9, "skipped": 0, "pct": 64.28}, "functions": {"total": 2, "covered": 1, "skipped": 0, "pct": 50}, "statements": {"total": 16, "covered": 11, "skipped": 0, "pct": 68.75}, "branches": {"total": 6, "covered": 1, "skipped": 0, "pct": 16.66}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\queries\\User.ts": {"lines": {"total": 8, "covered": 8, "skipped": 0, "pct": 100}, "functions": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "statements": {"total": 9, "covered": 8, "skipped": 0, "pct": 88.88}, "branches": {"total": 4, "covered": 1, "skipped": 0, "pct": 25}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ApiEndpoint.route.ts": {"lines": {"total": 15, "covered": 12, "skipped": 0, "pct": 80}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 15, "covered": 12, "skipped": 0, "pct": 80}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ApiKey.route.ts": {"lines": {"total": 79, "covered": 21, "skipped": 0, "pct": 26.58}, "functions": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 84, "covered": 21, "skipped": 0, "pct": 25}, "branches": {"total": 5, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Artifact.route.ts": {"lines": {"total": 473, "covered": 49, "skipped": 0, "pct": 10.35}, "functions": {"total": 95, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 514, "covered": 49, "skipped": 0, "pct": 9.53}, "branches": {"total": 169, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ArtifactCompletions.route.ts": {"lines": {"total": 142, "covered": 38, "skipped": 0, "pct": 26.76}, "functions": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 149, "covered": 38, "skipped": 0, "pct": 25.5}, "branches": {"total": 84, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ArtifactFavourites.route.ts": {"lines": {"total": 20, "covered": 16, "skipped": 0, "pct": 80}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 20, "covered": 16, "skipped": 0, "pct": 80}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\ArtifactSuggestions.route.ts": {"lines": {"total": 95, "covered": 19, "skipped": 0, "pct": 20}, "functions": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 107, "covered": 19, "skipped": 0, "pct": 17.75}, "branches": {"total": 27, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Audio.route.ts": {"lines": {"total": 68, "covered": 19, "skipped": 0, "pct": 27.94}, "functions": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 73, "covered": 19, "skipped": 0, "pct": 26.02}, "branches": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\EmailDomains.route.ts": {"lines": {"total": 16, "covered": 12, "skipped": 0, "pct": 75}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 16, "covered": 12, "skipped": 0, "pct": 75}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Geolocation.route.ts": {"lines": {"total": 38, "covered": 16, "skipped": 0, "pct": 42.1}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 38, "covered": 16, "skipped": 0, "pct": 42.1}, "branches": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\InAppNotification.route.ts": {"lines": {"total": 52, "covered": 18, "skipped": 0, "pct": 34.61}, "functions": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 55, "covered": 18, "skipped": 0, "pct": 32.72}, "branches": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Kinesis.route.ts": {"lines": {"total": 193, "covered": 31, "skipped": 0, "pct": 16.06}, "functions": {"total": 40, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 204, "covered": 31, "skipped": 0, "pct": 15.19}, "branches": {"total": 134, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Lists.route.ts": {"lines": {"total": 173, "covered": 31, "skipped": 0, "pct": 17.91}, "functions": {"total": 24, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 183, "covered": 31, "skipped": 0, "pct": 16.93}, "branches": {"total": 56, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Log.route.ts": {"lines": {"total": 75, "covered": 20, "skipped": 0, "pct": 26.66}, "functions": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 76, "covered": 20, "skipped": 0, "pct": 26.31}, "branches": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\NotificationAlert.route.ts": {"lines": {"total": 233, "covered": 36, "skipped": 0, "pct": 15.45}, "functions": {"total": 50, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 242, "covered": 36, "skipped": 0, "pct": 14.87}, "branches": {"total": 96, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\NotificationSummary.route.ts": {"lines": {"total": 184, "covered": 27, "skipped": 0, "pct": 14.67}, "functions": {"total": 39, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 198, "covered": 27, "skipped": 0, "pct": 13.63}, "branches": {"total": 80, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Organization.route.ts": {"lines": {"total": 71, "covered": 23, "skipped": 0, "pct": 32.39}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 76, "covered": 23, "skipped": 0, "pct": 30.26}, "branches": {"total": 19, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Permission.route.ts": {"lines": {"total": 16, "covered": 12, "skipped": 0, "pct": 75}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 16, "covered": 12, "skipped": 0, "pct": 75}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Region.route.ts": {"lines": {"total": 16, "covered": 12, "skipped": 0, "pct": 75}, "functions": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 16, "covered": 12, "skipped": 0, "pct": 75}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\RegionGroup.route.ts": {"lines": {"total": 50, "covered": 21, "skipped": 0, "pct": 42}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 52, "covered": 21, "skipped": 0, "pct": 40.38}, "branches": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Role.route.ts": {"lines": {"total": 133, "covered": 25, "skipped": 0, "pct": 18.79}, "functions": {"total": 46, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 161, "covered": 25, "skipped": 0, "pct": 15.52}, "branches": {"total": 31, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\S3.route.ts": {"lines": {"total": 65, "covered": 18, "skipped": 0, "pct": 27.69}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 65, "covered": 18, "skipped": 0, "pct": 27.69}, "branches": {"total": 19, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Statistics.route.ts": {"lines": {"total": 51, "covered": 17, "skipped": 0, "pct": 33.33}, "functions": {"total": 6, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 53, "covered": 18, "skipped": 0, "pct": 33.96}, "branches": {"total": 23, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\TourGuide.route.ts": {"lines": {"total": 52, "covered": 16, "skipped": 0, "pct": 30.76}, "functions": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 52, "covered": 16, "skipped": 0, "pct": 30.76}, "branches": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\User.route.ts": {"lines": {"total": 413, "covered": 45, "skipped": 0, "pct": 10.89}, "functions": {"total": 58, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 455, "covered": 45, "skipped": 0, "pct": 9.89}, "branches": {"total": 136, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\VesselAis.route.ts": {"lines": {"total": 106, "covered": 28, "skipped": 0, "pct": 26.41}, "functions": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 112, "covered": 28, "skipped": 0, "pct": 25}, "branches": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\VesselAlert.route.ts": {"lines": {"total": 30, "covered": 27, "skipped": 0, "pct": 90}, "functions": {"total": 4, "covered": 4, "skipped": 0, "pct": 100}, "statements": {"total": 30, "covered": 27, "skipped": 0, "pct": 90}, "branches": {"total": 2, "covered": 1, "skipped": 0, "pct": 50}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\VesselLocation.route.ts": {"lines": {"total": 157, "covered": 23, "skipped": 0, "pct": 14.64}, "functions": {"total": 37, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 173, "covered": 23, "skipped": 0, "pct": 13.29}, "branches": {"total": 36, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\VesselManagement.route.ts": {"lines": {"total": 104, "covered": 26, "skipped": 0, "pct": 25}, "functions": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 109, "covered": 26, "skipped": 0, "pct": 23.85}, "branches": {"total": 44, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\Vessels.route.ts": {"lines": {"total": 59, "covered": 20, "skipped": 0, "pct": 33.89}, "functions": {"total": 18, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 68, "covered": 20, "skipped": 0, "pct": 29.41}, "branches": {"total": 33, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\index.ts": {"lines": {"total": 61, "covered": 61, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 61, "covered": 61, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\Artifact.route.ts": {"lines": {"total": 67, "covered": 19, "skipped": 0, "pct": 28.35}, "functions": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 72, "covered": 19, "skipped": 0, "pct": 26.38}, "branches": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\Kinesis.route.ts": {"lines": {"total": 47, "covered": 19, "skipped": 0, "pct": 40.42}, "functions": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 49, "covered": 19, "skipped": 0, "pct": 38.77}, "branches": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\NotificationSummary.route.ts": {"lines": {"total": 34, "covered": 11, "skipped": 0, "pct": 32.35}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 34, "covered": 11, "skipped": 0, "pct": 32.35}, "branches": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\User.route.ts": {"lines": {"total": 50, "covered": 15, "skipped": 0, "pct": 30}, "functions": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 52, "covered": 15, "skipped": 0, "pct": 28.84}, "branches": {"total": 28, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\VesselLocation.route.ts": {"lines": {"total": 76, "covered": 20, "skipped": 0, "pct": 26.31}, "functions": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 82, "covered": 20, "skipped": 0, "pct": 24.39}, "branches": {"total": 24, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\routes\\v2\\index.v2.ts": {"lines": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 13, "covered": 13, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\AisVesselFlag.service.ts": {"lines": {"total": 45, "covered": 4, "skipped": 0, "pct": 8.88}, "functions": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 47, "covered": 4, "skipped": 0, "pct": 8.51}, "branches": {"total": 10, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\ApiKey.service.ts": {"lines": {"total": 41, "covered": 6, "skipped": 0, "pct": 14.63}, "functions": {"total": 11, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 55, "covered": 6, "skipped": 0, "pct": 10.9}, "branches": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\Artifact.service.ts": {"lines": {"total": 12, "covered": 4, "skipped": 0, "pct": 33.33}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 14, "covered": 4, "skipped": 0, "pct": 28.57}, "branches": {"total": 3, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\ArtifactFavourites.service.ts": {"lines": {"total": 73, "covered": 9, "skipped": 0, "pct": 12.32}, "functions": {"total": 14, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 85, "covered": 12, "skipped": 0, "pct": 14.11}, "branches": {"total": 21, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\ArtifactFlag.service.ts": {"lines": {"total": 91, "covered": 6, "skipped": 0, "pct": 6.59}, "functions": {"total": 17, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 98, "covered": 6, "skipped": 0, "pct": 6.12}, "branches": {"total": 26, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\List.service.ts": {"lines": {"total": 241, "covered": 12, "skipped": 0, "pct": 4.97}, "functions": {"total": 53, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 310, "covered": 12, "skipped": 0, "pct": 3.87}, "branches": {"total": 90, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\RegionGroup.service.ts": {"lines": {"total": 52, "covered": 8, "skipped": 0, "pct": 15.38}, "functions": {"total": 15, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 63, "covered": 8, "skipped": 0, "pct": 12.69}, "branches": {"total": 16, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\Stream.service.ts": {"lines": {"total": 45, "covered": 11, "skipped": 0, "pct": 24.44}, "functions": {"total": 15, "covered": 1, "skipped": 0, "pct": 6.66}, "statements": {"total": 54, "covered": 12, "skipped": 0, "pct": 22.22}, "branches": {"total": 25, "covered": 1, "skipped": 0, "pct": 4}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\Vessel.service.ts": {"lines": {"total": 165, "covered": 13, "skipped": 0, "pct": 7.87}, "functions": {"total": 19, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 173, "covered": 13, "skipped": 0, "pct": 7.51}, "branches": {"total": 106, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\VesselAlert.service.ts": {"lines": {"total": 22, "covered": 5, "skipped": 0, "pct": 22.72}, "functions": {"total": 4, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 23, "covered": 5, "skipped": 0, "pct": 21.73}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\VesselLocation.service.ts": {"lines": {"total": 81, "covered": 5, "skipped": 0, "pct": 6.17}, "functions": {"total": 13, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 83, "covered": 5, "skipped": 0, "pct": 6.02}, "branches": {"total": 25, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\services\\VesselOnlineLookup.service.ts": {"lines": {"total": 9, "covered": 4, "skipped": 0, "pct": 44.44}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 10, "covered": 4, "skipped": 0, "pct": 40}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\Email.ts": {"lines": {"total": 30, "covered": 20, "skipped": 0, "pct": 66.66}, "functions": {"total": 9, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 30, "covered": 20, "skipped": 0, "pct": 66.66}, "branches": {"total": 1, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\endpointIds.ts": {"lines": {"total": 3, "covered": 3, "skipped": 0, "pct": 100}, "functions": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "statements": {"total": 5, "covered": 5, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\functions.ts": {"lines": {"total": 278, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 52, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 321, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 172, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\isoCountryCodes.ts": {"lines": {"total": 13, "covered": 4, "skipped": 0, "pct": 30.76}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 13, "covered": 4, "skipped": 0, "pct": 30.76}, "branches": {"total": 8, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\permissions.ts": {"lines": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 2, "covered": 2, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\roles.ts": {"lines": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\seaPorts.ts": {"lines": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\staticMap.ts": {"lines": {"total": 110, "covered": 9, "skipped": 0, "pct": 8.18}, "functions": {"total": 12, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 114, "covered": 9, "skipped": 0, "pct": 7.89}, "branches": {"total": 71, "covered": 0, "skipped": 0, "pct": 0}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\timezonesList.ts": {"lines": {"total": 5, "covered": 4, "skipped": 0, "pct": 80}, "functions": {"total": 2, "covered": 0, "skipped": 0, "pct": 0}, "statements": {"total": 8, "covered": 6, "skipped": 0, "pct": 75}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}, "D:\\Techlio\\Quartermaster\\quartermaster-webapp\\rewrite\\backend\\src\\utils\\userEndpointRestrictions.ts": {"lines": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "functions": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}, "statements": {"total": 1, "covered": 1, "skipped": 0, "pct": 100}, "branches": {"total": 0, "covered": 0, "skipped": 0, "pct": 100}}}