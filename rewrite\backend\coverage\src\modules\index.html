
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/modules</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/modules</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.31% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>147/761</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">5.07% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>16/315</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">6.02% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>5/83</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.65% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>133/713</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="awsKinesis.ts"><a href="awsKinesis.ts.html">awsKinesis.ts</a></td>
	<td data-value="6.87" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.87" class="pct low">6.87%</td>
	<td data-value="291" class="abs low">20/291</td>
	<td data-value="1.66" class="pct low">1.66%</td>
	<td data-value="120" class="abs low">2/120</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="22" class="abs low">0/22</td>
	<td data-value="6.92" class="pct low">6.92%</td>
	<td data-value="289" class="abs low">20/289</td>
	</tr>

<tr>
	<td class="file low" data-value="awsS3.ts"><a href="awsS3.ts.html">awsS3.ts</a></td>
	<td data-value="17.54" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 17%"></div><div class="cover-empty" style="width: 83%"></div></div>
	</td>
	<td data-value="17.54" class="pct low">17.54%</td>
	<td data-value="228" class="abs low">40/228</td>
	<td data-value="4.95" class="pct low">4.95%</td>
	<td data-value="101" class="abs low">5/101</td>
	<td data-value="11.76" class="pct low">11.76%</td>
	<td data-value="17" class="abs low">2/17</td>
	<td data-value="17.41" class="pct low">17.41%</td>
	<td data-value="224" class="abs low">39/224</td>
	</tr>

<tr>
	<td class="file low" data-value="db.ts"><a href="db.ts.html">db.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="47" class="abs low">0/47</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="21" class="abs low">0/21</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="18" class="abs low">0/18</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="29" class="abs low">0/29</td>
	</tr>

<tr>
	<td class="file low" data-value="email.ts"><a href="email.ts.html">email.ts</a></td>
	<td data-value="38.46" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 38%"></div><div class="cover-empty" style="width: 62%"></div></div>
	</td>
	<td data-value="38.46" class="pct low">38.46%</td>
	<td data-value="13" class="abs low">5/13</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="38.46" class="pct low">38.46%</td>
	<td data-value="13" class="abs low">5/13</td>
	</tr>

<tr>
	<td class="file low" data-value="geolocation.ts"><a href="geolocation.ts.html">geolocation.ts</a></td>
	<td data-value="22.22" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 22%"></div><div class="cover-empty" style="width: 78%"></div></div>
	</td>
	<td data-value="22.22" class="pct low">22.22%</td>
	<td data-value="27" class="abs low">6/27</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="15" class="abs low">0/15</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="17.39" class="pct low">17.39%</td>
	<td data-value="23" class="abs low">4/23</td>
	</tr>

<tr>
	<td class="file high" data-value="ioEmitter.ts"><a href="ioEmitter.ts.html">ioEmitter.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file high" data-value="microservice_socket.ts"><a href="microservice_socket.ts.html">microservice_socket.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file low" data-value="notifyLog.ts"><a href="notifyLog.ts.html">notifyLog.ts</a></td>
	<td data-value="42.1" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 42%"></div><div class="cover-empty" style="width: 58%"></div></div>
	</td>
	<td data-value="42.1" class="pct low">42.1%</td>
	<td data-value="19" class="abs low">8/19</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="53.84" class="pct medium">53.84%</td>
	<td data-value="13" class="abs medium">7/13</td>
	</tr>

<tr>
	<td class="file medium" data-value="openai.ts"><a href="openai.ts.html">openai.ts</a></td>
	<td data-value="60" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 60%"></div><div class="cover-empty" style="width: 40%"></div></div>
	</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="5" class="abs medium">3/5</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="5" class="abs medium">3/5</td>
	</tr>

<tr>
	<td class="file low" data-value="otpService.ts"><a href="otpService.ts.html">otpService.ts</a></td>
	<td data-value="44.18" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 44%"></div><div class="cover-empty" style="width: 56%"></div></div>
	</td>
	<td data-value="44.18" class="pct low">44.18%</td>
	<td data-value="43" class="abs low">19/43</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="37.14" class="pct low">37.14%</td>
	<td data-value="35" class="abs low">13/35</td>
	</tr>

<tr>
	<td class="file high" data-value="pLimit.ts"><a href="pLimit.ts.html">pLimit.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="2" class="abs high">2/2</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="3" class="abs high">3/3</td>
	</tr>

<tr>
	<td class="file low" data-value="processLogs.ts"><a href="processLogs.ts.html">processLogs.ts</a></td>
	<td data-value="31.25" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 31%"></div><div class="cover-empty" style="width: 69%"></div></div>
	</td>
	<td data-value="31.25" class="pct low">31.25%</td>
	<td data-value="16" class="abs low">5/16</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="33.33" class="pct low">33.33%</td>
	<td data-value="15" class="abs low">5/15</td>
	</tr>

<tr>
	<td class="file low" data-value="sharedRedis.ts"><a href="sharedRedis.ts.html">sharedRedis.ts</a></td>
	<td data-value="30" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 30%"></div><div class="cover-empty" style="width: 70%"></div></div>
	</td>
	<td data-value="30" class="pct low">30%</td>
	<td data-value="20" class="abs low">6/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="3" class="abs low">0/3</td>
	<td data-value="23.52" class="pct low">23.52%</td>
	<td data-value="17" class="abs low">4/17</td>
	</tr>

<tr>
	<td class="file medium" data-value="spellingCorrector.ts"><a href="spellingCorrector.ts.html">spellingCorrector.ts</a></td>
	<td data-value="55.55" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 55%"></div><div class="cover-empty" style="width: 45%"></div></div>
	</td>
	<td data-value="55.55" class="pct medium">55.55%</td>
	<td data-value="27" class="abs medium">15/27</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="10" class="abs low">4/10</td>
	<td data-value="66.66" class="pct medium">66.66%</td>
	<td data-value="3" class="abs medium">2/3</td>
	<td data-value="55.55" class="pct medium">55.55%</td>
	<td data-value="27" class="abs medium">15/27</td>
	</tr>

<tr>
	<td class="file medium" data-value="swagger.ts"><a href="swagger.ts.html">swagger.ts</a></td>
	<td data-value="68.75" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 68%"></div><div class="cover-empty" style="width: 32%"></div></div>
	</td>
	<td data-value="68.75" class="pct medium">68.75%</td>
	<td data-value="16" class="abs medium">11/16</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="6" class="abs low">1/6</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="2" class="abs medium">1/2</td>
	<td data-value="64.28" class="pct medium">64.28%</td>
	<td data-value="14" class="abs medium">9/14</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2026-01-11T11:02:57.155Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    