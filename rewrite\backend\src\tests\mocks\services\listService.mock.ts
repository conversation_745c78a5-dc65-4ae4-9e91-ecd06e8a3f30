import { jest } from '@jest/globals';

const listService = {
    getVisibleListsForUser: jest.fn(),
    getUserListsArtifacts: jest.fn(),
    createList: jest.fn(),
    canReadList: jest.fn(),
    renameList: jest.fn(),
    toggleOrgShare: jest.fn(),
    deleteList: jest.fn(),
    shareWithUser: jest.fn(),
    shareWithEmail: jest.fn(),
    unshareWithUser: jest.fn(),
    addArtifact: jest.fn(),
    removeArtifact: jest.fn(),
    getSharedUsers: jest.fn(),
    downloadList: jest.fn(),
    downloadFavorites: jest.fn(),
    copyArtifactsFromList: jest.fn(),
};

export default listService;

