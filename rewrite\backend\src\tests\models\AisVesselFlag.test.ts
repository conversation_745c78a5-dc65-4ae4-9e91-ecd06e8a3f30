import { describe, it, jest, beforeEach, expect, afterEach } from "@jest/globals";
import { setupModelTest, cleanupModelTest } from "../mocks/modules/db.mock";

jest.resetModules();

describe('AisVesselFlag Model', () => {
    let mockMongoose: any;
    let mockDb: any;
    let mockIoEmitter: any;

    beforeEach(() => {
        const testSetup = setupModelTest('qm');
        mockMongoose = testSetup.mockMongoose;
        mockDb = testSetup.mockDb;
        mockIoEmitter = testSetup.mockIoEmitter;
        jest.clearAllMocks();
    });

    afterEach(() => {
        cleanupModelTest();
    });

    it('should create AisVesselFlag model with proper schema and hooks', async () => {
        jest.doMock('../../modules/ioEmitter', () => mockIoEmitter);
        jest.doMock('mongoose', () => mockMongoose);
        jest.doMock('../../modules/db', () => mockDb);

        delete require.cache[require.resolve('../../models/AisVesselFlag')];

        const AisVesselFlagModule = await import('../../models/AisVesselFlag');
        const AisVesselFlag = AisVesselFlagModule.default;

        expect(mockDb.qm.model).toHaveBeenCalled();
        expect(AisVesselFlag).toBeDefined();

        const schemaArg = mockDb.qm.model.mock.calls[0][1];

        expect(schemaArg.paths.mmsi).toBeDefined();
        expect(schemaArg.paths.mmsi.type).toBeDefined();
        expect(schemaArg.paths.mmsi.required).toBe(true);
        expect(schemaArg.paths.mmsi.index).toBe(true);

        expect(schemaArg.paths.flaggedBy).toBeDefined();
        expect(schemaArg.paths.flaggedBy.type).toBeDefined();
        expect(schemaArg.paths.flaggedBy.required).toBe(true);

        expect(schemaArg.paths.flaggedAt).toBeDefined();
        expect(schemaArg.paths.flaggedAt.type).toBeDefined();
        expect(schemaArg.paths.flaggedAt.required).toBe(true);
        expect(schemaArg.paths.flaggedAt.default).toBeDefined();

        // Check that indexes are set
        expect(schemaArg.index).toHaveBeenCalledTimes(2);
        expect(schemaArg.index).toHaveBeenNthCalledWith(1, { mmsi: 1, flaggedBy: 1 });
        expect(schemaArg.index).toHaveBeenNthCalledWith(2, { flaggedAt: -1 });

        // Check hooks
        expect(schemaArg.post).toHaveBeenCalledTimes(3);
        expect(schemaArg.post).toHaveBeenNthCalledWith(1, "save", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(2, "findOneAndDelete", expect.any(Function));
        expect(schemaArg.post).toHaveBeenNthCalledWith(3, "deleteMany", expect.any(Function));

        // Test save hook
        const saveHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'save');
        if (saveHookCall) {
            const mockFlag = {
                toObject: jest.fn().mockReturnValue({ _id: 'flag123', mmsi: '123456789' }),
            };
            saveHookCall[1](mockFlag);
            expect(mockIoEmitter.emit).toHaveBeenCalledWith('notifyAll', {
                name: 'ais_vessels_flagged/changed',
                data: { _id: 'flag123', mmsi: '123456789' },
            });
        }

        // Test findOneAndDelete hook
        const findOneAndDeleteHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'findOneAndDelete');
        if (findOneAndDeleteHookCall) {
            const mockFlag = {
                toObject: jest.fn().mockReturnValue({ _id: 'flag123', mmsi: '123456789' }),
                deletedCount: undefined,
            };
            findOneAndDeleteHookCall[1](mockFlag);
            expect(mockIoEmitter.emit).toHaveBeenCalledWith('notifyAll', {
                name: 'ais_vessels_flagged/changed',
                data: { _id: 'flag123', mmsi: '123456789' },
            });
        }

        // Test deleteMany hook with deletedCount > 0
        const deleteManyHookCall = schemaArg.post.mock.calls.find((call: any) => call[0] === 'deleteMany');
        if (deleteManyHookCall) {
            const mockResultWithCount = { deletedCount: 3 };
            deleteManyHookCall[1](mockResultWithCount);
            expect(mockIoEmitter.emit).toHaveBeenCalledWith('notifyAll', {
                name: 'ais_vessels_flagged/changed',
            });

            // Test deleteMany hook without deletedCount
            const mockResultWithoutCount = {
                toObject: jest.fn().mockReturnValue({ _id: 'flag123' }),
            };
            deleteManyHookCall[1](mockResultWithoutCount);
            expect(mockIoEmitter.emit).toHaveBeenCalledWith('notifyAll', {
                name: 'ais_vessels_flagged/changed',
                data: { _id: 'flag123' },
            });
        }

        // Check collection name
        expect(mockDb.qm.model).toHaveBeenCalledWith(
            'AisVesselFlagged',
            expect.any(Object),
            'ais_vessels_flagged'
        );
    });
});

