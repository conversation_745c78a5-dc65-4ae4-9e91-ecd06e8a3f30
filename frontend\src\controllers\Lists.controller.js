import axiosInstance from "../axios";

class ListsController {
    listsArtifactsCache = null;
    cachePromise = null;

    async fetchLists() {
        try {
            const res = await axiosInstance.get(`/lists`);
            return res.data;
        } catch (error) {
            console.error("Error fetching lists", error);
            throw error?.response?.data || error;
        }
    }

    async fetchListArtifacts({ listId, page = 1, limit = 50 }) {
        try {
            const res = await axiosInstance.get(`/lists/${listId}/artifacts`, { params: { page, limit } });
            return res.data;
        } catch (error) {
            console.error("Error fetching list artifacts", error);
            throw error?.response?.data || error;
        }
    }

    async createList({ name, sharedWithOrganization }) {
        try {
            const res = await axiosInstance.post(`/lists`, { name, sharedWithOrganization });
            return res.data;
        } catch (error) {
            console.error("Error creating list", error);
            throw error?.response?.data || error;
        }
    }

    async updateList({ listId, name, sharedWithOrganization }) {
        try {
            const res = await axiosInstance.patch(`/lists/${listId}`, { name, sharedWithOrganization });
            return res.data;
        } catch (error) {
            console.error("Error updating list", error);
            throw error?.response?.data || error;
        }
    }

    async deleteList({ listId }) {
        try {
            const res = await axiosInstance.delete(`/lists/${listId}`);
            return res.data;
        } catch (error) {
            console.error("Error deleting list", error);
            throw error?.response?.data || error;
        }
    }

    async removeFromSharedList({ listId, userId }) {
        try {
            const res = await axiosInstance.delete(`/lists/${listId}/share/users/${userId}`);
            return res.data;
        } catch (error) {
            console.error("Error removing from shared list", error);
            throw error?.response?.data || error;
        }
    }

    async shareWithUser({ listId, userId }) {
        try {
            const res = await axiosInstance.post(`/lists/${listId}/share/users`, { userId }, { meta: { showSnackbar: false } });
            return res.data;
        } catch (error) {
            console.error("Error sharing list", error);
            throw error?.response?.data || error;
        }
    }

    async shareWithEmail({ listId, email }) {
        try {
            const res = await axiosInstance.post(`/lists/${listId}/share/users`, { email }, { meta: { showSnackbar: false } });
            return res.data;
        } catch (error) {
            console.error("Error sharing list with email", error);
            throw error?.response?.data || error;
        }
    }

    async getSharedUsers({ listId }) {
        try {
            const res = await axiosInstance.get(`/lists/${listId}/share/users`);
            return res.data;
        } catch (error) {
            console.error("Error fetching shared users", error);
            throw error?.response?.data || error;
        }
    }

    async addArtifact({ listId, artifactId }) {
        try {
            const res = await axiosInstance.post(`/lists/${listId}/artifacts`, { artifactId }, { meta: { showSnackbar: true, variant: "listSnackbar" } });
            this.addArtifactToCache(listId, artifactId);
            return res.data;
        } catch (error) {
            console.error("Error adding artifact to list", error);
            throw error?.response?.data || error;
        }
    }

    async removeArtifact({ listId, artifactId }) {
        try {
            const res = await axiosInstance.delete(`/lists/${listId}/artifacts/${artifactId}`, { meta: { showSnackbar: true, variant: "listSnackbar" } });
            this.removeArtifactFromCache(listId, artifactId);
            return res.data;
        } catch (error) {
            console.error("Error removing artifact from list", error);
            throw error?.response?.data || error;
        }
    }

    async getUserListsArtifacts(forceRefresh = false) {
        if (!forceRefresh && this.listsArtifactsCache !== null) {
            return this.listsArtifactsCache;
        }

        if (this.cachePromise) {
            return this.cachePromise;
        }

        this.cachePromise = axiosInstance
            .get(`/lists/user/artifacts`)
            .then((res) => {
                this.listsArtifactsCache = res.data;
                this.cachePromise = null;
                return this.listsArtifactsCache;
            })
            .catch((error) => {
                console.error("Error fetching user lists artifacts", error);
                this.cachePromise = null;
                throw error?.response?.data || error;
            });

        return this.cachePromise;
    }

    async isArtifactInList(listId, artifactId) {
        try {
            const cache = await this.getUserListsArtifacts();
            return cache[listId]?.includes(artifactId) || false;
        } catch (error) {
            console.error("Error checking if artifact is in list", error);
            return false;
        }
    }

    isArtifactInAnyList(artifactId) {
        if (this.listsArtifactsCache === null) {
            return false;
        }
        return Object.values(this.listsArtifactsCache).some((artifactIds) => artifactIds.includes(artifactId));
    }

    async getListsContainingArtifact(artifactId) {
        try {
            const cache = await this.getUserListsArtifacts();
            return Object.keys(cache).filter((listId) => cache[listId].includes(artifactId));
        } catch (error) {
            console.error("Error getting lists containing artifact", error);
            return [];
        }
    }

    invalidateCache() {
        this.listsArtifactsCache = null;
        this.cachePromise = null;
    }

    addArtifactToCache(listId, artifactId) {
        if (this.listsArtifactsCache === null) return;
        if (!this.listsArtifactsCache[listId]) {
            this.listsArtifactsCache[listId] = [];
        }
        if (!this.listsArtifactsCache[listId].includes(artifactId)) {
            this.listsArtifactsCache[listId].push(artifactId);
        }
    }

    removeArtifactFromCache(listId, artifactId) {
        if (this.listsArtifactsCache === null) return;
        if (this.listsArtifactsCache[listId]) {
            this.listsArtifactsCache[listId] = this.listsArtifactsCache[listId].filter((id) => id !== artifactId);
        }
    }

    async downloadList({ listId }) {
        try {
            await axiosInstance.get(`/lists/${listId}/download`, { meta: { showSnackbar: true } });
        } catch (error) {
            console.error("Error downloading list", error);
            throw error?.response?.data || error;
        }
    }

    async downloadFavorites() {
        try {
            await axiosInstance.get(`/lists/favorite/download`, { meta: { showSnackbar: true } });
        } catch (error) {
            console.error("Error downloading favorites", error);
            throw error?.response?.data || error;
        }
    }

    async copyFromList({ targetType, targetListId, sourceListId, sourceType }) {
        try {
            const res = await axiosInstance.post(
                `/lists/copy-from`,
                { targetType, targetListId, sourceListId, sourceType },
                { meta: { showSnackbar: true } }
            );
            return res.data;
        } catch (error) {
            console.error("Error copying artifacts from list", error);
            throw error?.response?.data || error;
        }
    }
}

const listsController = new ListsController();
export default listsController;
