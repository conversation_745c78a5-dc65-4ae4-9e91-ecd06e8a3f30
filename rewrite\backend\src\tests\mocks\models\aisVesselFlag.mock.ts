import { jest } from '@jest/globals';

type AnyObject = Record<string, any>;

function MockAisVesselFlag(this: AnyObject, data: AnyObject) {
    Object.assign(this, data);
    this.save = jest.fn().mockResolvedValue(this);
}

(MockAisVesselFlag as AnyObject).find = jest.fn();
(MockAisVesselFlag as AnyObject).findOne = jest.fn();
(MockAisVesselFlag as AnyObject).aggregate = jest.fn();
(MockAisVesselFlag as AnyObject).deleteMany = jest.fn();
(MockAisVesselFlag as AnyObject).findOneAndDelete = jest.fn();
(MockAisVesselFlag as AnyObject).distinct = jest.fn();

(MockAisVesselFlag as AnyObject).prototype = {
    save: jest.fn().mockResolvedValue(true as never),
};

// Make it mockable
(MockAisVesselFlag as any).mockImplementation = jest.fn().mockImplementation;

export default MockAisVesselFlag as any;

