import { jest } from '@jest/globals';

export const flagAisVessel = jest.fn();
export const unflagAisVessel = jest.fn();
export const getFlaggedAisVessels = jest.fn();
export const getUserFlaggedAisVesselMmsis = jest.fn();
export const removeAllFlagsFromAisVessel = jest.fn();

export default {
    flagAisVessel,
    unflagAisVessel,
    getFlaggedAisVessels,
    getUserFlaggedAisVesselMmsis,
    removeAllFlagsFromAisVessel,
};

