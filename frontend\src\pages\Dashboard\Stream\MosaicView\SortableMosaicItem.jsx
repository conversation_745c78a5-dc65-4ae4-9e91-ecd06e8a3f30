import { useSortable } from "@dnd-kit/sortable";
import { CSS } from "@dnd-kit/utilities";
import { DragIndicator } from "@mui/icons-material";
import { Grid, Typography } from "@mui/material";
import theme from "../../../../theme";

const SortableMosaicItem = ({ stream }) => {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({ id: stream.StreamName });
        const constrainedTransform = transform
        ? {
              ...transform,
              x: 0, // Prevent horizontal movement
          }
        : null;
    
    const style = {
        transform: CSS.Transform.toString(constrainedTransform),
        transition,
        padding: "8px",
        display: "flex",
        alignItems: "center",
        backgroundColor: theme.palette.primary.dark,
        border: `1px solid ${theme.palette.custom?.borderColor || "#000"}`,
        borderRadius: "5px",
        margin: "8px 0",
        cursor: isDragging ? "grabbing" : "grab",
        width: "100%",
    };

    return (
        <Grid ref={setNodeRef} style={style} container alignItems="center" {...attributes} {...listeners}>
            <DragIndicator style={{ marginRight: 8 }} />
            <Typography>{stream.VesselName || "Unregistered"}</Typography>
        </Grid>
    );
};

export default SortableMosaicItem;
