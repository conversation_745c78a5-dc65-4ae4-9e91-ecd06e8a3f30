import mongoose from "mongoose";
import crypto from "crypto";
import List, { IList } from "../models/List";
import ListACL from "../models/ListACL";
import ListArtifact from "../models/ListArtifact";
import ArtifactFavourites from "../models/ArtifactFavourites";
import User from "../models/User";
import Vessel from "../models/Vessel";
import { IAuthUser } from "src/interfaces/User";
import microservice_socket from "../modules/microservice_socket";
import { canAccessVessel } from "../utils/functions";
import db from "../modules/db";

async function canManageList(list: IList, user: IAuthUser): Promise<boolean> {
    return String(list.owner_id) === String(user._id);
}

async function canReadList(list: IList, user: IAuthUser): Promise<boolean> {
    if (String(list.owner_id) === String(user._id)) return true;
    const acl = await ListACL.findOne({ list_id: list._id, user_id: user._id }).lean();
    if (acl) return true;
    if (list.shared_with_organization && user.organization && user.organization._id) {
        // Visibility is based on owner's current org: fetch owner's org_id from User collection via owner_id
        // For performance, caller should prefer using `getVisibleListsForUser` instead of per-list checks.
        return true; // Visibility filtering will happen in query in getVisibleListsForUser
    }
    return false;
}

async function createList({ name, sharedWithOrganization, user }: { name: string; sharedWithOrganization?: boolean; user: IAuthUser }) {
    const doc = await List.create({ owner_id: user._id, name, shared_with_organization: !!sharedWithOrganization });
    return doc.toObject();
}

async function renameList({ listId, name, user }: { listId: string; name: string; user: IAuthUser }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (!(await canManageList(list, user))) throw { status: 403, message: "Forbidden" };
    list.name = name;
    list.updated_at = new Date();
    await list.save();
    return list.toObject();
}

async function toggleOrgShare({ listId, sharedWithOrganization, user }: { listId: string; sharedWithOrganization: boolean; user: IAuthUser }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (!(await canManageList(list, user))) throw { status: 403, message: "Forbidden" };
    list.shared_with_organization = !!sharedWithOrganization;
    list.updated_at = new Date();
    await list.save();
    return list.toObject();
}

async function deleteList({ listId, user }: { listId: string; user: IAuthUser }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (!(await canManageList(list, user))) throw { status: 403, message: "Forbidden" };
    list.name = crypto.randomUUID();
    list.is_deleted = true;
    list.updated_at = new Date();
    await list.save();
    return { deleted: true };
}

async function shareWithUser({ listId, targetUserId, user }: { listId: string; targetUserId: string; user: IAuthUser }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (list.is_deleted) throw { status: 404, message: "List not found" };
    if (list.owner_id.toString() === targetUserId) throw { status: 400, message: "Cannot share list with yourself" };
    if (!(await canManageList(list, user))) throw { status: 403, message: "Forbidden" };

    const existingShare = await ListACL.findOne({
        list_id: list._id,
        user_id: new mongoose.Types.ObjectId(targetUserId),
    });

    if (existingShare) {
        throw { status: 409, message: "List is already shared with this user" };
    }

    await ListACL.create({
        list_id: list._id,
        user_id: new mongoose.Types.ObjectId(targetUserId),
    });
    return { ok: true };
}

async function shareWithEmail({ listId, email, user }: { listId: string; email: string; user: IAuthUser }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (list.is_deleted) throw { status: 404, message: "List not found" };
    if (!(await canManageList(list, user))) throw { status: 403, message: "Forbidden" };

    const existingUser = await User.findOne({ email: email.trim().toLowerCase(), is_deleted: false });

    if (!existingUser) {
        throw { status: 404, message: "User not found with this email address" };
    }

    if (list.owner_id.toString() === existingUser._id.toString()) {
        throw { status: 400, message: "Cannot share list with yourself" };
    }

    const existingShare = await ListACL.findOne({
        list_id: list._id,
        user_id: existingUser._id,
    });

    if (existingShare) {
        throw { status: 409, message: "List is already shared with this user" };
    }

    await ListACL.create({
        list_id: list._id,
        user_id: existingUser._id,
    });

    return { ok: true };
}

async function unshareWithUser({ listId, targetUserId }: { listId: string; targetUserId: string }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (list.is_deleted) throw { status: 404, message: "List not found" };
    if (list.owner_id.toString() === targetUserId) throw { status: 400, message: "Cannot unshare list with yourself" };
    // if (list.shared_with_organization) throw { status: 403, message: "Cannot remove users from organization-shared lists" };
    // if (!(await canManageList(list, user))) throw { status: 403, message: "Forbidden" };
    await ListACL.deleteOne({ list_id: list._id, user_id: new mongoose.Types.ObjectId(targetUserId) });
    return { ok: true };
}

async function addArtifact({ listId, artifactId, user }: { listId: string; artifactId: string; user: IAuthUser }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (list.is_deleted) throw { status: 404, message: "List not found" };
    if (!(await canReadList(list, user))) throw { status: 403, message: "Forbidden" };
    await ListArtifact.updateOne(
        { list_id: list._id, artifact_id: new mongoose.Types.ObjectId(artifactId) },
        { $setOnInsert: { added_by: user._id, created_at: new Date() } },
        { upsert: true },
    );
    return { ok: true, listName: list.name };
}

async function removeArtifact({ listId, artifactId, user }: { listId: string; artifactId: string; user: IAuthUser }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (list.is_deleted) throw { status: 404, message: "List not found" };
    if (!(await canReadList(list, user))) throw { status: 403, message: "Forbidden" };
    await ListArtifact.deleteOne({ list_id: list._id, artifact_id: new mongoose.Types.ObjectId(artifactId) });
    return { ok: true, listName: list.name };
}

async function getVisibleListsForUser(user: IAuthUser) {
    const userId = new mongoose.Types.ObjectId(String(user._id));
    const ownerOrgId = user.organization?._id ? new mongoose.Types.ObjectId(String(user.organization._id)) : null;

    const ownedLists = await List.find({ owner_id: userId, is_deleted: false }).lean();
    const sharedACL = await ListACL.find({ user_id: userId }).select("list_id").lean();
    const sharedListIds = sharedACL.map((acl) => new mongoose.Types.ObjectId(acl.list_id));
    const sharedLists = sharedListIds.length > 0 ? await List.find({ _id: { $in: sharedListIds }, is_deleted: false }).lean() : [];

    let orgSharedLists: any[] = [];
    if (ownerOrgId) {
        const orgOwners = await User.find({ organization_id: ownerOrgId, is_deleted: false }).select("_id").lean();
        const orgOwnerIds = orgOwners.map((u: any) => new mongoose.Types.ObjectId(u._id));
        if (orgOwnerIds.length > 0) {
            orgSharedLists = await List.find({
                owner_id: { $in: orgOwnerIds, $ne: userId },
                shared_with_organization: true,
                is_deleted: false,
            }).lean();
        }
    }

    const allListsMap = new Map();
    [...ownedLists, ...sharedLists, ...orgSharedLists].forEach((list) => {
        allListsMap.set(String(list._id), list);
    });

    const lists = Array.from(allListsMap.values()).sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

    // Get all unique owner IDs and list IDs for bulk queries
    const ownerIds = new Set(lists.map((list) => String(list.owner_id)));
    const listIds = lists.map((list) => new mongoose.Types.ObjectId(list._id));

    // Fetch all owners in bulk
    const owners = await User.find({ _id: { $in: Array.from(ownerIds).map((id) => new mongoose.Types.ObjectId(id)) } })
        .select("_id name")
        .lean();
    const ownerMap = new Map(owners.map((o: any) => [String(o._id), o.name]));

    // Count shared users for all lists in bulk
    const sharedUsersCounts = await ListACL.aggregate([
        { $match: { list_id: { $in: listIds } } },
        { $group: { _id: "$list_id", count: { $sum: 1 } } },
    ]);
    const sharedUsersCountMap = new Map(sharedUsersCounts.map((item: any) => [String(item._id), item.count]));

    // Count artifacts for all lists in bulk
    const artifactCounts = await ListArtifact.aggregate([
        { $match: { list_id: { $in: listIds } } },
        { $group: { _id: "$list_id", count: { $sum: 1 } } },
    ]);
    const artifactCountMap = new Map(artifactCounts.map((item: any) => [String(item._id), item.count]));

    // Enrich lists with owner name, shared users count, and artifact count
    const enrichedLists = lists.map((list: any) => ({
        ...list,
        owner_name: ownerMap.get(String(list.owner_id)) || "Unknown",
        shared_users_count: sharedUsersCountMap.get(String(list._id)) || 0,
        artifact_count: artifactCountMap.get(String(list._id)) || 0,
    }));

    return enrichedLists;
}

async function getUserListsArtifacts({ user }: { user: IAuthUser }) {
    const userId = new mongoose.Types.ObjectId(String(user._id));
    const ownerOrgId = user.organization?._id ? new mongoose.Types.ObjectId(String(user.organization._id)) : null;

    const ownedLists = await List.find({ owner_id: userId, is_deleted: false }).select("_id").lean();
    const sharedListACLs = await ListACL.find({ user_id: userId }).select("list_id").lean();
    const sharedListIds = sharedListACLs.map((acl) => new mongoose.Types.ObjectId(acl.list_id));
    const sharedLists =
        sharedListIds.length > 0
            ? await List.find({ _id: { $in: sharedListIds }, is_deleted: false })
                  .select("_id")
                  .lean()
            : [];

    let orgSharedLists: any[] = [];
    if (ownerOrgId) {
        const orgOwners = await User.find({ organization_id: ownerOrgId, is_deleted: false }).select("_id").lean();
        const orgOwnerIds = orgOwners.map((u: any) => new mongoose.Types.ObjectId(u._id));
        if (orgOwnerIds.length > 0) {
            orgSharedLists = await List.find({
                owner_id: { $in: orgOwnerIds, $ne: userId },
                shared_with_organization: true,
                is_deleted: false,
            })
                .select("_id")
                .lean();
        }
    }

    const allListIds = [
        ...ownedLists.map((list: any) => list._id),
        ...sharedLists.map((list: any) => list._id),
        ...orgSharedLists.map((list: any) => list._id),
    ];

    const uniqueListIds = [...new Set(allListIds.map((id) => String(id)))];

    if (uniqueListIds.length === 0) {
        return {};
    }

    const listObjectIds = uniqueListIds.map((id) => new mongoose.Types.ObjectId(id));

    const listArtifacts = await ListArtifact.find({ list_id: { $in: listObjectIds } })
        .select("list_id artifact_id")
        .lean();

    const map: Record<string, string[]> = {};

    for (const listArtifact of listArtifacts) {
        const listId = String(listArtifact.list_id);
        const artifactId = String(listArtifact.artifact_id);

        if (!map[listId]) {
            map[listId] = [];
        }

        if (!map[listId].includes(artifactId)) {
            map[listId].push(artifactId);
        }
    }

    return map;
}

async function getSharedUsers({ listId, user }: { listId: string; user: IAuthUser }) {
    const list = await List.findById(listId);
    if (!list) throw { status: 404, message: "List not found" };
    if (list.is_deleted) throw { status: 404, message: "List not found" };
    if (!(await canManageList(list, user))) throw { status: 403, message: "Forbidden" };

    const acls = await ListACL.find({ list_id: list._id }).lean();
    const userIds = acls.map((acl) => new mongoose.Types.ObjectId(acl.user_id));

    if (userIds.length === 0) {
        return [];
    }

    const users = await User.find({ _id: { $in: userIds }, is_deleted: false })
        .select("_id name email")
        .lean();

    return users.map((u: any) => ({
        _id: u._id,
        name: u.name,
        email: u.email,
    }));
}

async function downloadList({ listId, user }: { listId: string; user: IAuthUser }) {
    // Check permissions and get list
    const list = await List.findById(listId).lean();
    if (!list || list.is_deleted) throw { status: 404, message: "List not found" };
    if (!(await canReadList(list as any, user))) throw { status: 403, message: "Forbidden" };

    // Validate user has email
    if (!user.email) {
        throw { status: 400, message: "User email not found" };
    }

    // Check if list has accessible artifacts
    const listArtifacts = await ListArtifact.find({ list_id: list._id }).select("artifact_id").lean();
    if (listArtifacts.length === 0) {
        throw { status: 400, message: "List is empty" };
    }

    const artifactIds = listArtifacts.map((la: any) => new mongoose.Types.ObjectId(String(la.artifact_id)));
    const artifacts = await db.qmai
        .collection("analysis_results")
        .find({ _id: { $in: artifactIds } })
        .project({ _id: 1, onboard_vessel_id: 1 })
        .toArray();

    // Get vessels for artifacts
    const vesselIds = new Set<mongoose.Types.ObjectId>();
    artifacts.forEach((a: any) => {
        if (a.onboard_vessel_id) {
            vesselIds.add(new mongoose.Types.ObjectId(a.onboard_vessel_id));
        }
    });

    const vessels = await Vessel.find({ _id: { $in: Array.from(vesselIds) } }, { _id: 1, is_active: 1, region_group_id: 1 }).lean();
    const vesselMap = new Map(vessels.map((v: any) => [v._id.toString(), v]));

    // Count accessible artifacts
    const accessibleCount = artifacts.filter((a: any) => {
        if (!a.onboard_vessel_id) return false;
        const vessel = vesselMap.get(new mongoose.Types.ObjectId(a.onboard_vessel_id).toString());
        if (!vessel) return false;
        return canAccessVessel({ user }, vessel as any);
    }).length;

    if (accessibleCount === 0) {
        throw { status: 400, message: "List has no accessible artifacts" };
    }

    microservice_socket.emit("lists/downloadList", JSON.stringify({ type: "list", listId, userId: String(user._id) }));
    return { message: "Download started! You will receive an email with the download link once it's ready." };
}

async function downloadFavorites({ user }: { user: IAuthUser }) {
    if (!user.email) {
        throw { status: 400, message: "User email not found" };
    }

    // Check if favorites has accessible artifacts
    const favorites = await ArtifactFavourites.find({ user_id: user._id }).select("artifact_id").lean();
    if (favorites.length === 0) {
        throw { status: 400, message: "Favorites list is empty" };
    }

    const artifactIds = favorites.map((fav: any) => new mongoose.Types.ObjectId(String(fav.artifact_id)));
    const artifacts = await db.qmai
        .collection("analysis_results")
        .find({ _id: { $in: artifactIds } })
        .project({ _id: 1, onboard_vessel_id: 1 })
        .toArray();

    // Get vessels for artifacts
    const vesselIds = new Set<mongoose.Types.ObjectId>();
    artifacts.forEach((a: any) => {
        if (a.onboard_vessel_id) {
            vesselIds.add(new mongoose.Types.ObjectId(a.onboard_vessel_id));
        }
    });

    const vessels = await Vessel.find({ _id: { $in: Array.from(vesselIds) } }, { _id: 1, is_active: 1, region_group_id: 1 }).lean();
    const vesselMap = new Map(vessels.map((v: any) => [v._id.toString(), v]));

    // Count accessible artifacts
    const accessibleCount = artifacts.filter((a: any) => {
        if (!a.onboard_vessel_id) return false;
        const vessel = vesselMap.get(String(a.onboard_vessel_id));
        if (!vessel) return false;
        return canAccessVessel({ user }, vessel as any);
    }).length;

    if (accessibleCount === 0) {
        throw { status: 400, message: "Favorites list has no accessible artifacts" };
    }

    microservice_socket.emit("lists/downloadList", JSON.stringify({ type: "favorite", userId: String(user._id) }));
    return { message: "Download started! You will receive an email with the download link once it's ready." };
}

async function copyArtifactsFromList({
    targetType,
    targetListId,
    sourceListId,
    sourceType,
    user,
}: {
    targetType: "list" | "favorites";
    targetListId?: string;
    sourceListId?: string;
    sourceType: "list" | "favorites";
    user: IAuthUser;
}) {
    let sourceArtifactIds: mongoose.Types.ObjectId[] = [];
    let sourceListName = "";

    if (sourceType === "favorites") {
        sourceListName = "Favorites";
        const favorites = await ArtifactFavourites.find({ user_id: user._id }).select("artifact_id").lean();
        sourceArtifactIds = favorites.map((f: any) => new mongoose.Types.ObjectId(f.artifact_id));
    } else {
        if (!sourceListId) throw { status: 400, message: "Source list ID is required when sourceType is 'list'" };
        const sourceList = await List.findById(sourceListId);
        if (!sourceList) throw { status: 404, message: "Source list not found" };
        if (sourceList.is_deleted) throw { status: 404, message: "Source list not found" };
        if (!(await canReadList(sourceList, user))) throw { status: 403, message: "Forbidden: Cannot access source list" };

        sourceListName = sourceList.name;
        const listArtifacts = await ListArtifact.find({ list_id: sourceList._id }).select("artifact_id").lean();
        sourceArtifactIds = listArtifacts.map((la: any) => new mongoose.Types.ObjectId(la.artifact_id));
    }

    if (sourceArtifactIds.length === 0) {
        return { message: "Source list is empty" };
    }

    let targetListName = "";

    if (targetType === "favorites") {
        targetListName = "Favorites";
        const userId = new mongoose.Types.ObjectId(String(user._id));

        const existingFavorites = await ArtifactFavourites.find({
            user_id: user._id,
            artifact_id: { $in: sourceArtifactIds },
        })
            .select("artifact_id")
            .lean();
        const existingFavoriteIds = new Set(existingFavorites.map((ef: any) => String(ef.artifact_id)));

        if (existingFavoriteIds.size === sourceArtifactIds.length) {
            return { message: `All artifacts from ${sourceListName} already exist in ${targetListName}` };
        }

        const newArtifactIds = sourceArtifactIds.filter((artifactId) => !existingFavoriteIds.has(String(artifactId)));

        if (newArtifactIds.length > 0) {
            const operations = newArtifactIds.map((artifactId) => ({
                insertOne: {
                    document: {
                        user_id: userId,
                        artifact_id: artifactId,
                    },
                },
            }));

            await ArtifactFavourites.bulkWrite(operations);
        }
    } else {
        // Copy to regular list
        if (!targetListId) throw { status: 400, message: "Target list ID is required when targetType is 'list'" };
        const targetList = await List.findById(targetListId);
        if (!targetList) throw { status: 404, message: "Target list not found" };
        if (targetList.is_deleted) throw { status: 404, message: "Target list not found" };
        if (!(await canReadList(targetList, user))) throw { status: 403, message: "Forbidden: Cannot access target list" };

        targetListName = targetList.name;

        const existingArtifacts = await ListArtifact.find({
            list_id: targetList._id,
            artifact_id: { $in: sourceArtifactIds },
        })
            .select("artifact_id")
            .lean();
        const existingArtifactIds = new Set(existingArtifacts.map((ea: any) => String(ea.artifact_id)));

        if (existingArtifactIds.size === sourceArtifactIds.length) {
            return { message: `All artifacts from ${sourceListName} already exist in ${targetListName}` };
        }

        const newArtifactIds = sourceArtifactIds.filter((artifactId) => !existingArtifactIds.has(String(artifactId)));

        if (newArtifactIds.length > 0) {
            const operations = newArtifactIds.map((artifactId) => ({
                insertOne: {
                    document: {
                        list_id: targetList._id,
                        artifact_id: artifactId,
                        added_by: user._id,
                        created_at: new Date(),
                    },
                },
            }));

            await ListArtifact.bulkWrite(operations);
        }
    }

    return { message: `${sourceListName} has been copied to ${targetListName}` };
}

export default {
    canManageList,
    canReadList,
    getSharedUsers,
    createList,
    renameList,
    toggleOrgShare,
    deleteList,
    shareWithUser,
    shareWithEmail,
    unshareWithUser,
    addArtifact,
    removeArtifact,
    getVisibleListsForUser,
    getUserListsArtifacts,
    downloadList,
    downloadFavorites,
    copyArtifactsFromList,
};
