import React, { useState, useRef, useEffect } from "react";
import { TextField, CircularProgress, InputAdornment, Paper } from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import axiosInstance from "../../../axios";
import theme from "../../../theme";
import ClickAwayListener from "@mui/material/ClickAwayListener";

const DEBOUNCE_DELAY = 600;

const ArtifactAutoSuggest = ({ setSearchQuery, onSelect, isLoading }) => {

    const [input, setInput] = useState("");
    const [suggestions, setSuggestions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [open, setOpen] = useState(false);
    const [highlightedIdx, setHighlightedIdx] = useState(-1);
    const [isKeyboardNav, setIsKeyboardNav] = useState(false);
    const [selectedSuggestion, setSelectedSuggestion] = useState("");
    const debounceRef = useRef();
    const inputRef = useRef();
    const itemRefs = useRef([]);
    const containerRef = useRef();
    const abortControllerRef = useRef();
    const suppressFetch = useRef(false);

    useEffect(() => {
        // setSelectedSuggestion(input);
        // setLoading(false);
        // if (suppressFetch.current) {
        //     suppressFetch.current = false;
        //     return;
        // }
        // if (!input) {
        //     setSuggestions([]);
        //     setOpen(false);
        //     setHighlightedIdx(-1);
        //     return;
        // }
        // setLoading(true);
        if (debounceRef.current) clearTimeout(debounceRef.current);
        debounceRef.current = setTimeout(async () => {
            setSearchQuery(input);
            // try {
            //     if (abortControllerRef.current) {
            //         abortControllerRef.current.abort();
            //     }
            //     abortControllerRef.current = new AbortController();
            //     const res = await axiosInstance.post(
            //         "/suggestions",
            //         { query: input },
            //         { signal: abortControllerRef.current.signal, meta: { showSnackbar: false } },
            //     );
            //     abortControllerRef.current = null;
            //     setSuggestions(res.data?.suggestions || []);
            //     setOpen(true);
            //     setHighlightedIdx(-1);
            // } catch (err) {
            //     if (err.name === "CanceledError") return;
            //     setSuggestions([]);
            //     setOpen(false);
            //     setHighlightedIdx(-1);
            // } finally {
            //     setLoading(false);
            // }
        }, DEBOUNCE_DELAY);
        return () => debounceRef.current && clearTimeout(debounceRef.current);
    }, [input]);

    useEffect(() => {
        if (highlightedIdx >= 0 && itemRefs.current[highlightedIdx]) {
            itemRefs.current[highlightedIdx].scrollIntoView({ block: "nearest" });
        }
    }, [highlightedIdx]);

    // const handleSelect = (suggestion, isAutoSelect = false) => {
    //     suppressFetch.current = true;
    //     setInput(suggestion);
    //     setSelectedSuggestion(suggestion);
    //     setOpen(false);
    //     setHighlightedIdx(-1);
    //     if (onSelect) {
    //         if (isAutoSelect) {
    //             onSelect(suggestion, selectedSuggestion);
    //         } else {
    //             onSelect(suggestion);
    //         }
    //     }
    // };

    // const handleKeyDown = (e) => {
    //     if (loading || isLoading) return;
    //     if (e.key === "ArrowDown") {
    //         e.preventDefault();
    //         setIsKeyboardNav(true);
    //         if (!open && suggestions.length > 0) setOpen(true);
    //         setHighlightedIdx((prev) => Math.min(prev + 1, suggestions.length - 1));
    //     } else if (e.key === "ArrowUp") {
    //         e.preventDefault();
    //         setIsKeyboardNav(true);
    //         setHighlightedIdx((prev) => Math.max(prev - 1, 0));
    //     } else if (e.key === "Enter") {
    //         e.preventDefault();
    //         if (input.trim() === "") {
    //             if (onSelect) onSelect("");
    //             return;
    //         }
    //         if (highlightedIdx >= 0 && suggestions[highlightedIdx]) {
    //             if (isKeyboardNav) {
    //                 handleSelect(suggestions[highlightedIdx]);
    //             } else if (onSelect) onSelect(suggestions[highlightedIdx], selectedSuggestion);
    //         } else if (suggestions.length > 0 && input.trim().toLowerCase() !== suggestions[0].trim().toLowerCase()) {
    //             handleSelect(suggestions[0], true);
    //         } else {
    //             handleSelect(selectedSuggestion);
    //         }
    //     }
    // };

    return (
        <ClickAwayListener onClickAway={() => setOpen(false)}>
            <div ref={containerRef} style={{ position: "relative", width: "100%" }}>
                <TextField
                    fullWidth
                    placeholder="Search (Powered by AI)"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    autoComplete="off"
                    inputRef={inputRef}
                    InputProps={{
                        endAdornment: loading ? <CircularProgress size={20} sx={{ color: "#fff" }} /> : null,
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon sx={{ color: "#fff" }} />
                            </InputAdornment>
                        ),
                    }}
                    sx={{
                        borderRadius: 2,
                        input: { color: "#fff", textTransform: "none" },
                        label: { color: "#878787" },
                        "& input.Mui-disabled": {
                            "-webkit-text-fill-color": "unset",
                            color: "#878787",
                        },
                        "& .MuiOutlinedInput-root": {
                            "& fieldset": { borderColor: "#23272F" },
                            "&:hover fieldset": { borderColor: "#1976d2" },
                            "&.Mui-focused fieldset": { borderColor: "#1976d2" },
                        },
                    }}
                    onFocus={() => {
                        if (suggestions.length > 0) setOpen(true);
                    }}
                    // onKeyDown={handleKeyDown}
                    disabled={isLoading}
                />
                {open && !isLoading && suggestions.length > 0 && (
                    <Paper
                        elevation={4}
                        style={{
                            position: "absolute",
                            width: "100%",
                            zIndex: 10,
                            maxHeight: 400,
                            overflowY: "auto",
                            background: theme.palette.primary.main,
                            borderRadius: 10,
                            marginTop: 4,
                            padding: 0,
                        }}
                    >
                        <ul
                            style={{
                                listStyle: "none",
                                margin: 0,
                                padding: 0,
                                paddingTop: 6,
                            }}
                        >
                            {suggestions.map((s, idx) => (
                                <li
                                    key={idx}
                                    ref={(el) => (itemRefs.current[idx] = el)}
                                    tabIndex={0}
                                    onClick={() => handleSelect(s)}
                                    onMouseDown={(e) => e.preventDefault()}
                                    onMouseEnter={() => {
                                        setIsKeyboardNav(true);
                                        setHighlightedIdx(idx);
                                    }}
                                    onMouseLeave={() => {
                                        setIsKeyboardNav(true);
                                        setHighlightedIdx(-1);
                                    }}
                                    style={{
                                        color: "#fff",
                                        cursor: "pointer",
                                        background: highlightedIdx === idx ? "#0B1222" : "inherit",
                                        marginBottom: 6,
                                        padding: "10px 16px",
                                        display: "flex",
                                        alignItems: "center",
                                        gap: 12,
                                        outline: "none",
                                        textTransform: "none",
                                    }}
                                    onKeyDown={(e) => {
                                        if (e.key === "Enter" || e.key === " ") {
                                            handleSelect(s);
                                        }
                                    }}
                                >
                                    <SearchIcon sx={{ color: "#fff", mr: 1 }} />
                                    <span style={{ textTransform: "none" }}>{s}</span>
                                </li>
                            ))}
                        </ul>
                    </Paper>
                )}
            </div>
        </ClickAwayListener>
    );
};

export default ArtifactAutoSuggest;
