import express, { Request, Response, NextFunction } from "express";
import isAuthenticated from "../middlewares/auth";
import { validateError, canAccessVessel } from "../utils/functions";
import rateLimit from "express-rate-limit";
import assignEndpointId from "../middlewares/assignEndpointId";
import { endpointIds } from "../utils/endpointIds";
import streamService from "../services/Stream.service";
import { validateData } from "../middlewares/validator";
import { query } from "express-validator";
import Vessel from "../models/Vessel";
import RegionGroup from "../models/RegionGroup";
import mongoose from "mongoose";
import { IVessel } from "../interfaces/Vessel";
import { IRegionGroup } from "../interfaces/RegionGroup";
import { IStreamServiceInfo } from "../interfaces/Kinesis";
import { IVesselInfo } from "../interfaces/VesselInfo";
import db from "../modules/db";
import vesselOnlineLookupService from "../services/VesselOnlineLookup.service";

const router = express.Router();

const authUserApiLimiter = rateLimit({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

// Middleware to apply the correct limiter
function conditionalRateLimiter(req: Request, res: Response, next: NextFunction): void {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    } else {
        apiLimiter(req, res, next);
    }
}

router.use("/", conditionalRateLimiter);

router.get(
    "/info",
    assignEndpointId.bind(this, endpointIds.FETCH_VESSELS_INFO),
    isAuthenticated,
    validateData.bind(this, [
        query("regions")
            .isString()
            .optional()
            .customSanitizer((v: string) => v.split(",").map((v: string) => v.trim())),
        query("region_groups")
            .optional()
            .customSanitizer((v: string) => {
                if (!v || v.trim() === "") return [];
                return v
                    .split(",")
                    .map((id: string) => id.trim())
                    .filter((id: string) => id.length > 0 && mongoose.Types.ObjectId.isValid(id));
            }),
    ]),
    async (req: Request, res: Response) => {
        try {
            const { regions, region_groups } = req.query as { regions?: string[]; region_groups?: string[] };

            let vessels: IVessel[] = await Vessel.find();
            const streams: IStreamServiceInfo[] = await streamService.fetchAll();
            const regionGroups: IRegionGroup[] = await RegionGroup.find();

            const vesselIds = vessels.map((v) => v._id.toString());
            const vesselOnlineLookups = await vesselOnlineLookupService.findByVesselIds(vesselIds);

            const lastLocations = await db.lookups
                .collection("last_locations_lookup")
                .find(
                    { vessel_id: { $in: vesselIds.map((id) => new mongoose.Types.ObjectId(id)) } },
                    { projection: { vessel_id: 1, last_underway_at: 1 } },
                )
                .toArray();
            const vesselIdToLastUnderway: Record<string, Date | null> = lastLocations.reduce((acc: Record<string, Date | null>, doc: any) => {
                acc[doc.vessel_id.toString()] = doc.last_underway_at || null;
                return acc;
            }, {});

            // filter provisioned units
            vessels = vessels.filter((vessel) => canAccessVessel(req, vessel));

            // filter by regions
            if (regions && regions.length > 0) {
                vessels = vessels.filter((vessel) => {
                    const stream: IStreamServiceInfo | undefined = streams.find((s) => s.unit_id === vessel.unit_id);
                    if (!stream) return false;
                    return regions.includes(stream.region);
                });
            }

            // filter by region groups
            // if (region_groups && region_groups.length > 0) {
            //     vessels = vessels.filter((vessel) => {
            //         const regionGroup = regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString()));
            //         if (!regionGroup) return false;
            //         return region_groups.includes(regionGroup._id.toString());
            //     });
            // }
            if (region_groups && region_groups.length > 0) {
                vessels = vessels.filter((vessel) => {
                    if (!vessel.region_group_id) return false;
                    return region_groups.includes(vessel.region_group_id.toString());
                });
            }

            // generate response data
            const data: IVesselInfo[] = vessels.map((vessel) => {
                const stream: IStreamServiceInfo | undefined = streams.find((s) => s.unit_id === vessel.unit_id);
                // const regionGroup = regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString()));
                const regionGroup: IRegionGroup | undefined = vessel
                    ? regionGroups.find((rg) => rg._id.toString() === vessel.region_group_id?.toString())
                    : undefined;

                const onlineLookup = vesselOnlineLookups.find((lookup) => lookup.vessel_id.toString() === vessel._id.toString());
                const lastUnderwayAt = vesselIdToLastUnderway[vessel._id.toString()] || null;

                return {
                    vessel_id: vessel._id.toString(),
                    unit_id: vessel.unit_id,
                    name: vessel.name,
                    thumbnail_s3_key: vessel.thumbnail_s3_key,
                    is_active: vessel.is_active,
                    region: stream?.region || null,
                    is_live: stream?.is_live || false,
                    timezone: regionGroup?.timezone || null,
                    // region_group_id: regionGroup?._id.toString() || null,
                    region_group_id: vessel?.region_group_id || null,
                    home_port_location: vessel?.home_port_location || null,
                    last_online_at: onlineLookup?.last_online_at || null,
                    last_underway_at: lastUnderwayAt,
                };
            });

            res.json(data);
        } catch (err) {
            validateError(err, res);
        }
    },
);

export default router;

/**
 * @swagger
 * tags:
 *   name: Vessels
 *   description: Fetch vessel information
 */

/**
 * @swagger
 * /vessels/info:
 *   get:
 *     summary: Fetch list of vessels information
 *     description: Fetches a list of vessels with their respective details including vessel ID, unit ID, name, and other metadata.
 *     tags: [Vessels]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: regions
 *         in: query
 *         required: false
 *         description: Comma-separated AWS regions to fetch vessels for
 *         schema:
 *           type: string
 *           example: ap-southeast-1,us-east-1
 *       - name: region_groups
 *         in: query
 *         required: false
 *         description: Comma-separated region group IDs to fetch vessels for
 *         schema:
 *           type: string
 *           example: 67db2068a64a865006d065f7, 67dc2e586a89af04d143d471, 681c253f9f43051a7748b2c1
 *     responses:
 *       200:
 *         description: An array of vessels information
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   vessel_id:
 *                     type: string
 *                     description: "The unique identifier of the vessel."
 *                     example: "507f1f77bcf86cd799439011"
 *                   unit_id:
 *                     type: string
 *                     description: "The unit identifier of the vessel."
 *                     example: "QSX0000"
 *                   name:
 *                     type: string
 *                     nullable: true
 *                     description: "The name of the unit. Can be null if not provided."
 *                     example: "Vessel Alpha"
 *                   thumbnail_s3_key:
 *                     type: string
 *                     nullable: true
 *                     description: "The S3 key of the vessel's thumbnail image."
 *                     example: "vessels/thumbnail/507f1f77bcf86cd799439011.jpg"
 *                   is_active:
 *                     type: boolean
 *                     description: "Whether the vessel is active."
 *                     example: true
 *                   region:
 *                     type: string
 *                     nullable: true
 *                     description: "AWS region the vessel is listed in."
 *                     example: "ap-southeast-1"
 *                   is_live:
 *                     type: boolean
 *                     description: "Whether the vessel's camera is currently live."
 *                     example: true
 *                   timezone:
 *                     type: string
 *                     nullable: true
 *                     description: "The timezone of the vessel from its region group."
 *                     example: "+08:00"
 *                   region_group_id:
 *                     type: string
 *                     nullable: true
 *                     description: "The ID of the region group this vessel belongs to."
 *                     example: "507f1f77bcf86cd799439011"
 *                   home_port_location:
 *                     type: object
 *                     nullable: true
 *                     description: "The home port location as a GeoJSON Point."
 *                     properties:
 *                       type:
 *                         type: string
 *                         example: "Point"
 *                       coordinates:
 *                         type: array
 *                         items:
 *                           type: number
 *                         example: [120.9842, 14.5995]
 *                   last_online_at:
 *                     type: string
 *                     format: date-time
 *                     nullable: true
 *                     description: "The last time the vessel was online."
 *                     example: "2025-01-15T10:30:00.000Z"
 *                   last_underway_at:
 *                     type: string
 *                     format: date-time
 *                     nullable: true
 *                     description: "The last time the vessel was underway."
 *                     example: "2025-01-15T09:15:00.000Z"
 *       401:
 *         description: Unauthorized, the user must be authenticated.
 *       500:
 *         description: "Internal server error"
 */
