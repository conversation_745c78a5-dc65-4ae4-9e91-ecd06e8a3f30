import { jest } from '@jest/globals';

type AnyObject = Record<string, any>;

function MockListACL(this: AnyObject, data: AnyObject) {
    Object.assign(this, data);
}

const createChainableMock = (mockResolvedValue: any) => {
    const chainable = {
        lean: jest.fn().mockResolvedValue(mockResolvedValue as never),
        select: jest.fn().mockReturnThis(),
        sort: jest.fn().mockReturnThis(),
    };
    chainable.select.mockReturnValue(chainable);
    chainable.sort.mockReturnValue(chainable);
    return chainable;
};

(MockListACL as AnyObject).find = jest.fn().mockReturnValue(createChainableMock([]));
(MockListACL as AnyObject).findOne = jest.fn().mockReturnValue(createChainableMock(null));
(MockListACL as AnyObject).create = jest.fn();
(MockListACL as AnyObject).updateOne = jest.fn();
(MockListACL as AnyObject).deleteOne = jest.fn();
(MockListACL as AnyObject).aggregate = jest.fn();

(MockListACL as AnyObject).prototype = {
    save: jest.fn().mockResolvedValue(true as never),
    toObject: jest.fn().mockReturnValue({}),
};

export default MockListACL as any;

